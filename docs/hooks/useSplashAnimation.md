# useSplashAnimation Hook 文档

## 概述

`useSplashAnimation` 是一个用于管理开屏动画的自定义 React Hook，支持动态加载模式和传统固定时间模式，提供完整的动画状态管理和生命周期控制。

## 文件位置
`hooks/useSplashAnimation.ts`

## 核心特性

### 1. 双模式支持
- **动态加载模式**: 根据实际数据加载进度控制动画
- **固定时间模式**: 传统的基于时间的进度模拟

### 2. 状态管理
- 完整的动画状态跟踪
- 进度监控和回调
- 错误处理和超时保护

### 3. 任务管理
- 支持多个并行加载任务
- 权重分配和进度计算
- 任务完成状态跟踪

## 类型定义

### SplashAnimationPhase 枚举
```typescript
export enum SplashAnimationPhase {
  LOADING = 'loading',
  LOADED = 'loaded',
  HIDDEN = 'hidden'
}
```

### SplashAnimationState 接口
```typescript
export interface SplashAnimationState {
  phase: SplashAnimationPhase;
  progress: number;
  isVisible: boolean;
  hasError: boolean;
}
```

### LoadingTask 接口
```typescript
export interface LoadingTask {
  id: string;
  name: string;
  loader: () => Promise<any>;
  weight: number; // 该任务在数据加载中的权重
}
```

### SplashAnimationConfig 接口
```typescript
export interface SplashAnimationConfig {
  duration?: number;                    // 动画持续时间
  showTime?: number;                    // 显示时间
  progressStep?: number;                // 进度步长
  autoHide?: boolean;                   // 是否自动隐藏
  maxTimeout?: number;                  // 最大超时时间
  loadingTasks?: LoadingTask[];         // 加载任务列表
  animationWeight?: number;             // 动画权重 (0-1)
  dataWeight?: number;                  // 数据加载权重 (0-1)
  minDuration?: number;                 // 最小显示时间
  enableDynamicLoading?: boolean;       // 是否启用动态加载
  onPhaseChange?: (phase: SplashAnimationPhase) => void;
  onProgressChange?: (progress: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  onTimeout?: () => void;
}
```

## 默认配置

```typescript
const DEFAULT_CONFIG: Required<SplashAnimationConfig> = {
  duration: 3000,
  showTime: 1500,
  progressStep: 2,
  autoHide: true,
  maxTimeout: 12000,
  loadingTasks: [],
  animationWeight: 0.3,
  dataWeight: 0.7,
  minDuration: 1500,
  enableDynamicLoading: false,
  onPhaseChange: () => {},
  onProgressChange: () => {},
  onComplete: () => {},
  onError: () => {},
  onTimeout: () => {}
};
```

## 核心功能

### 1. 状态管理
```typescript
const [animationState, setAnimationState] = useState<SplashAnimationState>({
  phase: SplashAnimationPhase.LOADING,
  progress: 0,
  isVisible: true,
  hasError: false
});
```

### 2. 引用管理
```typescript
const isMountedRef = useRef(true);
const callbacksRef = useRef(config);
const timeoutTimerRef = useRef<NodeJS.Timeout | null>(null);
const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
const animationFrameRef = useRef<number | null>(null);
const completedTasksRef = useRef<Set<string>>(new Set());
```

### 3. 动态加载逻辑
```typescript
const startDataLoading = useCallback(() => {
  if (!enableDynamicLoading || loadingTasks.length === 0) return;

  const totalWeight = loadingTasks.reduce((sum, task) => sum + task.weight, 0);
  let completedWeight = 0;

  loadingTasks.forEach(async (task) => {
    try {
      await task.loader();
      completedWeight += task.weight;
      completedTasksRef.current.add(task.id);
      
      // 更新数据加载进度
      const newDataProgress = (completedWeight / totalWeight) * 100;
      setDataProgress(newDataProgress);
      
    } catch (error) {
      console.error(`Loading task ${task.name} failed:`, error);
      callbacksRef.current.onError(error as Error);
    }
  });
}, [enableDynamicLoading, loadingTasks]);
```

### 4. 进度计算
```typescript
// 动态加载模式下的总进度计算
const totalProgress = Math.min(100, 
  animationProgress * animationWeight + dataProgress * dataWeight
);
```

### 5. 完成条件检查
```typescript
const isDataComplete = completedTasksRef.current.size === loadingTasks.length;
const isMinTimeReached = elapsed >= minDuration;

if ((isDataComplete && isMinTimeReached) || elapsed >= maxTimeout) {
  if (autoHide) {
    setTimeout(() => {
      if (isMountedRef.current) {
        hide();
      }
    }, showTime);
  }
}
```

## 返回值

Hook 返回一个包含以下属性的对象：

```typescript
return {
  // 状态属性
  phase: animationState.phase,
  progress: animationState.progress,
  isVisible: animationState.isVisible,
  isHidden: animationState.phase === SplashAnimationPhase.HIDDEN,
  hasError: animationState.hasError,
  
  // 控制方法
  hide,
  
  // 加载状态（动态模式）
  loadingStatus: enableDynamicLoading ? {
    completedTasks: Array.from(completedTasksRef.current),
    totalTasks: loadingTasks.length,
    dataProgress
  } : undefined
};
```

## 使用示例

### 1. 基础用法（固定时间模式）
```typescript
const splashAnimation = useSplashAnimation({
  duration: 3000,
  showTime: 1500,
  autoHide: true,
  onComplete: () => {
    console.log('Animation completed');
    setAppReady(true);
  }
});

if (!splashAnimation.isHidden) {
  return <SplashScreen progress={splashAnimation.progress} />;
}
```

### 2. 动态加载模式
```typescript
const loadingTasks: LoadingTask[] = [
  {
    id: 'pokemon-data',
    name: '加载宝可梦数据',
    loader: loadPokemonData,
    weight: 3
  },
  {
    id: 'user-settings',
    name: '加载用户设置',
    loader: loadUserSettings,
    weight: 1
  },
  {
    id: 'app-config',
    name: '加载应用配置',
    loader: loadAppConfig,
    weight: 2
  }
];

const splashAnimation = useSplashAnimation({
  enableDynamicLoading: true,
  loadingTasks,
  minDuration: 1500,
  maxTimeout: 8000,
  animationWeight: 0.3,
  dataWeight: 0.7,
  onProgressChange: (progress) => {
    console.log(`Loading progress: ${progress.toFixed(1)}%`);
  },
  onComplete: () => {
    setAppReady(true);
  }
});
```

### 3. 在 App 组件中的完整使用
```typescript
const App: React.FC = () => {
  const [isAppReady, setIsAppReady] = useState(false);

  const loadingTasks: LoadingTask[] = [
    // 定义加载任务
  ];

  const splashAnimation = useSplashAnimation({
    enableDynamicLoading: true,
    loadingTasks,
    minDuration: 1500,
    maxTimeout: 8000,
    onComplete: () => setIsAppReady(true),
    onError: (error) => {
      console.error('Splash animation error:', error);
      setIsAppReady(true); // 出错时也要显示主应用
    }
  });

  if (!splashAnimation.isHidden && !isAppReady) {
    return (
      <SplashScreen
        progress={splashAnimation.progress}
        phase={splashAnimation.phase}
        loadingTasks={loadingTasks}
        loadingStatus={splashAnimation.loadingStatus}
      />
    );
  }

  return <MainApp />;
};
```

## 性能优化

### 1. 内存管理
- 使用 `useRef` 避免不必要的重新渲染
- 组件卸载时清理所有定时器和动画帧
- 使用 `isMountedRef` 防止内存泄漏

### 2. 动画优化
- 使用 `requestAnimationFrame` 进行平滑动画
- 避免频繁的状态更新
- 合理的进度更新间隔

### 3. 错误处理
- 完善的 try-catch 错误捕获
- 超时保护机制
- 优雅的错误恢复

## 最佳实践

### 1. 任务权重分配
```typescript
const loadingTasks: LoadingTask[] = [
  { id: 'critical-data', weight: 5, loader: loadCriticalData },
  { id: 'optional-data', weight: 2, loader: loadOptionalData },
  { id: 'cache-data', weight: 1, loader: loadCacheData }
];
```

### 2. 进度权重配置
```typescript
// 数据加载为主的应用
{ animationWeight: 0.2, dataWeight: 0.8 }

// 动画效果为主的应用
{ animationWeight: 0.6, dataWeight: 0.4 }
```

### 3. 超时和最小时间设置
```typescript
{
  minDuration: 1500,    // 确保用户能看到品牌信息
  maxTimeout: 10000,    // 防止无限等待
  showTime: 800         // 完成后的展示时间
}
```

## 注意事项

1. **内存泄漏**: 确保在组件卸载时清理所有定时器
2. **错误处理**: 加载任务失败时的优雅降级
3. **性能影响**: 避免过于频繁的进度更新
4. **用户体验**: 合理设置最小显示时间和超时时间

## 相关文件

- `types/splash.ts`: 开屏动画相关类型定义
- `components/SplashScreen.tsx`: 开屏动画组件
- `App.tsx`: Hook 的使用示例
