# Electron数据库操作总结

## 概述

本文档总结了Pokedex Electron应用的数据库操作机制，重点分析数据库架构、级联删除机制和服务层操作。

## 数据库架构

### 技术选型
- **数据库**: SQLite 3 (本地文件数据库)
- **驱动**: better-sqlite3 (高性能Node.js驱动)
- **模式**: WAL模式 (提升并发性能)
- **外键约束**: 已启用 (确保数据完整性)

### 数据库优化配置
- 启用外键约束防止数据不一致
- 使用WAL模式提升读写并发性能
- 优化缓存大小提升查询速度
- 设置合适的同步模式平衡性能和安全

## 数据库表结构

### 核心表设计

#### 1. categories表 (分类管理)
- **主键**: UUID格式，避免冲突
- **核心字段**: 分类名称、描述、缩略图路径
- **特色**: 支持本地路径和URL双模式缩略图

#### 2. images表 (图片管理)
- **主键**: UUID格式
- **外键**: 关联categories表，设置级联删除
- **核心字段**: 原始文件名、存储文件名、MIME类型、文件大小
- **特色**: 支持JSON格式的文件元数据和EXIF信息存储

#### 3. tags表 (标签管理)
- **主键**: UUID格式
- **唯一约束**: 标签名称不能重复
- **设计理念**: 简洁结构，专注核心功能

#### 4. image_tags表 (关联表)
- **复合主键**: (image_id, tag_id)
- **双外键**: 分别关联images和tags表
- **关系**: 实现图片和标签的多对多关联

### 性能优化
- **分类查询索引**: 快速查找分类下的图片
- **时间排序索引**: 支持按创建时间排序
- **标签搜索索引**: 提升标签名称搜索性能
- **关联查询索引**: 加速图片-标签关联查询

## 级联删除机制

### 设计原理
项目实现了完整的级联删除机制，确保删除操作的数据一致性：

#### 数据库层级联删除
- 通过SQLite外键约束的 `ON DELETE CASCADE` 实现
- 删除父记录时自动删除所有子记录
- 无需手动编写删除关联数据的代码

#### 应用层级联删除
- **删除分类**: 先删除图片文件 → 删除分类记录 → 自动级联删除图片和关联
- **删除图片**: 删除存储文件 → 删除图片记录 → 自动级联删除标签关联
- **删除标签**: 删除标签记录 → 自动级联删除图片关联

### 级联删除流程
```
删除分类 → 删除图片文件 → 删除分类记录 → 自动级联删除(images + image_tags)
删除图片 → 删除图片文件 → 删除图片记录 → 自动级联删除(image_tags)
删除标签 → 删除标签记录 → 自动级联删除(image_tags)
```

### 安全保障
- **事务保护**: 关键删除操作使用数据库事务
- **错误恢复**: 删除失败时提供详细错误信息
- **审计日志**: 记录删除操作用于追踪

## 服务层数据库操作

### DatabaseManager核心功能
- **连接管理**: 自动配置数据库路径，支持自定义路径
- **数据库迁移**: 支持版本升级和数据迁移
- **备份恢复**: 提供完整的数据库备份和恢复功能
- **重置功能**: 支持清空数据库并可选择保留默认数据
- **统计监控**: 提供数据库连接测试和统计信息

### CategoryService分类管理
#### 级联删除特点
- **双层保护**: 先删除图片文件，再删除数据库记录
- **事务安全**: 使用数据库事务确保操作原子性
- **详细报告**: 返回删除成功/失败的详细统计信息
- **错误处理**: 图片删除失败不阻止分类删除

#### 主要功能
- 分类CRUD操作（创建、读取、更新、删除）
- 分类列表查询和分页
- 分类与图片的关联关系处理
- 缩略图管理

### ImageService图片管理
#### 删除机制特点
- **存储文件删除**: 支持本地和OSS双模式文件删除
- **批量处理**: 支持批量删除，分批处理提升性能
- **标签关联清理**: 依赖外键约束自动删除图片-标签关联
- **审计日志**: 记录详细的删除操作日志

#### 主要功能
- 图片上传和元数据存储
- 图片查询、过滤和分页
- 缩略图生成和管理
- 文件存储路径解析（本地/OSS）

### TagService标签管理
#### 级联删除特点
- **简洁高效**: 单条SQL语句完成级联删除
- **不影响图片**: 只删除标签和关联关系，图片保持不变
- **自动清理**: 依赖外键约束自动清理image_tags关联表

#### 主要功能
- 标签CRUD操作
- 智能标签搜索（精确匹配、前缀匹配、模糊匹配）
- 图片-标签关联管理（添加/移除关联）
- 标签使用统计

## 数据库操作最佳实践

### 1. 事务管理
- **关键操作使用事务**: 删除、批量操作等重要操作
- **原子性保证**: 要么全部成功，要么全部失败
- **错误回滚**: 操作失败时自动回滚到原始状态

### 2. 外键约束
- **启用外键约束**: 通过 `PRAGMA foreign_keys = ON` 启用
- **级联删除**: 使用 `ON DELETE CASCADE` 实现自动级联
- **数据完整性**: 防止孤立记录和数据不一致

### 3. 性能优化
- **索引策略**: 为常用查询字段创建合适的索引
- **WAL模式**: 提升并发读写性能
- **缓存优化**: 合理设置缓存大小提升查询速度

### 4. 错误处理
- **详细错误信息**: 提供具体的错误描述和上下文
- **操作审计**: 记录关键操作的详细日志
- **优雅降级**: 部分失败不影响整体功能

### 5. 数据验证
- **输入验证**: 使用Zod Schema验证输入数据
- **存在性检查**: 操作前验证记录是否存在
- **约束检查**: 防止违反业务规则和数据约束

## 总结

Pokedex项目的数据库操作具有以下特点：

1. **完整的级联删除**: 通过外键约束和应用层逻辑实现完整的级联删除机制
2. **双存储支持**: 数据库设计支持本地和OSS双存储模式
3. **高性能设计**: 通过索引优化、WAL模式等提升数据库性能
4. **安全可靠**: 事务保护、错误处理、数据验证确保操作安全
5. **易于维护**: 清晰的表结构设计和服务层封装便于维护和扩展

## ImageService删除机制和标签级联

### 图片删除的完整流程

ImageService实现了复杂的图片删除逻辑，支持本地和OSS双存储模式：

```typescript
async deleteImage(imageId: string): Promise<DeleteResult> {
  const db = this.dbManager.getDatabase() as Database.Database;
  const storageType = this.getStorageType();

  // 1. 验证删除条件
  const validation = await this.validateDeleteConditions(imageId);
  if (!validation.valid) {
    return {
      success: false, imageId, error: `删除验证失败: ${validation.errors.join('; ')}`,
      storageDeleted: false, databaseDeleted: false, details: { storageType }
    };
  }

  // 2. 获取图片信息
  const image = db.prepare('SELECT * FROM images WHERE id = ?').get(imageId) as any;
  if (!image) {
    return {
      success: false, imageId, error: '图片不存在',
      storageDeleted: false, databaseDeleted: false, details: { storageType }
    };
  }

  let storageDeleted = false;
  let databaseDeleted = false;
  let storageError = '';
  let databaseError = '';

  // 3. 删除存储文件
  if (image.stored_filename) {
    try {
      if (storageType === 'oss') {
        // OSS存储模式
        if (this.ossService.isConfigured()) {
          // 删除原图
          const imageOSSPath = this.generateOSSPath(image.category_id, image.stored_filename, false);
          await this.ossService.deleteFile(imageOSSPath);

          // 删除缩略图
          const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
          const thumbnailOSSPath = this.generateOSSPath(image.category_id, thumbnailFilename, true);
          await this.ossService.deleteFile(thumbnailOSSPath);

          storageDeleted = true;
        } else {
          storageError = 'OSS未配置，无法删除存储文件';
        }
      } else {
        // 本地存储模式
        const imagePath = this.getImagePath(image.stored_filename);
        const thumbnailPath = this.getThumbnailPath(image.stored_filename);

        // 删除原图
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }

        // 删除缩略图
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath);
        }

        storageDeleted = true;
      }
    } catch (error) {
      storageError = `存储文件删除失败: ${error.message}`;
    }
  } else {
    storageDeleted = true; // 没有文件需要删除
  }

  // 4. 删除数据库记录（自动级联删除image_tags关联）
  try {
    const deleteImageTransaction = db.transaction(() => {
      const deleteImage = db.prepare('DELETE FROM images WHERE id = ?');
      const result = deleteImage.run(imageId);

      if (result.changes === 0) {
        throw new Error('数据库中没有找到图片记录');
      }
    });

    deleteImageTransaction();
    databaseDeleted = true;
  } catch (error) {
    databaseError = `数据库删除失败: ${error.message}`;
  }

  // 5. 生成删除结果
  const success = databaseDeleted && (storageDeleted || !image.stored_filename);
  const combinedError = [storageError, databaseError].filter(Boolean).join('; ');

  const result: DeleteResult = {
    success, imageId, error: combinedError || undefined,
    storageDeleted, databaseDeleted,
    details: { imagePath, thumbnailPath, storageType }
  };

  // 6. 记录审计日志
  if (success) {
    this.logDeleteAudit(imageId, 'DELETE_SUCCESS', {
      imageTitle: image.title, storageDeleted, databaseDeleted, details: result.details
    });
  } else {
    this.logDeleteAudit(imageId, 'DELETE_FAILED', {
      imageTitle: image.title, error: combinedError, storageDeleted, databaseDeleted, details: result.details
    });
  }

  return result;
}
```

### 批量删除机制

```typescript
async deleteImages(imageIds: string[]): Promise<BatchDeleteResult> {
  if (imageIds.length === 0) {
    return { totalCount: 0, successCount: 0, failedCount: 0, results: [], errors: [] };
  }

  const results: DeleteResult[] = [];
  const errors: string[] = [];

  // 1. 验证所有图片ID
  const validImageIds = await this.validateImageIds(imageIds);

  // 2. 按存储类型分组处理
  const storageType = this.getStorageType();

  if (storageType === 'oss') {
    await this.batchDeleteFromOSS(validImageIds, results, errors);
  } else {
    await this.batchDeleteFromLocal(validImageIds, results, errors);
  }

  // 3. 批量删除数据库记录
  await this.batchDeleteFromDatabase(validImageIds, results, errors);

  // 4. 统计最终结果
  let successCount = 0;
  let failedCount = 0;
  for (const result of results) {
    if (result.success) successCount++;
    else failedCount++;
  }

  return {
    totalCount: imageIds.length,
    successCount,
    failedCount,
    results,
    errors
  };
}
```

### 数据库批量删除事务

```typescript
private async batchDeleteFromDatabase(imageIds: string[], results: DeleteResult[], errors: string[]): Promise<void> {
  const db = this.dbManager.getDatabase() as Database.Database;

  try {
    const batchDeleteTransaction = db.transaction(() => {
      // 批量删除图片记录（自动级联删除image_tags关联）
      const deleteImages = db.prepare('DELETE FROM images WHERE id = ?');

      for (const imageId of imageIds) {
        const result = deleteImages.run(imageId);

        // 更新结果状态
        let deleteResult = results.find(r => r.imageId === imageId);
        if (!deleteResult) {
          deleteResult = {
            success: false, imageId, storageDeleted: false, databaseDeleted: false,
            details: { storageType: this.getStorageType() }
          };
          results.push(deleteResult);
        }

        deleteResult.databaseDeleted = result.changes > 0;
        deleteResult.success = deleteResult.storageDeleted && deleteResult.databaseDeleted;
      }
    });

    batchDeleteTransaction();
  } catch (error) {
    const errorMsg = `数据库批量删除事务失败: ${error.message}`;
    errors.push(errorMsg);

    // 标记所有图片的数据库删除失败
    for (const imageId of imageIds) {
      let result = results.find(r => r.imageId === imageId);
      if (!result) {
        result = {
          success: false, imageId, storageDeleted: false, databaseDeleted: false,
          details: { storageType: this.getStorageType() }
        };
        results.push(result);
      }

      result.databaseDeleted = false;
      result.success = false;
      result.error = result.error ? `${result.error}; ${errorMsg}` : errorMsg;
    }
  }
}

## TagService级联删除和关联管理

### 标签删除的级联机制

TagService实现了简洁但有效的级联删除逻辑：

```typescript
async deleteTag(tagId: string): Promise<void> {
  const db = this.dbManager.getDatabase() as Database.Database;

  // 1. 验证标签是否存在
  const tag = db.prepare('SELECT * FROM tags WHERE id = ?').get(tagId) as TagRead | undefined;
  if (!tag) {
    throw new Error(`标签不存在: ${tagId}`);
  }

  // 2. 删除标签（外键约束会自动删除image_tags关联）
  db.prepare('DELETE FROM tags WHERE id = ?').run(tagId);
}
```

**级联删除特点**：
- **自动级联**：依赖数据库外键约束 `ON DELETE CASCADE`
- **不影响图片**：只删除标签和关联关系，图片本身保持不变
- **简洁高效**：单条SQL语句完成级联删除

### 标签CRUD操作

#### 创建标签（防重复）
```typescript
async createTag(tagData: TagCreate): Promise<TagRead> {
  const db = this.dbManager.getDatabase() as Database.Database;

  // 检查标签是否已存在（不区分大小写）
  const existingTag = db.prepare('SELECT * FROM tags WHERE LOWER(name) = LOWER(?)')
    .get(tagData.name) as TagRead | undefined;

  if (existingTag) {
    return existingTag; // 返回已存在的标签
  }

  const id = uuidv4();
  const now = new Date().toISOString();

  const tag: TagRead = {
    id, name: tagData.name, created_at: now, updated_at: now
  };

  db.prepare('INSERT INTO tags (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)')
    .run(id, tagData.name, now, now);

  return tag;
}
```

#### 标签搜索（智能排序）
```typescript
async searchTags(query: string): Promise<TagRead[]> {
  const db = this.dbManager.getDatabase() as Database.Database;
  const normalizedQuery = query.toLowerCase().trim();

  if (!normalizedQuery) {
    return this.getAllTags();
  }

  // 使用SQL进行搜索和智能排序
  const sql = `
    SELECT *,
      CASE
        WHEN LOWER(name) = ? THEN 0          -- 精确匹配优先级最高
        WHEN LOWER(name) LIKE ? || '%' THEN 1 -- 前缀匹配优先级次之
        ELSE 2                               -- 包含匹配优先级最低
      END as match_score
    FROM tags
    WHERE LOWER(name) LIKE '%' || ? || '%'
    ORDER BY match_score, name
  `;

  const tags = db.prepare(sql).all(normalizedQuery, normalizedQuery, normalizedQuery) as TagRead[];
  return tags;
}
```

### 图片-标签关联管理

#### 添加标签到图片
```typescript
async addTagToImage(imageId: string, tagId: string): Promise<void> {
  const db = this.dbManager.getDatabase() as Database.Database;

  // 验证图片存在
  const imageExists = db.prepare('SELECT id FROM images WHERE id = ?').get(imageId);
  if (!imageExists) {
    throw new Error(`图片不存在: ${imageId}`);
  }

  // 验证标签存在
  const tagExists = db.prepare('SELECT * FROM tags WHERE id = ?').get(tagId) as TagRead | undefined;
  if (!tagExists) {
    throw new Error(`标签不存在: ${tagId}`);
  }

  // 检查关联是否已存在
  const existingRelation = db.prepare('SELECT * FROM image_tags WHERE image_id = ? AND tag_id = ?')
    .get(imageId, tagId);

  if (existingRelation) {
    return; // 关联已存在，直接返回
  }

  // 创建新的关联
  db.prepare('INSERT INTO image_tags (image_id, tag_id) VALUES (?, ?)')
    .run(imageId, tagId);
}
```

#### 从图片移除标签
```typescript
async removeTagFromImage(imageId: string, tagId: string): Promise<void> {
  const db = this.dbManager.getDatabase() as Database.Database;

  // 删除关联关系
  const result = db.prepare('DELETE FROM image_tags WHERE image_id = ? AND tag_id = ?')
    .run(imageId, tagId);

  if (result.changes === 0) {
    throw new Error(`图片和标签之间不存在关联关系: ${imageId} - ${tagId}`);
  }
}
```

#### 获取图片的所有标签
```typescript
async getImageTags(imageId: string): Promise<TagRead[]> {
  const db = this.dbManager.getDatabase() as Database.Database;

  const query = `
    SELECT t.*
    FROM tags t
    JOIN image_tags it ON t.id = it.tag_id
    WHERE it.image_id = ?
    ORDER BY t.name
  `;

  const tags = db.prepare(query).all(imageId) as TagRead[];
  return tags;
}
```

#### 根据标签搜索图片
```typescript
async searchImagesByTags(tagNames: string[]): Promise<string[]> {
  const db = this.dbManager.getDatabase() as Database.Database;

  if (tagNames.length === 0) {
    return [];
  }

  // 构建查询条件
  const placeholders = tagNames.map(() => '?').join(',');
  const query = `
    SELECT DISTINCT it.image_id
    FROM image_tags it
    JOIN tags t ON it.tag_id = t.id
    WHERE LOWER(t.name) IN (${placeholders})
  `;

  const normalizedNames = tagNames.map(name => name.toLowerCase());
  const rows = db.prepare(query).all(...normalizedNames) as { image_id: string }[];
  const imageIds = rows.map(row => row.image_id);

  return imageIds;
}
```

## 数据库操作最佳实践

### 1. 事务管理
- **关键操作使用事务**：删除、批量操作
- **原子性保证**：要么全部成功，要么全部失败
- **错误回滚**：操作失败时自动回滚

### 2. 外键约束
- **启用外键约束**：`PRAGMA foreign_keys = ON`
- **级联删除**：`ON DELETE CASCADE`
- **数据完整性**：防止孤立记录

### 3. 性能优化
- **索引策略**：为常用查询字段创建索引
- **WAL模式**：提升并发读写性能
- **缓存优化**：合理设置缓存大小

### 4. 错误处理
- **详细错误信息**：提供具体的错误描述
- **操作审计**：记录关键操作的日志
- **优雅降级**：部分失败不影响整体功能

### 5. 数据验证
- **输入验证**：使用Zod Schema验证
- **存在性检查**：操作前验证记录存在
- **约束检查**：防止违反业务规则
```
```
