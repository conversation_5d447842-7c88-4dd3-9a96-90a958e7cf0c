# category Schema 文档

## 概述

`category.ts` 定义了分类相关的 Zod Schema 和 TypeScript 类型，提供运行时数据验证和编译时类型安全。

## 文件位置
`schemas/category.ts`

## 核心 Schema 定义

### 1. 基础 Schema

#### FlexibleIdSchema
```typescript
const FlexibleIdSchema = z.string().min(1, 'ID不能为空');
```
- 兼容本地和远程的 ID 格式
- 可以是 UUID 或简单字符串
- 最小长度为 1

#### BaseCategorySchema
```typescript
const BaseCategorySchema = z.object({
  name: NonEmptyStringSchema,
  description: OptionalStringSchema,
});
```
- 分类的基础字段定义
- `name`: 必填的非空字符串
- `description`: 可选的字符串

### 2. 分类操作 Schema

#### CategoryCreateSchema
```typescript
export const CategoryCreateSchema = BaseCategorySchema.extend({
  // 创建时只需要 name 和 description
});
```
- 用于创建分类的数据验证
- 继承基础分类字段
- 不包含系统生成的字段（id, 时间戳等）

#### CategoryReadSchema
```typescript
export const CategoryReadSchema = BaseCategorySchema.extend({
  id: FlexibleIdSchema,
  created_at: DateTimeSchema,
  updated_at: DateTimeSchema,
  thumbnail_path: OptionalStringSchema,
  thumbnail_url: OptionalStringSchema,
});
```
- 用于读取分类数据的验证
- 包含所有分类字段
- 包含系统生成的元数据

#### CategoryUpdateSchema
```typescript
export const CategoryUpdateSchema = BaseCategorySchema.partial().extend({
  thumbnail_path: OptionalStringSchema,
  thumbnail_url: OptionalStringSchema,
});
```
- 用于更新分类的数据验证
- 所有字段都是可选的（partial）
- 支持缩略图字段更新

### 3. 关联数据 Schema

#### ImageForCategorySchema
```typescript
const ImageForCategorySchema = z.object({
  id: FlexibleIdSchema,
  category_id: FlexibleIdSchema,
  title: OptionalStringSchema,
  original_filename: OptionalStringSchema,
  stored_filename: OptionalStringSchema,
  relative_file_path: OptionalStringSchema,
  relative_thumbnail_path: OptionalStringSchema,
  mime_type: OptionalStringSchema,
  size_bytes: z.number().int().nonnegative().nullable(),
  description: OptionalStringSchema,
  created_at: DateTimeSchema,
  updated_at: OptionalStringSchema,
  file_metadata: RecordSchema,
  image_url: z.string(),
  thumbnail_url: OptionalStringSchema,
  tags: z.array(z.object({
    id: FlexibleIdSchema,
    name: z.string(),
    created_at: DateTimeSchema,
    updated_at: DateTimeSchema,
  })).optional(),
  exif_info: z.object({
    make: z.string().nullable(),
    model: z.string().nullable(),
    lens_make: z.string().nullable(),
    bits_per_sample: z.string().nullable(),
    date_time_original: z.string().nullable(),
    exposure_time: z.string().nullable(),
    f_number: z.string().nullable(),
    exposure_program: z.string().nullable(),
    iso_speed_rating: z.string().nullable(),
    focal_length: z.string().nullable(),
    lens_specification: z.string().nullable(),
    lens_model: z.string().nullable(),
    exposure_mode: z.string().nullable(),
    cfa_pattern: z.string().nullable(),
    color_space: z.string().nullable(),
    white_balance: z.string().nullable(),
  }).nullable()
});
```

#### 设计考虑
- **避免循环引用**: 简化的图片 Schema，用于分类详情
- **EXIF 数据**: 完整的相机元数据支持
- **标签关联**: 支持图片标签的嵌套结构
- **文件信息**: 包含文件路径、大小等元数据

### 4. 复合 Schema

#### CategoryReadWithImagesSchema
```typescript
export const CategoryReadWithImagesSchema = CategoryReadSchema.extend({
  images: z.array(ImageForCategorySchema).optional(),
});
```
- 包含图片列表的分类数据
- 用于分类详情页面
- 图片数组是可选的

#### CategoryListResponseSchema
```typescript
export const CategoryListResponseSchema = z.array(CategoryReadSchema);
```
- 分类列表的响应格式
- 简单的分类数组，不包含图片

## TypeScript 类型定义

### 导出类型
```typescript
export type CategoryCreate = z.infer<typeof CategoryCreateSchema>;
export type CategoryRead = z.infer<typeof CategoryReadSchema>;
export type CategoryUpdate = z.infer<typeof CategoryUpdateSchema>;
export type CategoryReadWithImages = z.infer<typeof CategoryReadWithImagesSchema>;
export type CategoryListResponse = z.infer<typeof CategoryListResponseSchema>;
```

### 类型示例

#### CategoryCreate
```typescript
interface CategoryCreate {
  name: string;
  description?: string;
}
```

#### CategoryRead
```typescript
interface CategoryRead {
  id: string;
  name: string;
  description?: string;
  thumbnail_path?: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
}
```

#### CategoryReadWithImages
```typescript
interface CategoryReadWithImages extends CategoryRead {
  images?: Array<{
    id: string;
    category_id: string;
    title?: string;
    // ... 其他图片字段
    tags?: Array<{
      id: string;
      name: string;
      created_at: string;
      updated_at: string;
    }>;
    exif_info?: {
      make?: string;
      model?: string;
      // ... 其他 EXIF 字段
    };
  }>;
}
```

## 验证函数

### 分类创建验证
```typescript
export function validateCategoryCreate(data: unknown): CategoryCreate {
  return CategoryCreateSchema.parse(data);
}
```

### 分类读取验证
```typescript
export function validateCategoryRead(data: unknown): CategoryRead {
  return CategoryReadSchema.parse(data);
}
```

### 分类更新验证
```typescript
export function validateCategoryUpdate(data: unknown): CategoryUpdate {
  return CategoryUpdateSchema.parse(data);
}
```

### 带图片分类验证
```typescript
export function validateCategoryReadWithImages(data: unknown): CategoryReadWithImages {
  return CategoryReadWithImagesSchema.parse(data);
}
```

## 使用示例

### 1. API 响应验证
```typescript
import { validateCategoryRead, validateCategoryListResponse } from '../schemas/category';

// 验证单个分类
const category = validateCategoryRead(apiResponse);

// 验证分类列表
const categories = validateCategoryListResponse(apiResponse);
```

### 2. 表单数据验证
```typescript
import { validateCategoryCreate } from '../schemas/category';

const handleSubmit = (formData: unknown) => {
  try {
    const validData = validateCategoryCreate(formData);
    // 提交有效数据
    await createCategory(validData);
  } catch (error) {
    // 处理验证错误
    console.error('表单数据无效:', error.message);
  }
};
```

### 3. 类型守卫
```typescript
import { CategoryReadSchema } from '../schemas/category';

const isCategoryRead = (data: unknown): data is CategoryRead => {
  return CategoryReadSchema.safeParse(data).success;
};
```

## 错误处理

### 验证错误类型
```typescript
import { ZodError } from 'zod';

try {
  const category = validateCategoryCreate(invalidData);
} catch (error) {
  if (error instanceof ZodError) {
    // 处理 Zod 验证错误
    error.errors.forEach(err => {
      console.log(`字段 ${err.path.join('.')} 错误: ${err.message}`);
    });
  }
}
```

### 安全解析
```typescript
const result = CategoryReadSchema.safeParse(data);
if (result.success) {
  // 使用验证通过的数据
  const category = result.data;
} else {
  // 处理验证失败
  console.error('验证失败:', result.error);
}
```

## 最佳实践

### 1. 数据验证
- 在 API 边界处进行数据验证
- 使用 `safeParse` 进行非抛出式验证
- 提供有意义的错误消息

### 2. 类型安全
- 从 Schema 推导类型，确保一致性
- 使用类型守卫进行运行时检查
- 避免手动定义重复的类型

### 3. 性能优化
- 缓存编译后的 Schema
- 在开发环境中启用详细错误信息
- 在生产环境中优化错误处理

### 4. 维护性
- 保持 Schema 与数据库结构同步
- 使用组合而非继承构建复杂 Schema
- 定期审查和更新验证规则

## 相关文件

- `schemas/common.ts`: 通用 Schema 定义
- `schemas/image.ts`: 图片相关 Schema
- `schemas/tag.ts`: 标签相关 Schema
- `services/api-validation.ts`: API 验证服务

## 注意事项

1. **循环引用**: 避免 Schema 之间的循环依赖
2. **性能影响**: 复杂 Schema 的验证可能影响性能
3. **版本兼容**: Schema 变更需要考虑向后兼容性
4. **错误信息**: 提供用户友好的验证错误信息
