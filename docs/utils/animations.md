# animations 工具函数文档

## 概述

`animations.ts` 是统一的动画配置文件，提供可复用的 Framer Motion 动画变体和配置，支持性能优化和用户偏好设置。

## 文件位置
`utils/animations.ts`

## 核心功能

### 1. 动画时长配置
```typescript
export const ANIMATION_DURATIONS = {
  fast: 0.15,
  normal: 0.3,
  slow: 0.5,
  verySlow: 0.8,
} as const;
```

### 2. 缓动函数配置
```typescript
export const EASING = {
  easeOut: [0.0, 0.0, 0.2, 1],
  easeIn: [0.4, 0.0, 1, 1],
  easeInOut: [0.4, 0.0, 0.2, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  spring: { type: 'spring', stiffness: 300, damping: 25 },
} as const;
```

## 动画变体

### 1. 卡片动画变体 (cardVariants)
```typescript
export const cardVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: ANIMATION_DURATIONS.normal,
      ease: EASING.easeOut,
    },
  },
  hover: {
    y: -4,
    scale: 1.02,
    transition: {
      duration: ANIMATION_DURATIONS.fast,
      ease: EASING.easeOut,
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: ANIMATION_DURATIONS.fast,
      ease: EASING.easeOut,
    },
  },
};
```

#### 使用场景
- CategoryCard 组件
- ImageCard 组件
- 其他卡片类组件

### 2. 图片动画变体 (imageVariants)
```typescript
export const imageVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 1.1,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: ANIMATION_DURATIONS.normal,
      ease: EASING.easeOut,
    },
  },
  hover: {
    scale: 1.05,
    transition: {
      duration: ANIMATION_DURATIONS.normal,
      ease: EASING.easeOut,
    },
  },
};
```

#### 使用场景
- 图片加载动画
- 图片悬停效果
- 图片查看器

### 3. 列表动画变体 (listVariants)
```typescript
export const listVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};
```

### 4. 交错动画变体
```typescript
export const staggerContainerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

export const staggerItemVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: ANIMATION_DURATIONS.normal,
      ease: EASING.easeOut,
    },
  },
};
```

## 性能优化功能

### 1. 用户偏好检测
```typescript
export const shouldReduceMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};
```

#### 功能
- 检测用户的动画偏好设置
- 支持无障碍访问
- 服务端渲染兼容

### 2. 动画配置优化
```typescript
export const getAnimationConfig = (variants: Variants): Variants => {
  if (shouldReduceMotion()) {
    const reducedVariants: Variants = {};
    Object.keys(variants).forEach(key => {
      reducedVariants[key] = {
        ...variants[key],
        transition: { duration: 0.01 },
      };
    });
    return reducedVariants;
  }
  return variants;
};
```

#### 功能
- 根据用户偏好调整动画
- 减少动画时长到最小值
- 保持动画结构不变

### 3. 设备性能检测
```typescript
export const getPerformanceOptimizedConfig = (variants: Variants): Variants => {
  if (typeof window === 'undefined') return variants;
  
  const isLowEndDevice = () => {
    const cores = navigator.hardwareConcurrency || 4;
    const memory = (navigator as any).deviceMemory || 4;
    const connection = (navigator as any).connection;
    const isSlowConnection = connection && 
      (connection.effectiveType === 'slow-2g' || 
       connection.effectiveType === '2g' || 
       connection.saveData);
    
    return cores <= 2 || memory <= 2 || isSlowConnection;
  };
  
  if (shouldReduceMotion()) {
    return getAnimationConfig(variants);
  }
  
  if (isLowEndDevice()) {
    // 为低端设备优化动画
    const optimizedVariants: Variants = {};
    Object.keys(variants).forEach(key => {
      const variant = variants[key];
      if (typeof variant === 'object' && variant.transition) {
        const transition = variant.transition as any;
        optimizedVariants[key] = {
          ...variant,
          transition: {
            ...transition,
            duration: Math.min(transition.duration || ANIMATION_DURATIONS.normal, ANIMATION_DURATIONS.fast),
          },
        };
      } else {
        optimizedVariants[key] = variant;
      }
    });
    return optimizedVariants;
  }
  
  return variants;
};
```

#### 检测指标
- **CPU 核心数**: `navigator.hardwareConcurrency`
- **内存大小**: `navigator.deviceMemory`
- **网络连接**: `navigator.connection`

## 预设配置

### 1. 性能级别
```typescript
export const PERFORMANCE_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium', 
  LOW: 'low',
} as const;

type PerformanceLevel = typeof PERFORMANCE_LEVELS[keyof typeof PERFORMANCE_LEVELS];
```

### 2. 级别配置映射
```typescript
export const getConfigForPerformanceLevel = (
  level: PerformanceLevel,
  baseVariants: Variants
): Variants => {
  switch (level) {
    case PERFORMANCE_LEVELS.HIGH:
      return baseVariants;
    case PERFORMANCE_LEVELS.MEDIUM:
      return getAnimationConfig(baseVariants);
    case PERFORMANCE_LEVELS.LOW:
      return getAnimationConfig(baseVariants);
    default:
      return baseVariants;
  }
};
```

## 高阶组件

### 性能优化包装器
```typescript
export const withPerformanceOptimization = <T extends React.ComponentType<any>>(
  Component: T,
  animationProps?: {
    enableGPUAcceleration?: boolean;
    performanceLevel?: PerformanceLevel;
  }
) => {
  return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => {
    const optimizedProps = {
      ...props,
      style: {
        ...props.style,
        ...(animationProps?.enableGPUAcceleration && {
          willChange: 'transform, opacity',
          transform: 'translateZ(0)',
        }),
      },
    };
    
    return React.createElement(Component, { ...optimizedProps, ref });
  });
};
```

#### 功能
- GPU 加速优化
- 性能级别配置
- 样式属性注入

## 使用示例

### 1. 基础动画使用
```tsx
import { motion } from 'framer-motion';
import { cardVariants, getAnimationConfig } from '../utils/animations';

const MyCard = () => (
  <motion.div
    variants={getAnimationConfig(cardVariants)}
    initial="hidden"
    animate="visible"
    whileHover="hover"
    whileTap="tap"
  >
    Card Content
  </motion.div>
);
```

### 2. 交错动画使用
```tsx
import { staggerContainerVariants, staggerItemVariants } from '../utils/animations';

const MyList = ({ items }) => (
  <motion.div
    variants={getAnimationConfig(staggerContainerVariants)}
    initial="hidden"
    animate="visible"
  >
    {items.map(item => (
      <motion.div
        key={item.id}
        variants={getAnimationConfig(staggerItemVariants)}
      >
        {item.content}
      </motion.div>
    ))}
  </motion.div>
);
```

### 3. 性能优化使用
```tsx
import { getPerformanceOptimizedConfig, cardVariants } from '../utils/animations';

const OptimizedCard = () => {
  const optimizedVariants = getPerformanceOptimizedConfig(cardVariants);
  
  return (
    <motion.div variants={optimizedVariants}>
      Content
    </motion.div>
  );
};
```

## 通用过渡配置

### 默认过渡
```typescript
export const defaultTransition: Transition = {
  duration: ANIMATION_DURATIONS.normal,
  ease: EASING.easeOut,
};
```

### 弹性过渡
```typescript
export const springTransition: Transition = {
  type: 'spring',
  stiffness: 300,
  damping: 25,
};
```

## 最佳实践

### 1. 性能考虑
- 始终使用 `getAnimationConfig` 包装动画变体
- 在低端设备上减少动画复杂度
- 使用 GPU 加速的属性（transform, opacity）

### 2. 用户体验
- 尊重用户的动画偏好设置
- 提供平滑的过渡效果
- 避免过度动画

### 3. 代码组织
- 复用预定义的动画变体
- 使用统一的时长和缓动配置
- 保持动画风格一致性

## 注意事项

1. **服务端渲染**: 性能检测函数在服务端安全运行
2. **浏览器兼容性**: 优雅降级到基础动画
3. **内存管理**: 避免创建过多动画实例
4. **调试支持**: 在开发环境中可以禁用动画优化
