# 项目架构文档

## 概述

Pokedex 是一个基于 Electron + React 的现代化图片管理应用，采用主/渲染进程分离架构，支持本地存储和云端OSS存储。

## 技术栈

### 前端技术
- **React 19**: 用户界面框架
- **TypeScript 5.7**: 类型安全的JavaScript
- **TailwindCSS 3.4**: 原子化CSS框架
- **Framer Motion 12**: 动画库
- **React Router DOM 7**: 路由管理
- **Vite 6**: 构建工具

### 桌面应用
- **Electron 28**: 跨平台桌面应用框架
- **Electron Vite 4**: Electron专用构建工具
- **Electron Builder 26**: 应用打包工具

### 数据存储
- **Better-SQLite3 11**: 高性能SQLite数据库
- **AWS SDK S3**: OSS对象存储支持
- **Zod 3**: 数据验证库

### 开发工具
- **Vitest 3**: 单元测试框架
- **ESLint 9**: 代码质量检查
- **Prettier 3**: 代码格式化
- **Playwright 1**: 端到端测试

## 架构设计

### 1. 进程架构

```
┌─────────────────┐    IPC通信    ┌─────────────────┐
│   主进程 (Main)  │ ←──────────→ │ 渲染进程 (Renderer) │
│                 │              │                 │
│ - 窗口管理       │              │ - React应用      │
│ - 数据库操作     │              │ - 用户界面       │
│ - 文件系统       │              │ - 状态管理       │
│ - OSS服务       │              │ - 路由管理       │
└─────────────────┘              └─────────────────┘
```

### 2. 数据流架构

```
┌─────────────┐    Context API    ┌─────────────┐
│ React组件    │ ←──────────────→ │ Context状态  │
│             │                  │             │
│ - UI展示     │                  │ - 全局状态   │
│ - 用户交互   │                  │ - 数据缓存   │
└─────────────┘                  └─────────────┘
       │                                │
       │ IPC调用                        │ 状态更新
       ▼                                ▼
┌─────────────┐    数据库操作    ┌─────────────┐
│ Electron API │ ←──────────────→ │ SQLite数据库 │
│             │                  │             │
│ - 业务逻辑   │                  │ - 数据持久化 │
│ - 文件操作   │                  │ - 事务管理   │
└─────────────┘                  └─────────────┘
```

### 3. 存储架构

```
┌─────────────┐              ┌─────────────┐
│   本地存储   │              │   OSS存储    │
│             │              │             │
│ - 图片文件   │              │ - 图片文件   │
│ - 缩略图     │              │ - 数据库备份 │
│ - SQLite数据库│              │ - 配置同步   │
└─────────────┘              └─────────────┘
       │                            │
       └──────────┬───────────────────┘
                  │
         ┌─────────────┐
         │  存储管理器  │
         │             │
         │ - 存储切换   │
         │ - 数据迁移   │
         │ - 同步管理   │
         └─────────────┘
```

## 目录结构

```
pokedex_front/
├── components/          # React组件
├── contexts/           # Context状态管理
├── electron/           # Electron主进程代码
│   ├── database/       # 数据库管理
│   └── services/       # 业务服务层
├── hooks/              # 自定义React钩子
├── schemas/            # Zod数据验证模式
├── services/           # 前端服务层
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── docs/               # 项目文档
└── __tests__/          # 测试文件
```

## 核心模块

### 1. 数据库管理 (DatabaseManager)
- SQLite数据库初始化和管理
- 数据库迁移和版本控制
- 性能优化配置

### 2. 服务层 (Services)
- **CategoryService**: 分类管理
- **ImageService**: 图片处理和存储
- **TagService**: 标签系统
- **OSSService**: 对象存储服务
- **SettingsService**: 应用配置管理

### 3. IPC通信层
- 50+ IPC通道定义
- 类型安全的通信接口
- 错误处理和日志记录

### 4. 状态管理
- **AuthContext**: 用户认证状态
- **CategoryContext**: 分类数据管理
- **ThemeContext**: 主题配置

## 数据模型

### 核心实体
- **Category**: 图片分类
- **Image**: 图片信息
- **Tag**: 标签数据
- **Species**: 物种信息

### 关系设计
- Category 1:N Image
- Image N:M Tag
- Image 1:1 Species

## 安全特性

### 1. 数据验证
- 使用Zod进行运行时类型检查
- 输入数据严格验证
- API响应数据验证

### 2. 文件安全
- 文件类型验证
- 路径遍历防护
- 文件大小限制

### 3. 存储安全
- OSS访问密钥加密存储
- 本地数据库文件权限控制
- 敏感配置隔离

## 性能优化

### 1. 数据库优化
- WAL模式启用
- 索引优化
- 查询缓存

### 2. 图片处理
- 缩略图生成
- 懒加载实现
- 内存管理

### 3. 界面优化
- 虚拟滚动
- 组件懒加载
- 动画性能优化

## 扩展性设计

### 1. 插件架构
- 服务层模块化
- IPC通道标准化
- 配置系统统一

### 2. 存储扩展
- 多种存储后端支持
- 存储适配器模式
- 数据迁移工具

### 3. 功能扩展
- 组件库标准化
- Hook复用机制
- 工具函数模块化
