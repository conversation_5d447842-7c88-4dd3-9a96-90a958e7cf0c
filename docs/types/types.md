# types 类型定义文档

## 概述

`types.ts` 是应用的主要类型定义文件，从 Zod Schema 推导类型定义，确保运行时验证和编译时类型的一致性。

## 文件位置
`types.ts`

## 设计理念

### 1. Schema-First 方法
- 所有类型都从 Zod Schema 推导而来
- 确保运行时验证和编译时类型的一致性
- 避免类型定义和验证逻辑的重复

### 2. 统一导出
- 集中管理所有应用类型
- 提供清晰的类型分类
- 便于类型的查找和使用

## 类型分类

### 1. 错误处理类型
```typescript
export {
  type ValidationError,      // 验证错误详情
  type HTTPValidationError,  // HTTP 验证错误
  type ApiError,            // API 错误响应
} from './schemas';
```

#### ValidationError
```typescript
interface ValidationError {
  loc: (string | number)[];  // 错误字段路径
  msg: string;               // 错误消息
  type: string;              // 错误类型
}
```

#### ApiError
```typescript
interface ApiError {
  message: string;           // 错误消息
  status?: number;           // HTTP 状态码
  details?: ValidationError[]; // 详细错误信息
}
```

### 2. 认证相关类型
```typescript
export {
  type TokenResponse,        // 认证令牌响应
} from './schemas';
```

#### TokenResponse
```typescript
interface TokenResponse {
  access_token: string;      // 访问令牌
  token_type: string;        // 令牌类型 (通常为 "Bearer")
  expires_in: number;        // 过期时间（秒）
}
```

### 3. 分类相关类型
```typescript
export {
  type CategoryCreate,       // 创建分类数据
  type CategoryRead,         // 分类读取数据
  type CategoryUpdate,       // 更新分类数据
  type CategoryReadWithImages, // 包含图片的分类数据
  type CategoryListResponse, // 分类列表响应
} from './schemas';
```

#### CategoryCreate
```typescript
interface CategoryCreate {
  name: string;              // 分类名称
  description?: string;      // 分类描述（可选）
}
```

#### CategoryRead
```typescript
interface CategoryRead {
  id: string;                // 分类ID
  name: string;              // 分类名称
  description?: string;      // 分类描述
  thumbnail_path?: string;   // 缩略图路径
  thumbnail_url?: string;    // 缩略图URL
  created_at: string;        // 创建时间
  updated_at: string;        // 更新时间
}
```

#### CategoryReadWithImages
```typescript
interface CategoryReadWithImages extends CategoryRead {
  images?: ImageRead[];      // 关联的图片列表
}
```

### 4. 图片相关类型
```typescript
export {
  type ImageRead,            // 图片读取数据
  type ImageUpdate,          // 图片更新数据
  type BodyUploadImage,      // 图片上传数据
  type ExifData,             // EXIF 元数据
  type ImageListResponse,    // 图片列表响应
} from './schemas';
```

#### ImageRead
```typescript
interface ImageRead {
  id: string;                // 图片ID
  category_id: string;       // 所属分类ID
  title?: string;            // 图片标题
  description?: string;      // 图片描述
  original_filename?: string; // 原始文件名
  stored_filename?: string;  // 存储文件名
  relative_file_path?: string; // 相对文件路径
  relative_thumbnail_path?: string; // 缩略图路径
  mime_type?: string;        // MIME 类型
  size_bytes?: number;       // 文件大小（字节）
  created_at: string;        // 创建时间
  updated_at?: string;       // 更新时间
  file_metadata?: Record<string, any>; // 文件元数据
  image_url: string;         // 图片URL
  thumbnail_url?: string;    // 缩略图URL
  tags?: TagRead[];          // 关联标签
  exif_info?: ExifData;      // EXIF 信息
}
```

#### BodyUploadImage
```typescript
interface BodyUploadImage {
  file: File;                // 上传的文件对象
  category_id: string;       // 目标分类ID
  title?: string;            // 图片标题
  description?: string;      // 图片描述
  set_as_category_thumbnail?: boolean; // 是否设为分类缩略图
}
```

#### ExifData
```typescript
interface ExifData {
  make?: string;             // 相机制造商
  model?: string;            // 相机型号
  lens_make?: string;        // 镜头制造商
  lens_model?: string;       // 镜头型号
  date_time_original?: string; // 拍摄时间
  exposure_time?: string;    // 曝光时间
  f_number?: string;         // 光圈值
  iso_speed_rating?: string; // ISO 感光度
  focal_length?: string;     // 焦距
  exposure_program?: string; // 曝光程序
  exposure_mode?: string;    // 曝光模式
  white_balance?: string;    // 白平衡
  color_space?: string;      // 色彩空间
  // ... 其他 EXIF 字段
}
```

### 5. 标签相关类型
```typescript
export {
  type TagRead,              // 标签读取数据
  type TagCreate,            // 创建标签数据
  type TagUpdate,            // 更新标签数据
  type TagListResponse,      // 标签列表响应
} from './schemas';
```

#### TagRead
```typescript
interface TagRead {
  id: string;                // 标签ID
  name: string;              // 标签名称
  description?: string;      // 标签描述
  created_at: string;        // 创建时间
  updated_at: string;        // 更新时间
}
```

#### TagCreate
```typescript
interface TagCreate {
  name: string;              // 标签名称
  description?: string;      // 标签描述（可选）
}
```

### 6. 物种相关类型
```typescript
export {
  type SpeciesRead,          // 物种读取数据
  type SpeciesSuggestionsResponse, // 物种建议响应
} from './schemas';
```

#### SpeciesRead
```typescript
interface SpeciesRead {
  id: string;                // 物种ID
  chinese_name: string;      // 中文名称
  scientific_name: string;   // 学名
  description?: string;      // 描述
  habitat?: string;          // 栖息地
  characteristics?: string;  // 特征
  created_at: string;        // 创建时间
  updated_at: string;        // 更新时间
}
```

#### SpeciesSuggestionsResponse
```typescript
interface SpeciesSuggestionsResponse {
  suggestions: SpeciesRead[]; // 建议的物种列表
  message?: string;          // 附加消息
}
```

## 兼容性类型别名

### SpeciesListResponse
```typescript
// 保留的兼容性类型别名
export type SpeciesListResponse = SpeciesRead[];
```

## 类型使用示例

### 1. API 调用类型注解
```typescript
import type { CategoryCreate, CategoryRead, ApiError } from '../types';

const createCategory = async (data: CategoryCreate): Promise<CategoryRead> => {
  try {
    const response = await api.post('/categories', data);
    return response.data;
  } catch (error) {
    throw error as ApiError;
  }
};
```

### 2. 组件 Props 类型
```typescript
import type { CategoryRead, ImageRead } from '../types';

interface CategoryCardProps {
  category: CategoryRead;
  showDetails?: boolean;
  onSelect?: (category: CategoryRead) => void;
}

interface ImageGalleryProps {
  images: ImageRead[];
  onImageClick?: (image: ImageRead) => void;
}
```

### 3. 状态管理类型
```typescript
import type { CategoryRead, TagRead, ApiError } from '../types';

interface AppState {
  categories: CategoryRead[];
  tags: TagRead[];
  loading: boolean;
  error: ApiError | null;
}
```

### 4. 表单数据类型
```typescript
import type { CategoryCreate, ImageUpdate } from '../types';

const handleCategorySubmit = (formData: CategoryCreate) => {
  // 处理分类创建表单
};

const handleImageUpdate = (imageId: string, data: ImageUpdate) => {
  // 处理图片更新
};
```

## 类型守卫

### 运行时类型检查
```typescript
import { CategoryReadSchema, ImageReadSchema } from '../schemas';

const isCategoryRead = (data: unknown): data is CategoryRead => {
  return CategoryReadSchema.safeParse(data).success;
};

const isImageRead = (data: unknown): data is ImageRead => {
  return ImageReadSchema.safeParse(data).success;
};
```

### 使用类型守卫
```typescript
const processApiResponse = (data: unknown) => {
  if (isCategoryRead(data)) {
    // data 现在被推断为 CategoryRead 类型
    console.log(data.name);
  } else if (isImageRead(data)) {
    // data 现在被推断为 ImageRead 类型
    console.log(data.title);
  }
};
```

## 最佳实践

### 1. 类型导入
```typescript
// 推荐：使用 type 关键字导入类型
import type { CategoryRead, ImageRead } from '../types';

// 避免：混合导入类型和值
import { CategoryRead, someFunction } from '../types';
```

### 2. 泛型使用
```typescript
// 定义通用的 API 响应类型
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// 使用泛型
type CategoryResponse = ApiResponse<CategoryRead>;
type CategoryListResponse = ApiResponse<CategoryRead[]>;
```

### 3. 联合类型
```typescript
// 定义状态联合类型
type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 定义操作类型
type CrudOperation = 'create' | 'read' | 'update' | 'delete';
```

### 4. 条件类型
```typescript
// 根据条件选择类型
type ApiResult<T extends 'success' | 'error'> = 
  T extends 'success' ? { data: any } : { error: string };
```

## 相关文件

- `schemas/`: Zod Schema 定义目录
- `schemas/index.ts`: Schema 统一导出
- `types/splash.ts`: 开屏动画相关类型
- `services/api-validation.ts`: API 验证服务

## 注意事项

1. **类型一致性**: 所有类型都应从 Schema 推导，避免手动定义
2. **向后兼容**: 类型变更时考虑向后兼容性
3. **文档同步**: 类型变更时及时更新相关文档
4. **性能影响**: 复杂类型可能影响 TypeScript 编译性能
