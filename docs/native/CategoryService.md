# CategoryService 原生服务文档

## 概述

`CategoryService` 是负责分类数据管理的核心服务类，提供分类的增删改查、关联图片管理等功能。

## 文件位置
`electron/services/CategoryService.ts`

## 类结构

### 构造函数
```typescript
constructor(private dbManager: DatabaseManager)
```

### 依赖注入
```typescript
setImageService(imageService: any): void
```
- 用于设置 ImageService 实例，避免循环依赖
- 在删除分类时需要处理关联的图片文件

## 核心方法

### 1. getCategories - 获取分类列表
```typescript
async getCategories(skip = 0, limit = 100): Promise<CategoryListResponse>
```

#### 功能
- 分页获取分类列表
- 按创建时间倒序排列
- 返回分类基本信息

#### 参数
- `skip`: 跳过的记录数，默认 0
- `limit`: 返回的最大记录数，默认 100

#### 返回值
```typescript
interface CategoryListResponse {
  categories: CategoryRead[];
  total: number;
  skip: number;
  limit: number;
}
```

#### SQL 查询
```sql
SELECT id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at
FROM categories
ORDER BY created_at DESC
LIMIT ? OFFSET ?
```

### 2. createCategory - 创建分类
```typescript
async createCategory(categoryData: CategoryCreate): Promise<CategoryRead>
```

#### 功能
- 创建新的分类记录
- 自动生成 UUID 作为主键
- 设置创建和更新时间

#### 参数
```typescript
interface CategoryCreate {
  name: string;
  description?: string;
}
```

#### 实现逻辑
1. 生成 UUID 作为分类 ID
2. 设置当前时间为创建和更新时间
3. 初始化缩略图字段为 null
4. 插入数据库记录

### 3. updateCategory - 更新分类
```typescript
async updateCategory(categoryId: string, categoryData: CategoryUpdate): Promise<CategoryRead>
```

#### 功能
- 更新指定分类的信息
- 自动更新修改时间
- 支持部分字段更新

#### 参数
- `categoryId`: 分类 ID
- `categoryData`: 更新的数据

#### 实现逻辑
1. 验证分类是否存在
2. 构建动态 SQL 更新语句
3. 更新 updated_at 字段
4. 返回更新后的分类信息

### 4. deleteCategory - 删除分类
```typescript
async deleteCategory(categoryId: string): Promise<{
  success: boolean;
  message: string;
  details?: {
    categoryDeleted: boolean;
    imagesDeleted: BatchDeleteResult;
  };
}>
```

#### 功能
- 删除分类及其关联的所有图片
- 支持事务操作确保数据一致性
- 详细的删除结果报告

#### 实现流程
1. **验证分类存在性**
   ```typescript
   const category = await this.getCategoryById(categoryId);
   if (!category) {
     return { success: false, message: "分类不存在" };
   }
   ```

2. **获取关联图片**
   ```sql
   SELECT id FROM images WHERE category_id = ?
   ```

3. **删除图片文件**
   - 调用 ImageService 批量删除图片
   - 处理文件系统操作

4. **删除数据库记录**
   ```sql
   -- 删除图片标签关联
   DELETE FROM image_tags WHERE image_id IN (SELECT id FROM images WHERE category_id = ?);
   
   -- 删除图片记录
   DELETE FROM images WHERE category_id = ?;
   
   -- 删除分类记录
   DELETE FROM categories WHERE id = ?;
   ```

### 5. getCategoryById - 获取单个分类
```typescript
async getCategoryById(categoryId: string): Promise<CategoryRead | null>
```

#### 功能
- 根据 ID 获取分类详情
- 返回 null 如果分类不存在

#### SQL 查询
```sql
SELECT * FROM categories WHERE id = ?
```

### 6. getCategoryWithImages - 获取分类及图片
```typescript
async getCategoryWithImages(categoryId: string): Promise<CategoryReadWithImages | null>
```

#### 功能
- 获取分类信息及其所有关联图片
- 图片按创建时间倒序排列
- 处理图片的 JSON 字段解析

#### 实现逻辑
1. **获取分类信息**
   ```sql
   SELECT * FROM categories WHERE id = ?
   ```

2. **获取关联图片**
   ```sql
   SELECT * FROM images 
   WHERE category_id = ?
   ORDER BY created_at DESC
   ```

3. **处理图片数据**
   ```typescript
   const processedImages = images.map(image => ({
     ...image,
     tags: image.tags || [],
     exif_info: image.exif_info ? JSON.parse(image.exif_info) : null,
     file_metadata: image.file_metadata ? JSON.parse(image.file_metadata) : null
   }));
   ```

## 数据类型

### CategoryRead
```typescript
interface CategoryRead {
  id: string;
  name: string;
  description?: string;
  thumbnail_path?: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
}
```

### CategoryReadWithImages
```typescript
interface CategoryReadWithImages extends CategoryRead {
  images?: ImageRead[];
}
```

### BatchDeleteResult
```typescript
interface BatchDeleteResult {
  totalCount: number;
  successCount: number;
  failedCount: number;
  results: Array<{
    imageId: string;
    success: boolean;
    error?: string;
  }>;
  errors: string[];
}
```

## 错误处理

### 常见错误类型
1. **数据库连接错误**: 数据库不可用
2. **分类不存在**: 操作不存在的分类
3. **外键约束**: 删除有关联数据的分类
4. **文件系统错误**: 删除图片文件失败

### 错误处理策略
```typescript
try {
  // 数据库操作
} catch (error) {
  console.error('操作失败:', error);
  throw new Error(`操作失败: ${error.message}`);
}
```

## 性能优化

### 数据库优化
1. **索引策略**
   - 主键索引: `id`
   - 时间索引: `created_at`, `updated_at`
   - 外键索引: `category_id` (在 images 表)

2. **查询优化**
   - 使用 prepared statements
   - 限制返回字段
   - 合理使用分页

### 内存优化
1. **批量操作**: 删除大量图片时分批处理
2. **连接复用**: 复用数据库连接
3. **及时释放**: 大对象及时释放内存

## 事务管理

### 删除分类事务
```typescript
const transaction = db.transaction(() => {
  // 删除图片标签关联
  db.prepare('DELETE FROM image_tags WHERE image_id IN (SELECT id FROM images WHERE category_id = ?)').run(categoryId);
  
  // 删除图片记录
  db.prepare('DELETE FROM images WHERE category_id = ?').run(categoryId);
  
  // 删除分类记录
  db.prepare('DELETE FROM categories WHERE id = ?').run(categoryId);
});

transaction();
```

## 测试覆盖

### 单元测试
- 创建分类测试
- 更新分类测试
- 删除分类测试
- 查询分类测试
- 错误处理测试

### 集成测试
- 与 ImageService 的集成
- 数据库事务测试
- 文件系统操作测试

## 使用示例

### 基础操作
```typescript
// 创建分类
const category = await categoryService.createCategory({
  name: "鸟类",
  description: "各种鸟类图片"
});

// 获取分类列表
const categories = await categoryService.getCategories(0, 20);

// 获取分类详情
const categoryDetail = await categoryService.getCategoryWithImages(category.id);

// 更新分类
const updated = await categoryService.updateCategory(category.id, {
  name: "鸟类摄影",
  description: "专业鸟类摄影作品"
});

// 删除分类
const deleteResult = await categoryService.deleteCategory(category.id);
```

## 相关服务

- **DatabaseManager**: 数据库连接管理
- **ImageService**: 图片文件管理
- **TagService**: 标签管理服务

## 注意事项

1. **循环依赖**: 通过依赖注入避免与 ImageService 的循环依赖
2. **事务一致性**: 删除操作使用事务确保数据一致性
3. **文件清理**: 删除分类时确保清理相关文件
4. **性能考虑**: 大量数据操作时注意内存和性能
