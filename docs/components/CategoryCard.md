# CategoryCard 组件文档

## 概述

`CategoryCard` 是一个用于展示分类信息的卡片组件，支持缩略图显示、动画效果和主题切换。

## 文件位置
`components/CategoryCard.tsx`

## 功能特性

### 核心功能
- **分类展示**: 显示分类名称、缩略图和编号
- **响应式设计**: 支持不同屏幕尺寸的自适应布局
- **图片处理**: 自动处理图片加载失败的情况
- **动画效果**: 使用 Framer Motion 提供流畅的交互动画
- **主题支持**: 完全支持应用的主题系统

### 显示模式
- **标准模式**: 仅显示缩略图（正方形卡片）
- **详细模式**: 显示缩略图 + 分类信息（编号和名称）

## Props 接口

```typescript
interface CategoryCardProps {
  category: CategoryRead;           // 分类数据对象
  showCompactDetails?: boolean;     // 是否显示详细信息，默认 false
  displayIndex?: number;            // 显示的编号索引
}
```

### CategoryRead 类型
```typescript
interface CategoryRead {
  id: string;
  name: string;
  thumbnail_url?: string;
  // ... 其他分类属性
}
```

## 依赖项

### React 相关
- `React`: 核心框架
- `react-router-dom`: 路由导航
- `framer-motion`: 动画库

### 内部依赖
- `../types`: TypeScript 类型定义
- `../contexts/ThemeContext`: 主题上下文
- `../constants`: 常量配置
- `../utils/animations`: 动画配置
- `./icons`: 图标组件

## 状态管理

### 本地状态
```typescript
const [imageLoadError, setImageLoadError] = useState(false);
```

### 上下文状态
```typescript
const { theme } = useTheme();
```

## 核心逻辑

### 图片URL处理
```typescript
const getRelativeUrl = (absoluteUrl: string | null | undefined): string | null | undefined => {
  if (absoluteUrl && absoluteUrl.startsWith(IMAGE_BASE_URL)) {
    return absoluteUrl.substring(IMAGE_BASE_URL.length);
  }
  return absoluteUrl;
};
```

### 图片加载错误处理
- 监听图片加载失败事件
- 自动显示占位符图标
- 分类或缩略图URL变化时重置错误状态

### 动画配置
- **卡片动画**: 使用 `cardVariants` 配置
- **图片动画**: 使用 `imageVariants` 配置
- **性能优化**: 通过 `getAnimationConfig` 考虑用户偏好

## 样式系统

### 主题集成
- 完全使用主题系统的样式类
- 支持明暗模式切换
- 响应式设计

### 关键样式类
```typescript
// 卡片容器
const cardClasses = `block ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} 
  ${theme.card.hoverShadow} ${theme.card.border || ''} overflow-hidden group 
  ${theme.card.transition}`;

// 图片容器
const imageContainerBaseClasses = `relative w-full overflow-hidden transition-all 
  duration-500 ease-in-out aspect-square`;

// 图片样式
const imageClasses = `w-full h-full object-cover transition-transform duration-300 
  ease-in-out group-hover:scale-105`;
```

## 动画效果

### 卡片动画
- **初始状态**: 透明度0，向下偏移20px，缩放0.95
- **显示状态**: 透明度1，位置正常，缩放1
- **悬停效果**: 向上偏移4px，缩放1.02
- **点击效果**: 缩放0.98

### 图片动画
- **初始状态**: 透明度0，缩放1.1
- **显示状态**: 透明度1，缩放1
- **悬停效果**: 缩放1.05

### 详细信息动画
- **渐入效果**: 透明度和位置的渐变动画
- **延迟显示**: 使用不同的延迟时间创建层次感

## 无障碍支持

### ARIA 标签
```typescript
<Link
  to={`/categories/${category.id}`}
  aria-label={`View category: ${category.name}`}
  className="block w-full h-full"
>
```

### 键盘导航
- 支持 Tab 键导航
- 支持 Enter 键激活

## 性能优化

### 图片优化
- 懒加载支持（通过父组件实现）
- 错误处理避免重复加载
- 缩略图使用优化

### 动画优化
- 考虑用户的动画偏好设置
- 使用 GPU 加速的 transform 属性
- 避免引起重排的动画属性

## 使用示例

### 基础用法
```tsx
<CategoryCard category={categoryData} />
```

### 显示详细信息
```tsx
<CategoryCard 
  category={categoryData} 
  showCompactDetails={true}
  displayIndex={1}
/>
```

### 在列表中使用
```tsx
{categories.map((category, index) => (
  <CategoryCard
    key={category.id}
    category={category}
    showCompactDetails={true}
    displayIndex={index + 1}
  />
))}
```

## 相关组件

- `CategoryCardSkeleton`: 加载状态的骨架屏组件
- `CategoryDetail`: 分类详情页面组件
- `ImageCard`: 图片卡片组件

## 注意事项

1. **图片URL**: 组件会自动处理绝对URL到相对URL的转换
2. **错误处理**: 图片加载失败时会显示占位符图标
3. **性能**: 在大量卡片渲染时建议使用虚拟滚动
4. **主题**: 确保在 ThemeProvider 包裹下使用

## 测试建议

1. **图片加载测试**: 测试有效和无效的图片URL
2. **动画测试**: 测试不同动画偏好设置下的表现
3. **主题测试**: 测试不同主题下的样式表现
4. **响应式测试**: 测试不同屏幕尺寸下的布局
5. **无障碍测试**: 测试键盘导航和屏幕阅读器支持
