# Electron行为总结

## 概述

本文档总结了Pokedex Electron应用的核心行为机制，包括主进程管理、IPC通信、安全特性等关键功能。

## 应用生命周期管理

### 启动流程
1. **服务初始化**: 初始化设置服务和数据库管理器
2. **数据库迁移**: 执行必要的数据库版本升级
3. **服务注册**: 初始化所有业务服务（图片、分类、标签等）
4. **IPC注册**: 注册所有IPC通信处理器
5. **窗口创建**: 创建主应用窗口
6. **菜单设置**: 设置应用菜单和快捷键

### 退出处理
- **数据库安全关闭**: 确保数据库连接正确关闭
- **平台差异处理**: macOS保持应用运行，其他平台完全退出
- **清理操作**: 释放资源，清理临时文件
- **错误处理**: 退出过程中的异常处理

## 窗口管理

### 主窗口配置
- **尺寸设置**: 默认1400x900，最小800x600
- **安全配置**: 禁用Node.js集成，启用上下文隔离
- **预加载脚本**: 通过preload.js安全暴露API
- **平台适配**: macOS使用隐藏标题栏样式
- **延迟显示**: 等待内容加载完成后显示窗口

### 开发环境支持
- **开发服务器**: 连接到Vite开发服务器(localhost:5173)
- **开发者工具**: 自动打开DevTools
- **生产环境**: 加载打包后的静态文件

### 窗口事件处理
- **关闭事件**: 清理窗口引用
- **最小化/恢复**: 记录窗口状态变化
- **准备显示**: 发送初始化信号到渲染进程

## 菜单系统

### 菜单结构
- **文件菜单**: 新建分类(Ctrl+N)、导入图片(Ctrl+I)、退出
- **编辑菜单**: 标准编辑操作（撤销、重做、剪切、复制、粘贴）
- **视图菜单**: 页面刷新、开发者工具、缩放控制、全屏切换
- **存储菜单**: 本地/OSS存储模式切换、存储设置
- **数据库菜单**: 数据库统计、备份、恢复、重置
- **帮助菜单**: 关于信息、开发者工具

### 快捷键支持
- **跨平台兼容**: 使用CmdOrCtrl自动适配Windows/macOS
- **常用操作**: 新建(Ctrl+N)、导入(Ctrl+I)、开发者工具(F12)
- **标准编辑**: 撤销(Ctrl+Z)、重做(Ctrl+Shift+Z)等

### macOS特殊处理
- **应用菜单**: 添加macOS标准的应用菜单
- **系统集成**: 支持系统服务、隐藏/显示等macOS特性

## 自定义协议处理器

### electron://协议
- **用途**: 统一处理本地和OSS存储的文件访问
- **格式**: `electron://file/filename` 或 `electron://thumbnail/filename`
- **双模式支持**: 自动根据存储类型(本地/OSS)选择相应的文件获取方式

### 协议处理流程
1. **URL解析**: 解析协议URL获取文件类型和文件名
2. **存储类型检测**: 根据设置确定当前存储模式
3. **文件获取**: 
   - 本地模式：直接读取本地文件
   - OSS模式：从OSS下载文件
4. **MIME类型设置**: 根据文件扩展名设置正确的MIME类型
5. **错误处理**: 文件不存在或下载失败时返回错误

### 支持的文件类型
- **图片格式**: JPG、PNG、GIF、BMP、WebP、SVG
- **自动MIME**: 根据文件扩展名自动设置正确的MIME类型

## 首次启动配置

### 首次设置检测
- **自动检测**: 应用启动时自动检测是否为首次运行
- **延迟显示**: 等待主窗口加载完成后显示设置对话框
- **配置保存**: 设置完成后自动创建配置目录

### 设置对话框功能
- **存储位置选择**: 用户可选择自定义文件夹或使用默认位置
- **文件夹选择器**: 提供原生文件夹选择对话框
- **设置确认**: 显示设置结果和存储路径信息
- **错误处理**: 设置失败时提供友好的错误提示

### 用户体验
- **友好引导**: 清晰的欢迎信息和操作指引
- **灵活选择**: 支持自定义路径和默认路径两种选项
- **后续修改**: 用户可在设置中随时更改存储位置

## IPC通信机制

### 通信架构
- **双向通信**: 主进程和渲染进程之间的双向异步通信
- **类型安全**: 通过TypeScript类型定义确保通信接口的类型安全
- **错误处理**: 统一的错误处理和响应格式

### IPC处理器分类
- **数据库操作**: 数据库连接测试、统计信息、重置等
- **分类管理**: 分类的增删改查操作
- **图片管理**: 图片上传、删除、更新、查询
- **标签管理**: 标签的创建、搜索、关联管理
- **设置管理**: 存储设置、OSS配置等
- **批量操作**: 批量导入、数据库同步等
- **物种查询**: 物种搜索和信息查询

### 通信特点
- **异步处理**: 所有IPC调用都是异步的，不阻塞UI
- **进度反馈**: 长时间操作提供实时进度更新
- **统一响应**: 标准化的成功/失败响应格式
- **错误传播**: 详细的错误信息传递到前端

## 预加载脚本API

### API结构
通过contextBridge安全地暴露API给渲染进程：
- **数据库API**: testDatabase、getDatabaseStats、resetDatabase
- **分类API**: categories.getAll、create、update、delete等
- **图片API**: images.upload、getById、delete、update等  
- **标签API**: tags.getAll、create、search、addToImage等
- **设置API**: settings.getStorage、saveStorage、selectFolder
- **OSS API**: oss.configure、testConnection
- **导入API**: import.validateFolder、fromFolder、selectFolder
- **物种API**: species.search、getInfo、getByFamily等
- **同步API**: sync.backupToOSS、restoreFromOSS、listBackups

### 安全特性
- **上下文隔离**: 启用contextIsolation防止代码注入
- **API白名单**: 只暴露必要的API，不暴露Node.js原生API
- **类型安全**: 完整的TypeScript类型定义
- **通道验证**: 验证IPC通道名称，防止恶意监听

## 安全机制

### 进程隔离
- **禁用Node集成**: 渲染进程无法直接访问Node.js API
- **预加载脚本**: 通过安全的预加载脚本暴露必要功能
- **权限最小化**: 只提供应用必需的最小权限

### 文件访问控制
- **路径验证**: 防止路径遍历攻击
- **文件类型检查**: 只允许支持的图片格式
- **存储隔离**: 文件访问限制在指定的存储目录内

### 错误处理
- **统一格式**: 标准化的错误响应格式
- **详细日志**: 记录操作日志用于调试和审计
- **优雅降级**: 部分功能失败不影响整体应用

## 总结

Pokedex Electron应用的行为特点：

1. **完整的生命周期管理**: 从启动到退出的完整流程控制
2. **安全的进程通信**: 通过IPC和预加载脚本实现安全的跨进程通信
3. **用户友好的界面**: 原生菜单、快捷键、首次设置引导
4. **灵活的文件访问**: 自定义协议支持本地和云存储
5. **强大的安全机制**: 多层安全防护确保应用安全
