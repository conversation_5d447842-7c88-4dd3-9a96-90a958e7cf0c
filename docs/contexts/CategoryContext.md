# CategoryContext 状态管理文档

## 概述

`CategoryContext` 是用于管理应用中分类数据的全局状态管理器，提供分类列表的获取、缓存和更新功能。

## 文件位置
`contexts/CategoryContext.tsx`

## 功能特性

### 核心功能
- **全局状态管理**: 管理应用中的分类数据状态
- **自动数据获取**: 组件挂载时自动获取分类数据
- **错误处理**: 统一的错误状态管理
- **加载状态**: 提供加载状态指示
- **手动刷新**: 支持手动重新获取数据

### 状态管理
- **categories**: 分类数据列表
- **isLoading**: 加载状态标识
- **error**: 错误信息
- **fetchCategories**: 手动刷新方法

## 接口定义

### CategoryContextState
```typescript
interface CategoryContextState {
  categories: CategoryReadWithImages[];
  isLoading: boolean;
  error: ApiError | string | null;
  fetchCategories: () => Promise<void>;
}
```

### CategoryReadWithImages
```typescript
interface CategoryReadWithImages {
  id: string;
  name: string;
  description?: string;
  thumbnail_path?: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
  images?: ImageRead[];
}
```

## 组件结构

### CategoryProvider
```typescript
export const CategoryProvider: React.FC<{ children: ReactNode }> = ({ children })
```

#### 状态管理
```typescript
const [categories, setCategories] = useState<CategoryReadWithImages[]>([]);
const [isLoading, setIsLoading] = useState(true);
const [error, setError] = useState<ApiError | string | null>(null);
```

#### 数据获取逻辑
```typescript
const fetchCategoriesCallback = useCallback(async () => {
  setIsLoading(true);
  setError(null);
  try {
    const basicCategories = await getCategories(0, MAX_CATEGORIES_TO_LOAD_IMAGES_FROM);
    const categoriesForState: CategoryReadWithImages[] = basicCategories.map(c => ({
      ...c,
      images: [], // 初始化为空数组
    }));
    setCategories(categoriesForState);
  } catch (err) {
    setError(err as ApiError);
    setCategories([]);
  } finally {
    setIsLoading(false);
  }
}, []);
```

### useCategories Hook
```typescript
export const useCategories = (): CategoryContextState => {
  const context = useContext(CategoryContext);
  if (context === undefined) {
    throw new Error('useCategories must be used within a CategoryProvider');
  }
  return context;
}
```

## 数据流程

### 1. 初始化流程
```
组件挂载 → useEffect 触发 → fetchCategoriesCallback 执行 → 
设置 loading 状态 → 调用 API → 处理响应 → 更新状态
```

### 2. 数据转换
```typescript
// API 返回的基础分类数据
const basicCategories = await getCategories(0, MAX_CATEGORIES_TO_LOAD_IMAGES_FROM);

// 转换为 CategoryReadWithImages 格式
const categoriesForState: CategoryReadWithImages[] = basicCategories.map(c => ({
  ...c,
  images: [], // 初始化图片数组为空
}));
```

### 3. 错误处理
```typescript
try {
  // 数据获取逻辑
} catch (err) {
  setError(err as ApiError);
  setCategories([]); // 清空数据
} finally {
  setIsLoading(false); // 结束加载状态
}
```

## 配置常量

### MAX_CATEGORIES_TO_LOAD_IMAGES_FROM
```typescript
// 从 constants.ts 导入
import { MAX_CATEGORIES_TO_LOAD_IMAGES_FROM } from '../constants';
```
- 控制一次性加载的最大分类数量
- 用于性能优化，避免加载过多数据

## 使用示例

### 基础用法
```tsx
import { CategoryProvider, useCategories } from '../contexts/CategoryContext';

// 在应用根组件中包装
function App() {
  return (
    <CategoryProvider>
      <CategoryList />
    </CategoryProvider>
  );
}

// 在子组件中使用
function CategoryList() {
  const { categories, isLoading, error, fetchCategories } = useCategories();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      {categories.map(category => (
        <div key={category.id}>{category.name}</div>
      ))}
      <button onClick={fetchCategories}>Refresh</button>
    </div>
  );
}
```

### 在 CategoryList 组件中的使用
```tsx
const CategoryList: React.FC = () => {
  const {
    categories: allCategories,
    isLoading: isLoadingCategories,
    error: categoriesError,
    fetchCategories: fetchCategoriesFromContext,
  } = useCategories();

  // 创建分类后刷新数据
  const handleCreateCategory = async (data: CategoryCreate) => {
    await createCategory(data);
    await fetchCategoriesFromContext(); // 刷新分类列表
  };

  return (
    // 组件渲染逻辑
  );
};
```

## 性能优化

### 1. useCallback 优化
```typescript
const fetchCategoriesCallback = useCallback(async () => {
  // 获取数据逻辑
}, []); // 空依赖数组，避免不必要的重新创建
```

### 2. 数据限制
- 使用 `MAX_CATEGORIES_TO_LOAD_IMAGES_FROM` 限制加载数量
- 图片数据延迟加载（初始化为空数组）

### 3. 错误恢复
- 错误时清空数据，避免显示过期信息
- 提供手动刷新机制

## 状态生命周期

### 1. 初始状态
```typescript
categories: []
isLoading: true
error: null
```

### 2. 加载中状态
```typescript
categories: [] // 保持之前的数据或空数组
isLoading: true
error: null // 清空之前的错误
```

### 3. 成功状态
```typescript
categories: [...] // 获取到的分类数据
isLoading: false
error: null
```

### 4. 错误状态
```typescript
categories: [] // 清空数据
isLoading: false
error: ApiError | string
```

## 错误处理策略

### 错误类型
1. **网络错误**: 网络连接问题
2. **API 错误**: 服务器返回错误
3. **数据验证错误**: 响应数据格式不正确

### 错误处理
```typescript
catch (err) {
  setError(err as ApiError); // 设置错误状态
  setCategories([]); // 清空数据，避免显示过期信息
}
```

## 相关组件

### 使用 CategoryContext 的组件
- `CategoryList`: 分类列表页面
- `CategorySearch`: 分类搜索组件
- `CategoryDetail`: 分类详情页面

### 相关 API
- `getCategories`: 获取分类列表的 API 方法
- `createCategory`: 创建分类的 API 方法

## 最佳实践

### 1. 错误边界
```tsx
// 确保在 CategoryProvider 外使用时有适当的错误处理
try {
  const categories = useCategories();
} catch (error) {
  // 处理 Context 未提供的错误
}
```

### 2. 数据同步
```tsx
// 在修改分类数据后及时刷新
const handleUpdateCategory = async (id: string, data: CategoryUpdate) => {
  await updateCategory(id, data);
  await fetchCategories(); // 刷新数据保持同步
};
```

### 3. 加载状态处理
```tsx
// 提供良好的加载体验
if (isLoading && categories.length === 0) {
  return <SkeletonLoader />;
}

// 增量加载时保持现有数据显示
if (isLoading && categories.length > 0) {
  return (
    <div>
      {/* 显示现有数据 */}
      <LoadingIndicator />
    </div>
  );
}
```

## 注意事项

1. **Context 范围**: 确保在 CategoryProvider 包裹的组件树中使用
2. **数据一致性**: 修改分类数据后及时调用 fetchCategories 刷新
3. **性能考虑**: 避免频繁调用 fetchCategories，考虑防抖处理
4. **错误恢复**: 提供用户友好的错误提示和重试机制
