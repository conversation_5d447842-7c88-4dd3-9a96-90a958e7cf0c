# get-categories IPC 通道文档

## 概述

`get-categories` 是用于获取分类列表的 IPC 通道，支持分页查询和数据统计。

## 通道信息

- **通道名称**: `get-categories`
- **通信方向**: 渲染进程 → 主进程
- **返回类型**: Promise\<CategoryListResponse\>

## 参数

### 输入参数
```typescript
(skip?: number, limit?: number) => Promise<CategoryListResponse>
```

- `skip` (可选): 跳过的记录数，默认为 0，用于分页
- `limit` (可选): 返回的最大记录数，默认为 100

### 返回数据
```typescript
interface CategoryListResponse {
  categories: CategoryRead[];
  totalCount: number;
  hasMore: boolean;
}

interface CategoryRead {
  id: string;
  name: string;
  description?: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
  image_count?: number;
}
```

## 实现位置

### 主进程处理器
**文件**: `electron/main.ts`
**行号**: 1018-1034

```typescript
ipcMain.handle('get-categories', async (_, skip = 0, limit = 100) => {
  try {
    console.log('📥 IPC: 收到获取分类请求', { skip, limit });
    const result = await categoryService.getCategories(skip, limit);
    console.log(`📤 IPC: 返回 ${result.categories.length} 条分类记录`);
    return result;
  } catch (error) {
    console.error('❌ 获取分类失败:', error);
    throw error;
  }
});
```

### 渲染进程接口
**文件**: `electron/preload.ts`
**行号**: 13-14

```typescript
getCategories: (skip?: number, limit?: number) => 
  ipcRenderer.invoke('get-categories', skip, limit),
```

## 业务逻辑

### 服务层实现
**服务**: `CategoryService.getCategories()`
**文件**: `electron/services/CategoryService.ts`

#### 主要功能
1. **分页查询**: 支持 skip 和 limit 参数进行分页
2. **数据统计**: 返回总记录数和是否有更多数据
3. **关联查询**: 可选择是否包含图片数量统计
4. **排序**: 按创建时间或更新时间排序

#### 数据库查询
```sql
-- 获取分类列表
SELECT c.*, COUNT(i.id) as image_count 
FROM categories c 
LEFT JOIN images i ON c.id = i.category_id 
GROUP BY c.id 
ORDER BY c.created_at DESC 
LIMIT ? OFFSET ?;

-- 获取总数
SELECT COUNT(*) as total FROM categories;
```

## 使用示例

### 前端调用
```typescript
// 获取第一页数据
const firstPage = await window.electronAPI.getCategories(0, 20);

// 获取下一页数据
const nextPage = await window.electronAPI.getCategories(20, 20);

// 获取所有数据
const allCategories = await window.electronAPI.getCategories();
```

### React Hook 使用
```typescript
const useCategories = (page: number = 0, pageSize: number = 20) => {
  const [categories, setCategories] = useState<CategoryRead[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const loadCategories = useCallback(async () => {
    setLoading(true);
    try {
      const result = await window.electronAPI.getCategories(
        page * pageSize, 
        pageSize
      );
      setCategories(prev => [...prev, ...result.categories]);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error('加载分类失败:', error);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize]);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  return { categories, loading, hasMore, loadCategories };
};
```

## 错误处理

### 常见错误
1. **数据库连接错误**: 数据库不可用或损坏
2. **参数验证错误**: skip 或 limit 参数无效
3. **权限错误**: 数据库文件权限不足

### 错误响应
```typescript
// 错误会被抛出并传递到渲染进程
try {
  const categories = await window.electronAPI.getCategories();
} catch (error) {
  console.error('获取分类失败:', error.message);
  // 处理错误状态
}
```

## 性能考虑

### 优化策略
1. **分页加载**: 避免一次性加载大量数据
2. **索引优化**: 数据库表建立适当索引
3. **缓存机制**: 在适当的场景下使用缓存
4. **懒加载**: 图片数量等统计信息可选加载

### 推荐用法
```typescript
// 推荐：分页加载
const CATEGORIES_PER_PAGE = 20;
const categories = await window.electronAPI.getCategories(0, CATEGORIES_PER_PAGE);

// 不推荐：一次性加载所有数据
const allCategories = await window.electronAPI.getCategories(0, 999999);
```

## 相关通道

- `create-category`: 创建新分类
- `update-category`: 更新分类信息
- `delete-category`: 删除分类
- `get-category-by-id`: 获取单个分类详情
- `get-category-with-images`: 获取分类及其图片

## 日志记录

### 主进程日志
```
📥 IPC: 收到获取分类请求 { skip: 0, limit: 20 }
📤 IPC: 返回 15 条分类记录
📋 IPC返回的分类数据: [
  { id: "cat1", name: "鸟类", image_count: 25 },
  { id: "cat2", name: "植物", image_count: 18 }
]
```

### 错误日志
```
❌ 获取分类失败: Error: Database connection failed
```

## 测试用例

### 单元测试
```typescript
describe('get-categories IPC', () => {
  test('应该返回分页的分类数据', async () => {
    const result = await ipcRenderer.invoke('get-categories', 0, 10);
    expect(result).toHaveProperty('categories');
    expect(result).toHaveProperty('totalCount');
    expect(result).toHaveProperty('hasMore');
    expect(result.categories).toHaveLength(10);
  });

  test('应该处理空结果', async () => {
    const result = await ipcRenderer.invoke('get-categories', 1000, 10);
    expect(result.categories).toHaveLength(0);
    expect(result.hasMore).toBe(false);
  });
});
```

## 版本历史

- **v1.0.0**: 初始实现，支持基础分页查询
- **v1.1.0**: 添加图片数量统计
- **v1.2.0**: 优化查询性能，添加索引
- **v1.3.0**: 增强错误处理和日志记录
