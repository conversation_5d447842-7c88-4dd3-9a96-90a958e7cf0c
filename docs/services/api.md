# api 服务层文档

## 概述

`api.ts` 是应用的核心服务层，提供统一的 API 接口，支持 Electron 本地模式和 Web 远程模式的自动切换。

## 文件位置
`services/api.ts`

## 核心特性

### 1. 双模式支持
- **Electron 模式**: 使用本地 IPC 通信
- **Web 模式**: 使用远程 HTTP API
- **自动检测**: 根据运行环境自动选择模式

### 2. 数据验证
- 使用 Zod Schema 进行运行时验证
- 统一的错误处理和格式化
- 类型安全的 API 响应

### 3. 错误处理
- 统一的错误格式化
- 详细的错误信息和状态码
- 用户友好的错误消息

## 配置和初始化

### Axios 客户端配置
```typescript
const apiClient = axios.create({
  baseURL: API_BASE_URL,
});
```

### 请求拦截器
```typescript
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('access_token');
    const isAuthEndpoint = 
      config.url?.includes('/api/send-verification') || 
      config.url?.includes('/api/verify');

    if (
      token &&
      config.method &&
      !['get', 'head', 'options'].includes(config.method.toLowerCase()) &&
      !isAuthEndpoint
    ) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);
```

#### 功能
- 自动添加认证 Token
- 排除认证端点的 Token 添加
- 仅对非安全方法添加 Token

## 错误处理系统

### formatApiError 函数
```typescript
function formatApiError(error: AxiosError | any, url: string = 'API'): ApiError {
  if (axios.isAxiosError(error)) {
    const serverError = error.response?.data as Partial<
      HTTPValidationError & { message?: string; error?: string; detail?: any }
    >;
    
    let message = `API 请求 ${error.config?.url || url} 失败。状态码: ${error.response?.status || 'N/A'}`;
    let details: ValidationError[] | undefined = undefined;

    // 处理不同类型的错误响应
    if (serverError?.detail) {
      if (Array.isArray(serverError.detail)) {
        details = serverError.detail;
        message = '请求数据验证失败。';
      } else if (typeof serverError.detail === 'string') {
        message = serverError.detail;
      }
    } else if (serverError?.message) {
      message = serverError.message;
    } else if (serverError?.error) {
      message = serverError.error;
    }

    return { message, status: error.response?.status, details };
  }
  return { message: error.message || '发生未知错误。', status: undefined };
}
```

### 错误类型
```typescript
interface ApiError {
  message: string;
  status?: number;
  details?: ValidationError[];
}

interface ValidationError {
  loc: (string | number)[];
  msg: string;
  type: string;
}
```

## API 端点分类

### 1. 认证端点 (Auth Endpoints)

#### sendVerificationCode
```typescript
export async function sendVerificationCode(email: string): Promise<any>
```

**功能**: 发送验证码到指定邮箱
**模式支持**:
- **Electron**: 模拟发送成功
- **Web**: 调用远程 API

#### verifyCodeAndGetToken
```typescript
export async function verifyCodeAndGetToken(email: string, code: string): Promise<TokenResponse>
```

**功能**: 验证码验证并获取访问令牌
**模式支持**:
- **Electron**: 返回本地模拟 Token
- **Web**: 调用远程验证 API

### 2. 分类端点 (Category Endpoints)

#### createCategory
```typescript
export async function createCategory(categoryData: CategoryCreate): Promise<CategoryRead>
```

#### getCategories
```typescript
export async function getCategories(skip: number = 0, limit: number = 100): Promise<CategoryListResponse>
```

**实现示例**:
```typescript
export async function getCategories(skip: number = 0, limit: number = 100): Promise<CategoryListResponse> {
  const url = `/api/categories/?skip=${skip}&limit=${limit}`;
  const isElectron = typeof window !== 'undefined' && 
                     window.electronAPI?.isElectron === true && 
                     typeof window.electronAPI.getCategories === 'function';
  
  try {
    let response;
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API获取分类');
      response = await window.electronAPI.getCategories!(skip, limit);
    } else {
      console.log('⚠️ API: 使用远程HTTP API获取分类');
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, CategoryListResponseSchema, '获取分类列表响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}
```

#### getCategoryWithImages
```typescript
export async function getCategoryWithImages(categoryId: string): Promise<CategoryReadWithImages>
```

#### updateCategory
```typescript
export async function updateCategory(categoryId: string, categoryData: CategoryUpdate): Promise<CategoryRead>
```

#### deleteCategory
```typescript
export async function deleteCategory(categoryId: string): Promise<void>
```

### 3. 图片端点 (Image Endpoints)

#### uploadImage
```typescript
export async function uploadImage(imageData: BodyUploadImage): Promise<ImageRead>
```

**特殊处理**:
- **Electron**: 将 File 对象转换为 Buffer
- **Web**: 使用 FormData 上传

**实现逻辑**:
```typescript
if (isElectron) {
  // 将File对象转换为Buffer
  const arrayBuffer = await imageData.file.arrayBuffer();
  const fileBuffer = new Uint8Array(arrayBuffer);
  
  const response = await window.electronAPI.uploadImage!(
    imageData.category_id,
    fileBuffer,
    imageData.file.name,
    imageData.file.type,
    imageData.set_as_category_thumbnail || false
  );
  
  // 如果需要更新图片标题和描述
  if (imageData.title || imageData.description) {
    const updateData: any = {};
    if (imageData.title) updateData.title = imageData.title;
    if (imageData.description) updateData.description = imageData.description;
    
    return await window.electronAPI.updateImage!(response.id, updateData);
  }
  
  return response;
}
```

#### getImage
```typescript
export async function getImage(imageId: string): Promise<ImageRead>
```

#### updateImage
```typescript
export async function updateImage(imageId: string, imageData: ImageUpdate): Promise<ImageRead>
```

#### deleteImage
```typescript
export async function deleteImage(imageId: string): Promise<any>
```

#### deleteImages (批量删除)
```typescript
export async function deleteImages(imageIds: string[]): Promise<any>
```

### 4. 标签端点 (Tag Endpoints)

#### getAllTags
```typescript
export async function getAllTags(): Promise<TagRead[]>
```

#### createTag
```typescript
export async function createTag(tagData: TagCreate): Promise<TagRead>
```

#### updateTag
```typescript
export async function updateTag(tagId: string, tagData: TagUpdate): Promise<TagRead>
```

#### deleteTag
```typescript
export async function deleteTag(tagId: string): Promise<void>
```

#### addTagToImage
```typescript
export async function addTagToImage(imageId: string, tagId: string): Promise<void>
```

#### removeTagFromImage
```typescript
export async function removeTagFromImage(imageId: string, tagId: string): Promise<void>
```

### 5. 物种信息端点 (Species Endpoints)

#### getSpeciesSuggestions
```typescript
export async function getSpeciesSuggestions(query: string, limit: number = 10): Promise<SpeciesSuggestionsResponse>
```

**模式支持**:
- **Electron**: 返回占位符响应，提示需要网络
- **Web**: 调用远程识别 API

#### getSpeciesDetails
```typescript
export async function getSpeciesDetails(chineseName: string): Promise<SpeciesRead>
```

## 环境检测

### Electron 环境检测
```typescript
const isElectron = typeof window !== 'undefined' && 
                   window.electronAPI && 
                   window.electronAPI.isElectron === true && 
                   typeof window.electronAPI.methodName === 'function';
```

### 检测条件
1. `window` 对象存在
2. `window.electronAPI` 存在
3. `isElectron` 标志为 true
4. 对应的方法存在

## 数据验证集成

### safeParseApiResponse
```typescript
import { safeParseApiResponse } from './api-validation';

// 使用示例
return safeParseApiResponse(response, CategoryReadSchema, '创建分类响应数据格式错误');
```

### formatApiErrorWithZod
```typescript
import { formatApiErrorWithZod } from './api-validation';

// 使用示例
throw formatApiErrorWithZod(error, url);
```

## 日志记录

### 模式标识
- `✅ API: 使用本地Electron API` - Electron 模式
- `⚠️ API: 使用远程HTTP API` - Web 模式
- `🌐 API: 使用远程HTTP API` - Web 模式（标签相关）
- `🔌 API: 本地模式 - 功能需要联网` - 功能限制提示

### 详细日志
```typescript
console.log('📥 前端收到的分类数据:', response.length, '条');
console.log('📋 前端收到的详细数据:', response.map((cat: any) => ({
  id: cat.id,
  name: cat.name,
  description: cat.description,
  created_at: cat.created_at
})));
```

## 使用示例

### 基础调用
```typescript
import { getCategories, createCategory } from '../services/api';

// 获取分类列表
const categories = await getCategories(0, 20);

// 创建新分类
const newCategory = await createCategory({
  name: "鸟类",
  description: "各种鸟类图片"
});
```

### 错误处理
```typescript
try {
  const categories = await getCategories();
} catch (error) {
  if (error.status === 422) {
    // 处理验证错误
    console.error('数据验证失败:', error.details);
  } else {
    // 处理其他错误
    console.error('API 调用失败:', error.message);
  }
}
```

## 最佳实践

### 1. 错误处理
- 始终使用 try-catch 包装 API 调用
- 根据错误状态码提供不同的用户反馈
- 记录详细的错误信息用于调试

### 2. 数据验证
- 所有 API 响应都应通过 Zod 验证
- 使用 `safeParseApiResponse` 进行安全解析
- 提供有意义的验证错误消息

### 3. 环境适配
- 检测运行环境并选择合适的 API 模式
- 为不支持的功能提供友好的提示
- 保持 API 接口的一致性

### 4. 性能优化
- 合理使用分页参数
- 避免不必要的数据传输
- 实现适当的缓存策略

## 相关文件

- `services/api-validation.ts`: API 验证服务
- `constants.ts`: 环境常量定义
- `schemas/`: Zod Schema 定义
- `electron/preload.ts`: Electron API 接口定义
