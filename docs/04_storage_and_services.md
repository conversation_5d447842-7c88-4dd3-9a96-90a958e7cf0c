# 存储系统和高级服务总结

## 概述

本文档总结了Pokedex Electron应用的存储系统和高级服务功能，包括双存储模式、OSS集成、批量导入、数据库同步、物种查询等核心服务。

## 存储系统架构

### 双存储模式设计
项目支持本地存储和OSS存储的无缝切换：
- **存储类型**: 'local' | 'oss'
- **配置管理**: 通过SettingsService统一管理存储配置
- **动态切换**: 运行时可在两种存储模式间切换
- **数据一致性**: 切换时保持数据库记录和文件的一致性

### 本地存储模式

#### 文件组织结构
```
用户选择的存储路径/
├── 分类1/
│   ├── images/           # 原图存储
│   └── thumbnails/       # 缩略图存储
├── 分类2/
│   ├── images/
│   └── thumbnails/
└── database.db          # SQLite数据库文件
```

#### 特点
- **分类文件夹**: 每个分类对应一个文件夹
- **分离存储**: 原图和缩略图分别存储在images和thumbnails子文件夹
- **路径解析**: 根据分类ID和文件名动态解析文件路径
- **默认处理**: 未知分类的图片存储在unknown-category文件夹

### OSS存储模式

#### OSS配置
- **服务端点**: OSS服务的访问端点
- **访问密钥**: AccessKeyId和AccessKeySecret
- **存储桶**: 指定的OSS Bucket名称
- **路径前缀**: 可选的文件路径前缀
- **区域设置**: OSS服务所在区域

#### OSS路径结构
```
bucket/
├── 分类1/
│   ├── images/           # 原图存储
│   └── thumbnails/       # 缩略图存储
├── 分类2/
│   ├── images/
│   └── thumbnails/
```

#### 特点
- **云端存储**: 文件存储在阿里云OSS等云存储服务
- **路径映射**: 与本地存储保持相同的路径结构
- **动态路径**: 根据分类和文件类型动态生成OSS路径
- **缩略图处理**: 自动生成缩略图文件名（添加_thumb后缀）

## OSSService功能

### 核心功能
- **配置管理**: 支持OSS连接配置和验证
- **文件上传**: 支持分块上传，提升大文件上传效率
- **文件下载**: 支持文件下载和流处理
- **文件删除**: 支持单个和批量文件删除
- **连接测试**: 提供OSS连接状态检测

### 技术特点
- **S3兼容**: 使用AWS S3 SDK兼容OSS服务
- **分块上传**: 大文件自动分块上传，提升效率
- **错误重试**: 网络异常时自动重试
- **MIME类型**: 自动设置正确的文件MIME类型

## 自定义协议处理器

### electron://协议实现
- **统一访问**: 通过自定义协议统一处理本地和OSS文件访问
- **格式**: `electron://file/filename` 或 `electron://thumbnail/filename`
- **自动切换**: 根据当前存储模式自动选择文件获取方式
- **MIME支持**: 自动设置正确的MIME类型

### 处理流程
1. **URL解析**: 解析协议URL获取文件类型和文件名
2. **存储检测**: 根据设置确定当前存储模式
3. **文件获取**: 本地模式读取文件，OSS模式下载文件
4. **类型设置**: 根据文件扩展名设置MIME类型
5. **错误处理**: 文件不存在时返回适当的错误

## SettingsService配置管理

### 配置文件结构
- **存储路径**: 用户选择的存储根路径
- **存储类型**: 'local' | 'oss'
- **OSS配置**: OSS连接参数（可选）
- **首次设置**: 是否完成首次设置标记
- **同步配置**: 数据库同步相关设置

### 主要功能
- **配置读写**: 配置文件的读取和保存
- **默认设置**: 提供合理的默认配置
- **路径管理**: 存储路径的获取和验证
- **目录创建**: 自动创建必要的配置目录

## 批量导入功能

### FolderStructureValidator文件夹验证
- **结构检查**: 验证导入文件夹的结构是否符合要求
- **分类验证**: 检查分类文件夹名称和图片数量
- **文件格式**: 验证图片文件格式是否支持
- **错误报告**: 提供详细的验证错误和警告信息

### BatchImportService批量导入
- **导入模式**: 支持覆盖模式和追加模式
- **批量处理**: 分批处理大量图片，避免内存溢出
- **进度反馈**: 实时报告导入进度和状态
- **错误处理**: 部分失败不影响整体导入过程
- **性能优化**: 并发控制和内存管理

### 导入选项
- **导入模式**: 覆盖现有数据或追加到现有数据
- **文件名处理**: 保留原文件名或生成UUID文件名
- **分类名清理**: 可选的分类名称清理和规范化
- **并发控制**: 可配置的最大并发上传数
- **批次大小**: 可配置的批处理大小

## 数据库同步功能

### DatabaseSyncService数据库同步
- **备份到OSS**: 将数据库备份上传到OSS存储
- **从OSS恢复**: 从OSS下载备份并恢复数据库
- **备份列表**: 列出OSS中的所有数据库备份
- **进度反馈**: 备份和恢复过程的实时进度更新

### 同步特点
- **数据验证**: 备份前后的数据完整性验证
- **重试机制**: 网络异常时的自动重试
- **版本管理**: 支持多个备份版本的管理
- **安全检查**: 恢复前的备份文件格式验证

## 物种查询服务

### SpeciesService物种查询功能
- **数据加载**: 从JSON文件加载物种数据
- **索引构建**: 构建多维索引提升查询性能
- **智能搜索**: 支持精确匹配、前缀匹配、拼音匹配、N-gram匹配
- **分类查询**: 支持按科、属查询物种信息

### 查询算法
- **精确匹配**: 优先级最高，直接匹配物种名称
- **前缀匹配**: 匹配物种名称的前缀
- **拼音匹配**: 支持拼音全拼和首字母匹配
- **N-gram匹配**: 支持部分字符匹配
- **智能排序**: 根据匹配度智能排序结果

### 性能优化
- **内存索引**: 将数据和索引加载到内存中
- **多维索引**: 构建多种类型的索引提升查询速度
- **缓存机制**: 缓存查询结果避免重复计算
- **分页支持**: 支持结果分页避免一次返回过多数据

## 存储和服务最佳实践

### 1. 存储模式切换
- **无缝切换**: 支持运行时在本地和OSS存储间切换
- **数据一致性**: 切换时保持数据库记录和文件的一致性
- **错误恢复**: 存储操作失败时提供降级方案

### 2. 性能优化
- **分块上传**: 大文件使用分块上传提升效率
- **并发控制**: 限制并发操作数量防止资源耗尽
- **内存管理**: 及时释放大对象，控制内存使用

### 3. 数据安全
- **备份策略**: 定期自动备份数据库到OSS
- **数据验证**: 上传和下载时验证数据完整性
- **错误处理**: 完善的错误处理和恢复机制

### 4. 用户体验
- **进度反馈**: 长时间操作提供实时进度更新
- **批量处理**: 支持批量操作提升效率
- **智能搜索**: 多维度搜索算法提供精确结果

## 总结

Pokedex项目的存储和服务系统具有以下特点：

1. **灵活的存储架构**: 支持本地和OSS双存储模式的无缝切换
2. **高效的批量处理**: 优化的批量导入和数据同步机制
3. **智能的搜索服务**: 多维度物种查询算法提供精确结果
4. **完善的配置管理**: 统一的配置管理和用户友好的设置界面
5. **可靠的数据安全**: 完整的备份恢复机制和数据验证
