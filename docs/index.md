# 项目知识索引 (Project Knowledge Index)

**1. 项目概览 (Overview)**
- **项目名称:** pokedex
- **技术栈:** React 19, Electron 28, Vite 6, TypeScript 5.7, TailwindCSS 3.4, Better-SQLite3, <PERSON><PERSON>r Motion, <PERSON><PERSON>s, Zod
- **核心架构:** 基于 Electron 的主/渲染进程分离架构，使用 Context API 进行状态管理，支持本地存储和OSS对象存储
- **文档入口:** `README.md`
- **架构文档:** `docs/01_architecture.md`

**2. 前端组件 (Components)**
- `AlertDialog`: `docs/components/AlertDialog.md`
- `AnalyticsPage`: `docs/components/AnalyticsPage.md`
- `BirdSightingTimeline`: `docs/components/BirdSightingTimeline.md`
- `CategoryCard`: `docs/components/CategoryCard.md`
- `CategoryCardSkeleton`: `docs/components/CategoryCardSkeleton.md`
- `CategoryDetail`: `docs/components/CategoryDetail.md`
- `CategoryForm`: `docs/components/CategoryForm.md`
- `CategoryList`: `docs/components/CategoryList.md`
- `CategorySearch`: `docs/components/CategorySearch.md`
- `ChinaBirdMap`: `docs/components/ChinaBirdMap.md`
- `EnhancedAnimations`: `docs/components/EnhancedAnimations.md`
- `ErrorDisplay`: `docs/components/ErrorDisplay.md`
- `IOSInstallPrompt`: `docs/components/IOSInstallPrompt.md`
- `ImageCard`: `docs/components/ImageCard.md`
- `ImageCardSkeleton`: `docs/components/ImageCardSkeleton.md`
- `ImageDetailModal`: `docs/components/ImageDetailModal.md`
- `ImageUploadForm`: `docs/components/ImageUploadForm.md`
- `Layout`: `docs/components/Layout.md`
- `LoadingSpinner`: `docs/components/LoadingSpinner.md`
- `LoginPage`: `docs/components/LoginPage.md`
- `Modal`: `docs/components/Modal.md`
- `PieChart`: `docs/components/PieChart.md`
- `ShortcutsModal`: `docs/components/ShortcutsModal.md`
- `SpeciesDetailCard`: `docs/components/SpeciesDetailCard.md`
- `SpeciesSearch`: `docs/components/SpeciesSearch.md`
- `SplashScreen`: `docs/components/SplashScreen.md`
- `TagPage`: `docs/components/TagPage.md`
- `WelcomeGuide`: `docs/components/WelcomeGuide.md`
- `Icons`: `docs/components/Icons.md`
- `SplashComponents`: `docs/components/SplashComponents.md`

**3. 状态管理 (Contexts)**
- `AuthContext`: `docs/contexts/AuthContext.md`
- `CategoryContext`: `docs/contexts/CategoryContext.md`
- `ThemeContext`: `docs/contexts/ThemeContext.md`

**4. 进程间通信 (IPC Channels)**
- `test-database`: `docs/ipc/test-database.md`
- `get-database-stats`: `docs/ipc/get-database-stats.md`
- `reset-database`: `docs/ipc/reset-database.md`
- `get-categories`: `docs/ipc/get-categories.md`
- `create-category`: `docs/ipc/create-category.md`
- `update-category`: `docs/ipc/update-category.md`
- `delete-category`: `docs/ipc/delete-category.md`
- `get-category-by-id`: `docs/ipc/get-category-by-id.md`
- `get-category-with-images`: `docs/ipc/get-category-with-images.md`
- `upload-image`: `docs/ipc/upload-image.md`
- `get-image-by-id`: `docs/ipc/get-image-by-id.md`
- `update-image`: `docs/ipc/update-image.md`
- `delete-image`: `docs/ipc/delete-image.md`
- `delete-images`: `docs/ipc/delete-images.md`
- `validate-delete-conditions`: `docs/ipc/validate-delete-conditions.md`
- `get-image-path`: `docs/ipc/get-image-path.md`
- `get-thumbnail-path`: `docs/ipc/get-thumbnail-path.md`
- `get-all-tags`: `docs/ipc/get-all-tags.md`
- `create-tag`: `docs/ipc/create-tag.md`
- `update-tag`: `docs/ipc/update-tag.md`
- `delete-tag`: `docs/ipc/delete-tag.md`
- `get-tag-by-id`: `docs/ipc/get-tag-by-id.md`
- `search-tags`: `docs/ipc/search-tags.md`
- `add-tag-to-image`: `docs/ipc/add-tag-to-image.md`
- `remove-tag-from-image`: `docs/ipc/remove-tag-from-image.md`
- `get-tags-for-image`: `docs/ipc/get-tags-for-image.md`
- `search-images-by-tags`: `docs/ipc/search-images-by-tags.md`
- `show-delete-confirmation`: `docs/ipc/show-delete-confirmation.md`
- `show-delete-progress`: `docs/ipc/show-delete-progress.md`
- `get-storage-settings`: `docs/ipc/get-storage-settings.md`
- `update-storage-settings`: `docs/ipc/update-storage-settings.md`
- `select-directory`: `docs/ipc/select-directory.md`
- `migrate-storage-location`: `docs/ipc/migrate-storage-location.md`
- `get-oss-config`: `docs/ipc/get-oss-config.md`
- `update-oss-config`: `docs/ipc/update-oss-config.md`
- `test-oss-connection`: `docs/ipc/test-oss-connection.md`
- `switch-storage-type`: `docs/ipc/switch-storage-type.md`
- `backup-database-to-oss`: `docs/ipc/backup-database-to-oss.md`
- `list-database-backups`: `docs/ipc/list-database-backups.md`
- `restore-database-from-oss`: `docs/ipc/restore-database-from-oss.md`
- `validate-database-backup`: `docs/ipc/validate-database-backup.md`
- `delete-database-backup`: `docs/ipc/delete-database-backup.md`
- `rename-database-backup`: `docs/ipc/rename-database-backup.md`
- `get-database-sync-settings`: `docs/ipc/get-database-sync-settings.md`
- `update-database-sync-settings`: `docs/ipc/update-database-sync-settings.md`
- `validate-folder-structure`: `docs/ipc/validate-folder-structure.md`
- `import-from-folder`: `docs/ipc/import-from-folder.md`
- `select-import-folder`: `docs/ipc/select-import-folder.md`

**5. 原生功能封装 (Native APIs)**
- `DatabaseManager`: `docs/native/DatabaseManager.md`
- `CategoryService`: `docs/native/CategoryService.md`
- `ImageService`: `docs/native/ImageService.md`
- `TagService`: `docs/native/TagService.md`
- `OSSService`: `docs/native/OSSService.md`
- `SettingsService`: `docs/native/SettingsService.md`
- `DatabaseSyncService`: `docs/native/DatabaseSyncService.md`
- `BatchImportService`: `docs/native/BatchImportService.md`
- `FolderStructureValidator`: `docs/native/FolderStructureValidator.md`

**6. 工具函数 (Utils)**
- `animations`: `docs/utils/animations.md`
- `dataLoader`: `docs/utils/dataLoader.md`

**7. 数据验证 (Schemas)**
- `auth`: `docs/schemas/auth.md`
- `category`: `docs/schemas/category.md`
- `common`: `docs/schemas/common.md`
- `errors`: `docs/schemas/errors.md`
- `image`: `docs/schemas/image.md`
- `species`: `docs/schemas/species.md`
- `tag`: `docs/schemas/tag.md`

**8. 服务层 (Services)**
- `api`: `docs/services/api.md`
- `api-validation`: `docs/services/api-validation.md`
- `echarts`: `docs/services/echarts.md`

**9. 自定义钩子 (Hooks)**
- `useSplashAnimation`: `docs/hooks/useSplashAnimation.md`

**10. 类型定义 (Types)**
- `types`: `docs/types/types.md`
- `splash`: `docs/types/splash.md`
