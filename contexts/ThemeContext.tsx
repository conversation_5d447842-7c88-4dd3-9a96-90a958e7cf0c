import React, { createContext, useState, useContext, useEffect, useMemo } from 'react';

export type ThemeName = 'neonGalaxy' | 'arcadeFlash' | 'retroTechDark' | 'oceanDepth' | 'zen';

export interface ThemeStyling {
  name: string; // Display name
  bg: string;
  text: string;
  headerBg: string;
  headerText: string;
  navLink: string;
  navLinkActive: string;
  brandColor: string;
  button: {
    primary: string;
    primaryText: string;
    secondary: string;
    secondaryText: string;
    danger: string;
    dangerText: string;
    transition: string;
  };
  card: {
    bg: string;
    text: string;
    secondaryText: string;
    shadow: string;
    hoverShadow: string;
    rounded: string;
    border?: string;
    titleHover: string;
    transition: string;
  };
  modal: {
    bg: string;
    titleText: string;
    shadow: string;
    rounded: string;
  };
  input: {
    bg: string;
    border: string;
    focusRing: string;
    focusBorder: string;
    text: string;
    placeholderText: string;
    transition: string;
  };
  iconButton: string;
  bodyBg: string;
  footerText: string;
  footerHeartColor: string; // Added new property
  dropdown: {
    bg: string;
    itemText: string;
    itemHoverBg: string;
    itemHoverText: string;
    itemActiveBg: string;
    itemActiveText: string;
  };
  skeletonBase: string;
  skeletonHighlight: string;
}

export const themeSettings: Record<ThemeName, ThemeStyling> = {
  neonGalaxy: {
    name: 'Neon Galaxy',
    bg: 'bg-slate-950 dark:bg-black',
    text: 'text-slate-100 dark:text-white',
    headerBg: 'bg-slate-900/80 dark:bg-black/80 backdrop-blur-xl',
    headerText: 'text-white dark:text-white',
    navLink:
      'text-purple-300 dark:text-purple-400 hover:text-fuchsia-400 dark:hover:text-fuchsia-300 transition-all duration-300 hover:drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
    navLinkActive: 'text-fuchsia-400 dark:text-fuchsia-300 font-semibold drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
    brandColor: 'text-fuchsia-400 dark:text-fuchsia-300 drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
    button: {
      primary: 'bg-gradient-to-r from-fuchsia-500 to-cyan-400 hover:from-fuchsia-400 hover:to-cyan-300 focus-visible:outline-fuchsia-400 shadow-[0_0_20px_rgba(217,70,239,0.5)] hover:shadow-[0_0_30px_rgba(217,70,239,0.8)]',
      primaryText: 'text-black font-bold',
      secondary:
        'bg-gradient-to-r from-purple-800/80 to-indigo-800/80 hover:from-purple-700/90 hover:to-indigo-700/90 focus-visible:outline-purple-500 border border-purple-500/50 shadow-[0_0_15px_rgba(147,51,234,0.3)]',
      secondaryText: 'text-purple-100 dark:text-purple-200 font-medium',
      danger: 'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-400 hover:to-rose-400 focus-visible:outline-pink-500 shadow-[0_0_20px_rgba(236,72,153,0.5)]',
      dangerText: 'text-white font-bold',
      transition:
        'transition-all duration-300 ease-out transform active:scale-95 hover:scale-105',
    },
    card: {
      bg: 'bg-slate-900/70 dark:bg-slate-950/70 backdrop-blur-md',
      text: 'text-slate-100 dark:text-white',
      secondaryText: 'text-purple-300 dark:text-purple-400',
      shadow: 'shadow-2xl shadow-purple-900/50 dark:shadow-black/80',
      hoverShadow: 'hover:shadow-2xl hover:shadow-fuchsia-500/30 dark:hover:shadow-fuchsia-400/30 hover:shadow-[0_0_40px_rgba(217,70,239,0.3)]',
      rounded: 'rounded-2xl',
      border: 'border border-fuchsia-500/30 dark:border-fuchsia-400/30 shadow-[inset_0_0_20px_rgba(217,70,239,0.1)]',
      titleHover: 'group-hover:text-fuchsia-400 dark:group-hover:text-fuchsia-300 group-hover:drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
      transition: 'transition-all duration-500 ease-out hover:-translate-y-2',
    },
    modal: {
      bg: 'bg-slate-900/95 dark:bg-slate-950/95 backdrop-blur-xl',
      titleText: 'text-white dark:text-white',
      shadow: 'shadow-2xl shadow-fuchsia-900/50 shadow-[0_0_50px_rgba(217,70,239,0.3)]',
      rounded: 'rounded-2xl',
    },
    input: {
      bg: 'bg-slate-800/60 dark:bg-slate-900/60 backdrop-blur-sm',
      border: 'border-purple-500/50 dark:border-purple-400/50',
      focusRing: 'focus:ring-2 focus:ring-fuchsia-400/50 dark:focus:ring-fuchsia-300/50 focus:shadow-[0_0_20px_rgba(217,70,239,0.3)]',
      focusBorder: 'focus:border-fuchsia-400 dark:focus:border-fuchsia-300',
      text: 'text-white dark:text-white',
      placeholderText: 'placeholder-purple-400 dark:placeholder-purple-300',
      transition: 'transition-all duration-300 ease-out',
    },
    iconButton:
      'text-purple-400 hover:text-fuchsia-400 dark:text-purple-300 dark:hover:text-fuchsia-300 transition-all duration-300 hover:scale-110 hover:drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
    bodyBg: 'bg-slate-950 dark:bg-black',
    footerText: 'text-purple-400 dark:text-purple-300',
    footerHeartColor: 'text-fuchsia-400 dark:text-fuchsia-300 drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
    dropdown: {
      bg: 'bg-slate-900/90 dark:bg-slate-950/90 backdrop-blur-xl border border-purple-500/30',
      itemText: 'text-purple-200 dark:text-purple-300',
      itemHoverBg: 'hover:bg-purple-800/50 dark:hover:bg-purple-900/50',
      itemHoverText: 'hover:text-fuchsia-300 dark:hover:text-fuchsia-200 hover:drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
      itemActiveBg: 'bg-gradient-to-r from-fuchsia-500/20 to-cyan-400/20 dark:from-fuchsia-400/20 dark:to-cyan-300/20',
      itemActiveText: 'text-fuchsia-300 dark:text-fuchsia-200 font-medium drop-shadow-[0_0_8px_rgba(217,70,239,0.8)]',
    },
    skeletonBase: 'bg-slate-800 dark:bg-slate-900',
    skeletonHighlight: 'shimmer-gradient-galaxy',
  },
  arcadeFlash: {
    name: 'Arcade Flash',
    bg: 'bg-slate-50 dark:bg-zinc-900',
    text: 'text-black dark:text-white',
    headerBg: 'bg-yellow-400 dark:bg-yellow-500',
    headerText: 'text-black dark:text-black',
    navLink:
      'text-black dark:text-black hover:bg-yellow-500 dark:hover:bg-yellow-600 transition-all duration-150',
    navLinkActive:
      'text-black dark:text-black font-bold bg-red-500 dark:bg-red-600 underline decoration-blue-500 decoration-2 underline-offset-2',
    brandColor: 'text-black dark:text-black',
    button: {
      primary:
        'bg-red-500 hover:bg-red-600 focus-visible:outline-red-600 border-2 border-black dark:border-red-300',
      primaryText: 'text-white font-bold',
      secondary:
        'bg-blue-500 hover:bg-blue-600 focus-visible:outline-blue-600 border-2 border-black dark:border-blue-300',
      secondaryText: 'text-white font-bold',
      danger:
        'bg-orange-500 hover:bg-orange-600 focus-visible:outline-orange-600 border-2 border-black dark:border-orange-300',
      dangerText: 'text-black font-bold',
      transition:
        'transition-all duration-150 ease-out transform active:translate-y-0.5 active:translate-x-0.5',
    },
    card: {
      bg: 'bg-white dark:bg-zinc-800',
      text: 'text-black dark:text-white',
      secondaryText: 'text-slate-600 dark:text-zinc-400',
      shadow: 'shadow-[2px_2px_0px_#A0A0A0] dark:shadow-[2px_2px_0px_#4A4A4A]',
      hoverShadow: 'hover:shadow-[4px_4px_0px_#000000] dark:hover:shadow-[4px_4px_0px_#FFFF00]',
      rounded: 'rounded', // Sharp corners
      border: 'border-2 border-black dark:border-zinc-500',
      titleHover: 'group-hover:text-red-500 dark:group-hover:text-red-400',
      transition: 'transition-all duration-150 ease-out',
    },
    modal: {
      bg: 'bg-slate-100 dark:bg-zinc-800',
      titleText: 'text-black dark:text-white',
      shadow: 'shadow-xl border-2 border-black dark:border-yellow-400',
      rounded: 'rounded',
    },
    input: {
      bg: 'bg-white dark:bg-zinc-700',
      border: 'border-2 border-slate-500 dark:border-zinc-600',
      focusRing: 'focus:ring-4 focus:ring-yellow-400/50 dark:focus:ring-yellow-300/50',
      focusBorder: 'focus:border-black dark:focus:border-yellow-400',
      text: 'text-black dark:text-white',
      placeholderText: 'placeholder-slate-500 dark:placeholder-zinc-400',
      transition: 'transition-colors duration-150 ease-in-out',
    },
    iconButton:
      'text-slate-700 hover:text-red-500 dark:text-zinc-300 dark:hover:text-red-400 transition-colors duration-150',
    bodyBg: 'bg-slate-50 dark:bg-zinc-900',
    footerText: 'text-black dark:text-black',
    footerHeartColor: 'text-red-500 dark:text-red-400', // Arcade theme's red
    dropdown: {
      bg: 'bg-white dark:bg-zinc-800 border-2 border-black dark:border-yellow-500',
      itemText: 'text-black dark:text-white',
      itemHoverBg: 'hover:bg-yellow-300 dark:hover:bg-yellow-600/50',
      itemHoverText: 'hover:text-black dark:hover:text-white',
      itemActiveBg: 'bg-red-500 dark:bg-red-600',
      itemActiveText: 'text-white dark:text-white font-bold',
    },
    skeletonBase: 'bg-slate-300 dark:bg-zinc-700',
    skeletonHighlight: 'shimmer-gradient-arcade',
  },
  retroTechDark: {
    name: 'RetroTech Dark',
    bg: 'bg-[#1A1A2E]',
    text: 'text-white',
    headerBg: 'bg-[#151515]/90 backdrop-blur-md',
    headerText: 'text-white',
    navLink: 'text-zinc-400 hover:text-emerald-400',
    navLinkActive: 'text-emerald-400 font-semibold',
    brandColor: 'text-emerald-400',
    button: {
      primary: 'bg-emerald-400 hover:bg-emerald-500 focus-visible:outline-emerald-400',
      primaryText: 'text-black',
      secondary: 'bg-transparent border border-white hover:bg-white/10 focus-visible:outline-white',
      secondaryText: 'text-white',
      danger: 'bg-red-700 hover:bg-red-800 focus-visible:outline-red-600',
      dangerText: 'text-white',
      transition: 'transition-all duration-200 ease-in-out transform active:scale-95',
    },
    card: {
      bg: 'bg-[#151515]',
      text: 'text-white',
      secondaryText: 'text-zinc-400',
      shadow: 'shadow-[2px_2px_0px_#2EE59D]', // Mint Green Accent Shadow
      hoverShadow: 'hover:shadow-[4px_4px_0px_#6EE7B7]', // Lighter Mint Green Accent Hover Shadow
      rounded: 'rounded-lg',
      border: 'border border-slate-700',
      titleHover: 'group-hover:text-emerald-400',
      transition: 'transition-all duration-300 ease-in-out',
    },
    modal: {
      bg: 'bg-[#151515]',
      titleText: 'text-white',
      shadow: 'shadow-2xl shadow-black/50',
      rounded: 'rounded-lg',
    },
    input: {
      bg: 'bg-slate-800',
      border: 'border-slate-600',
      focusRing: 'focus:ring-2 focus:ring-emerald-400/60',
      focusBorder: 'focus:border-emerald-400',
      text: 'text-white',
      placeholderText: 'placeholder-zinc-500',
      transition: 'transition-colors duration-200 ease-in-out',
    },
    iconButton: 'text-zinc-300 hover:text-emerald-400',
    bodyBg: 'bg-[#1A1A2E]',
    footerText: 'text-zinc-400',
    footerHeartColor: 'text-amber-600',
    dropdown: {
      bg: 'bg-slate-800',
      itemText: 'text-zinc-300',
      itemHoverBg: 'hover:bg-slate-700',
      itemHoverText: 'hover:text-emerald-400',
      itemActiveBg: 'bg-emerald-400/20',
      itemActiveText: 'text-emerald-400 font-medium',
    },
    skeletonBase: 'bg-slate-800',
    skeletonHighlight: 'shimmer-gradient-galaxy',
  },
  oceanDepth: {
    name: 'Ocean Depth',
    bg: 'bg-sky-50 dark:bg-slate-900',
    text: 'text-slate-800 dark:text-cyan-100',
    headerBg: 'bg-cyan-100/90 dark:bg-slate-800/90 backdrop-blur-lg',
    headerText: 'text-teal-800 dark:text-cyan-200',
    navLink:
      'text-teal-600 dark:text-cyan-300 hover:text-blue-600 dark:hover:text-cyan-100 transition-all duration-400 hover:drop-shadow-[0_0_6px_rgba(34,211,238,0.6)]',
    navLinkActive: 'text-blue-600 dark:text-cyan-100 font-semibold drop-shadow-[0_0_6px_rgba(34,211,238,0.6)]',
    brandColor: 'text-teal-700 dark:text-cyan-200 drop-shadow-[0_0_6px_rgba(34,211,238,0.4)]',
    button: {
      primary: 'bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-400 hover:to-blue-500 focus-visible:outline-teal-500 shadow-lg shadow-teal-500/30 hover:shadow-xl hover:shadow-blue-500/40',
      primaryText: 'text-white font-medium',
      secondary:
        'bg-cyan-100 hover:bg-cyan-200 dark:bg-slate-700 dark:hover:bg-slate-600 focus-visible:outline-cyan-400 border border-cyan-300 dark:border-slate-600',
      secondaryText: 'text-teal-700 dark:text-cyan-200',
      danger: 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-400 hover:to-pink-400 focus-visible:outline-red-500 shadow-lg shadow-red-500/30',
      dangerText: 'text-white font-medium',
      transition: 'transition-all duration-400 ease-out transform active:scale-95 hover:scale-105',
    },
    card: {
      bg: 'bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm',
      text: 'text-slate-800 dark:text-cyan-100',
      secondaryText: 'text-teal-600 dark:text-cyan-300',
      shadow: 'shadow-lg shadow-cyan-200/40 dark:shadow-slate-900/60',
      hoverShadow: 'hover:shadow-xl hover:shadow-teal-300/50 dark:hover:shadow-cyan-500/20 hover:shadow-[0_0_30px_rgba(34,211,238,0.2)]',
      rounded: 'rounded-3xl',
      border: 'border border-cyan-200/60 dark:border-slate-700/60',
      titleHover: 'group-hover:text-teal-600 dark:group-hover:text-cyan-200 group-hover:drop-shadow-[0_0_6px_rgba(34,211,238,0.6)]',
      transition: 'transition-all duration-500 ease-out hover:-translate-y-2 hover:rotate-1',
    },
    modal: {
      bg: 'bg-cyan-50/95 dark:bg-slate-800/95 backdrop-blur-xl',
      titleText: 'text-teal-800 dark:text-cyan-200',
      shadow: 'shadow-2xl shadow-teal-500/25 dark:shadow-cyan-500/20',
      rounded: 'rounded-3xl',
    },
    input: {
      bg: 'bg-cyan-50 dark:bg-slate-700/60',
      border: 'border-cyan-300 dark:border-slate-600',
      focusRing: 'focus:ring-2 focus:ring-teal-400/40 dark:focus:ring-cyan-400/40 focus:shadow-[0_0_15px_rgba(34,211,238,0.3)]',
      focusBorder: 'focus:border-teal-400 dark:focus:border-cyan-400',
      text: 'text-slate-900 dark:text-cyan-100',
      placeholderText: 'placeholder-teal-400 dark:placeholder-cyan-400',
      transition: 'transition-all duration-400 ease-out',
    },
    iconButton:
      'text-teal-500 hover:text-blue-600 dark:text-cyan-300 dark:hover:text-cyan-100 transition-all duration-400 hover:scale-110 hover:drop-shadow-[0_0_8px_rgba(34,211,238,0.6)]',
    bodyBg: 'bg-sky-50 dark:bg-slate-900',
    footerText: 'text-teal-600 dark:text-cyan-300',
    footerHeartColor: 'text-blue-500 dark:text-cyan-400 drop-shadow-[0_0_6px_rgba(34,211,238,0.6)]',
    dropdown: {
      bg: 'bg-cyan-50/90 dark:bg-slate-800/90 backdrop-blur-xl border border-cyan-200/50 dark:border-slate-700/50',
      itemText: 'text-teal-700 dark:text-cyan-200',
      itemHoverBg: 'hover:bg-cyan-100 dark:hover:bg-slate-700',
      itemHoverText: 'hover:text-blue-600 dark:hover:text-cyan-100 hover:drop-shadow-[0_0_6px_rgba(34,211,238,0.6)]',
      itemActiveBg: 'bg-gradient-to-r from-teal-100 to-blue-100 dark:from-teal-900/30 dark:to-blue-900/30',
      itemActiveText: 'text-blue-600 dark:text-cyan-100 font-medium drop-shadow-[0_0_6px_rgba(34,211,238,0.6)]',
    },
    skeletonBase: 'bg-cyan-200 dark:bg-slate-700',
    skeletonHighlight: 'shimmer-gradient-ocean',
  },
  zen: {
    name: 'Zen',
    bg: 'bg-gray-50 dark:bg-gray-950',
    text: 'text-gray-800 dark:text-gray-200',
    headerBg: 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl',
    headerText: 'text-gray-900 dark:text-gray-100',
    navLink:
      'text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-all duration-500 hover:drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
    navLinkActive: 'text-pink-600 dark:text-pink-400 font-medium drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
    brandColor: 'text-gray-800 dark:text-gray-200',
    button: {
      primary: 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 focus-visible:outline-gray-700 shadow-md hover:shadow-lg',
      primaryText: 'text-white font-normal',
      secondary:
        'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 focus-visible:outline-gray-400 border border-gray-300 dark:border-gray-700',
      secondaryText: 'text-gray-700 dark:text-gray-300',
      danger: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-400 hover:to-red-500 focus-visible:outline-red-500 shadow-md',
      dangerText: 'text-white font-normal',
      transition: 'transition-all duration-500 ease-out transform active:scale-98',
    },
    card: {
      bg: 'bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm',
      text: 'text-gray-800 dark:text-gray-200',
      secondaryText: 'text-gray-600 dark:text-gray-400',
      shadow: 'shadow-sm shadow-gray-200/60 dark:shadow-gray-950/40',
      hoverShadow: 'hover:shadow-md hover:shadow-gray-300/70 dark:hover:shadow-gray-950/60',
      rounded: 'rounded-lg',
      border: 'border border-gray-200/80 dark:border-gray-800/80',
      titleHover: 'group-hover:text-pink-600 dark:group-hover:text-pink-400 group-hover:drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
      transition: 'transition-all duration-600 ease-out hover:-translate-y-0.5',
    },
    modal: {
      bg: 'bg-white/98 dark:bg-gray-900/98 backdrop-blur-2xl',
      titleText: 'text-gray-900 dark:text-gray-100',
      shadow: 'shadow-xl shadow-gray-400/20 dark:shadow-black/30',
      rounded: 'rounded-lg',
    },
    input: {
      bg: 'bg-gray-50 dark:bg-gray-800/50',
      border: 'border-gray-300 dark:border-gray-700',
      focusRing: 'focus:ring-1 focus:ring-pink-400/30 dark:focus:ring-pink-400/30',
      focusBorder: 'focus:border-pink-400 dark:focus:border-pink-400',
      text: 'text-gray-900 dark:text-gray-100',
      placeholderText: 'placeholder-gray-500 dark:placeholder-gray-500',
      transition: 'transition-all duration-500 ease-out',
    },
    iconButton:
      'text-gray-500 hover:text-pink-600 dark:text-gray-400 dark:hover:text-pink-400 transition-all duration-500 hover:scale-105 hover:drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
    bodyBg: 'bg-gray-50 dark:bg-gray-950',
    footerText: 'text-gray-500 dark:text-gray-400',
    footerHeartColor: 'text-pink-500 dark:text-pink-400 drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
    dropdown: {
      bg: 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-2xl border border-gray-200/70 dark:border-gray-800/70',
      itemText: 'text-gray-700 dark:text-gray-300',
      itemHoverBg: 'hover:bg-gray-100 dark:hover:bg-gray-800',
      itemHoverText: 'hover:text-pink-600 dark:hover:text-pink-400 hover:drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
      itemActiveBg: 'bg-pink-50 dark:bg-pink-900/20',
      itemActiveText: 'text-pink-600 dark:text-pink-400 font-medium drop-shadow-[0_0_4px_rgba(244,114,182,0.5)]',
    },
    skeletonBase: 'bg-gray-200 dark:bg-gray-800',
    skeletonHighlight: 'shimmer-gradient-zen',
  },
};

interface ThemeContextType {
  themeName: ThemeName;
  theme: ThemeStyling;
  setThemeName: (themeName: ThemeName) => void;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const defaultThemeName: ThemeName = 'retroTechDark';

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [themeName, setThemeNameState] = useState<ThemeName>(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem('appTheme') as ThemeName;
      return Object.keys(themeSettings).includes(storedTheme) ? storedTheme : defaultThemeName;
    }
    return defaultThemeName;
  });

  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem('appTheme') as ThemeName;
      if (storedTheme === 'retroTechDark') {
        return true;
      }

      return (
        localStorage.getItem('darkMode') === 'true' ||
        (!('darkMode' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)
      );
    }
    return false;
  });

  useEffect(() => {
    localStorage.setItem('appTheme', themeName);
    document.body.className = 'antialiased';
    document.body.classList.add(...themeSettings[themeName].bodyBg.split(' '));

    if (themeName === 'retroTechDark') {
      document.documentElement.classList.add('dark');
    } else {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, [themeName, isDarkMode]);

  useEffect(() => {
    if (themeName === 'retroTechDark') {
      if (!isDarkMode) {
        setIsDarkMode(true);
      }
      document.documentElement.classList.add('dark');
      localStorage.setItem('darkMode', 'true');
    } else {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('darkMode', 'true');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('darkMode', 'false');
      }
    }
  }, [isDarkMode, themeName]);

  const toggleDarkMode = () => {
    if (themeName !== 'retroTechDark') {
      setIsDarkMode(!isDarkMode);
    }
  };

  const setThemeNameInternal = (newThemeName: ThemeName) => {
    setThemeNameState(newThemeName);
    if (newThemeName === 'retroTechDark') {
      setIsDarkMode(true);
    }
  };

  const currentThemeStyles = useMemo(() => themeSettings[themeName], [themeName]);

  return (
    <ThemeContext.Provider
      value={{
        themeName,
        theme: currentThemeStyles,
        setThemeName: setThemeNameInternal,
        isDarkMode,
        toggleDarkMode,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
