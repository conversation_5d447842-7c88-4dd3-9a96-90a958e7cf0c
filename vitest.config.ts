import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    environment: 'jsdom',          // 默认使用jsdom环境（适合前端组件）
    globals: true,                 // 启用全局测试API (describe, it, expect)
    setupFiles: ['__tests__/setup/test-setup.ts'], // 测试设置文件
    coverage: {
      provider: 'v8',              // 使用V8覆盖率引擎
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'dist-electron/',
        '**/*.test.ts',
        '**/__tests__/**'
      ]
    },
    testTimeout: 10000,            // 10秒测试超时
    include: ['__tests__/**/*.test.{ts,tsx}'],
    exclude: ['node_modules/', 'dist/', 'dist-electron/'],
    // 禁用并发执行以避免数据库竞争
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    },
    // 增加重试次数以处理偶发性失败
    retry: 2,
    // 根据文件路径配置环境
    environmentMatchGlobs: [
      // Electron测试使用node环境
      ['__tests__/electron/**/*.test.{ts,tsx}', 'node'],
      // 前端组件和服务测试使用jsdom环境
      ['__tests__/components/**/*.test.{ts,tsx}', 'jsdom'],
      ['__tests__/services/**/*.test.{ts,tsx}', 'jsdom'],
      ['__tests__/integration/**/*.test.{ts,tsx}', 'jsdom']
    ]
  }
})