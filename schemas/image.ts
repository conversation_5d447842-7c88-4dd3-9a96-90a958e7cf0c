import { z } from 'zod';
import { 
  UuidSchema, 
  DateTimeSchema, 
  OptionalStringSchema, 
  OptionalDateTimeSchema,
  FileSizeSchema,
  RecordSchema,
  BooleanStringSchema
} from './common';
import { TagReadSchema } from './tag';

// 兼容本地和远程的ID Schema
const FlexibleIdSchema = z.string().min(1, 'ID不能为空');

/**
 * 图片相关的 Schema 定义
 */

// EXIF 数据 Schema
export const ExifDataSchema = z.object({
  make: OptionalStringSchema.optional(),                    // 制造商
  model: OptionalStringSchema.optional(),                   // 型号
  lens_make: OptionalStringSchema.optional(),               // 镜头制造商
  bits_per_sample: OptionalStringSchema.optional(),         // 每样本位数
  date_time_original: OptionalStringSchema.optional(),      // 原始拍摄时间
  exposure_time: OptionalStringSchema.optional(),           // 曝光时间
  f_number: OptionalStringSchema.optional(),                // 光圈值
  exposure_program: OptionalStringSchema.optional(),        // 曝光程序
  iso_speed_rating: OptionalStringSchema.optional(),        // ISO感光度
  focal_length: OptionalStringSchema.optional(),            // 焦距
  lens_specification: OptionalStringSchema.optional(),       // 镜头规格
  lens_model: OptionalStringSchema.optional(),              // 镜头型号
  exposure_mode: OptionalStringSchema.optional(),           // 曝光模式
  cfa_pattern: OptionalStringSchema.optional(),             // CFA模式
  color_space: OptionalStringSchema.optional(),             // 色彩空间
  white_balance: OptionalStringSchema.optional(),           // 白平衡
});

// 图片读取 Schema
export const ImageReadSchema = z.object({
  id: FlexibleIdSchema,
  category_id: FlexibleIdSchema,
  title: OptionalStringSchema,
  original_filename: OptionalStringSchema,
  stored_filename: OptionalStringSchema,
  relative_file_path: OptionalStringSchema,
  relative_thumbnail_path: OptionalStringSchema,
  mime_type: OptionalStringSchema,
  size_bytes: FileSizeSchema.nullable(),
  description: OptionalStringSchema,
  tags: z.array(TagReadSchema).optional(),
  created_at: DateTimeSchema,
  updated_at: OptionalDateTimeSchema,
  file_metadata: RecordSchema,
  exif_info: ExifDataSchema.nullable(),
  image_url: z.string(), // 可能是相对路径，不强制要求URL格式
  thumbnail_url: OptionalStringSchema,
  set_as_category_thumbnail: z.boolean().nullable().optional(), // 添加这个字段
});

// 图片更新 Schema - 所有字段都是可选的，支持部分更新
export const ImageUpdateSchema = z.object({
  title: OptionalStringSchema,
  description: OptionalStringSchema,
  tags: OptionalStringSchema, // 逗号分隔的字符串
  category_id: FlexibleIdSchema.nullable().optional(), // 可选字段，可以不传
  set_as_category_thumbnail: z.boolean().nullable().optional(), // 可选字段，可以不传
}).partial(); // 使所有字段都变为可选，支持部分更新

// 创建一个兼容的File类型检查器
const FileSchema = typeof File !== 'undefined'
  ? z.instanceof(File).refine(file => file instanceof File, '请选择一个文件')
  : z.any().refine(() => true, '文件验证在服务端跳过');

// 图片上传 Schema (用于表单验证)
export const BodyUploadImageSchema = z.object({
  file: FileSchema,
  category_id: FlexibleIdSchema,
  title: OptionalStringSchema,
  description: OptionalStringSchema,
  tags: OptionalStringSchema, // 逗号分隔的字符串
  set_as_category_thumbnail: z.boolean().nullable(),
});

// 图片上传 FormData Schema (用于API验证)
export const ImageUploadFormDataSchema = z.object({
  file: FileSchema,
  category_id: FlexibleIdSchema,
  title: z.string().optional(),
  description: z.string().optional(),
  tags: z.string().optional(),
  set_as_category_thumbnail: BooleanStringSchema.optional(),
});

// 图片列表响应 Schema
export const ImageListResponseSchema = z.array(ImageReadSchema);

/**
 * 图片相关类型定义
 */
export type ExifData = z.infer<typeof ExifDataSchema>;
export type ImageRead = z.infer<typeof ImageReadSchema>;
export type ImageUpdate = z.infer<typeof ImageUpdateSchema>;
export type BodyUploadImage = z.infer<typeof BodyUploadImageSchema>;
export type ImageUploadFormData = z.infer<typeof ImageUploadFormDataSchema>;
export type ImageListResponse = z.infer<typeof ImageListResponseSchema>;

/**
 * 图片相关验证函数
 */

// 验证图片读取数据
export function validateImageRead(data: unknown): ImageRead {
  return ImageReadSchema.parse(data);
}

// 验证图片更新数据
export function validateImageUpdate(data: unknown): ImageUpdate {
  return ImageUpdateSchema.parse(data);
}

// 验证图片上传数据
export function validateImageUpload(data: unknown): BodyUploadImage {
  return BodyUploadImageSchema.parse(data);
}

// 验证图片列表数据
export function validateImageList(data: unknown): ImageListResponse {
  return ImageListResponseSchema.parse(data);
}

// 验证EXIF数据
export function validateExifData(data: unknown): ExifData {
  return ExifDataSchema.parse(data);
}

// 验证图片文件
export function validateImageFile(file: any): any {
  // 在Node.js环境中跳过File对象验证
  if (typeof File === 'undefined') {
    return file;
  }

  // 浏览器环境中的验证
  if (!(file instanceof File)) {
    throw new Error('必须是File对象');
  }

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    throw new Error('文件必须是图片格式');
  }

  // 验证文件大小 (最大 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    throw new Error('文件大小不能超过 10MB');
  }

  return file;
}