import { z } from 'zod';
import { NonEmptyStringSchema, DateTimeSchema } from './common';

// 兼容本地和远程的ID Schema - 可以是UUID或简单字符串
const FlexibleIdSchema = z.string().min(1, 'ID不能为空');

/**
 * 观鸟数据相关的 Schema 定义
 * 基于 BirdSightingService 的接口
 */

// Excel 鸟类数据 Schema（解析原始Excel文件时使用）
export const ExcelBirdDataSchema = z.object({
  鸟种编号: z.string(),
  中文名: z.string(),
  拉丁名: z.string(),
  英文名: z.string(),
  目: z.string(),
  科: z.string(),
  定点记报告: z.string(),
  随手记报告: z.string(),
  记录次数: z.string(),
  记录时间: z.string(),
});

// 观鸟记录 Schema（数据库存储格式）
export const BirdSightingRecordSchema = z.object({
  id: FlexibleIdSchema,
  chineseName: NonEmptyStringSchema,
  latinName: NonEmptyStringSchema,
  englishName: NonEmptyStringSchema,
  order: NonEmptyStringSchema,
  family: NonEmptyStringSchema,
  count: z.number().int().nonnegative(),
  recordDate: z.string().min(1, '记录时间不能为空'),
});

// 解析结果 Schema
export const ParseResultSchema = z.object({
  success: z.boolean(),
  data: z.array(BirdSightingRecordSchema).optional(),
  error: z.string().optional(),
});

// 验证结果 Schema
export const ValidationResultSchema = z.object({
  isValid: z.boolean(),
  errors: z.array(z.string()),
});

// 数据库操作结果 Schema
export const DatabaseResultSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
});

// 观鸟统计信息 Schema
export const BirdSightingStatsSchema = z.object({
  totalRecords: z.number().int().nonnegative(),
  totalSpecies: z.number().int().nonnegative(),
  totalObservations: z.number().int().nonnegative(),
  latestRecord: z.string().optional(),
});

// 上传结果 Schema
export const UploadResultSchema = z.object({
  success: z.boolean(),
  data: z.object({
    importedCount: z.number().int().nonnegative(),
    totalCount: z.number().int().nonnegative(),
  }).optional(),
  error: z.string().optional(),
});

// 观鸟记录列表响应 Schema
export const BirdSightingListResponseSchema = z.array(BirdSightingRecordSchema);

/**
 * 观鸟数据相关类型定义
 */
export type ExcelBirdData = z.infer<typeof ExcelBirdDataSchema>;
export type BirdSightingRecord = z.infer<typeof BirdSightingRecordSchema>;
export type ParseResult = z.infer<typeof ParseResultSchema>;
export type ValidationResult = z.infer<typeof ValidationResultSchema>;
export type DatabaseResult = z.infer<typeof DatabaseResultSchema>;
export type BirdSightingStats = z.infer<typeof BirdSightingStatsSchema>;
export type UploadResult = z.infer<typeof UploadResultSchema>;
export type BirdSightingListResponse = z.infer<typeof BirdSightingListResponseSchema>;

/**
 * 观鸟数据相关验证函数
 */

// 验证观鸟记录数据
export function validateBirdSightingRecord(data: unknown): BirdSightingRecord {
  return BirdSightingRecordSchema.parse(data);
}

// 验证观鸟记录列表
export function validateBirdSightingList(data: unknown): BirdSightingListResponse {
  return BirdSightingListResponseSchema.parse(data);
}

// 验证观鸟统计信息
export function validateBirdSightingStats(data: unknown): BirdSightingStats {
  return BirdSightingStatsSchema.parse(data);
}

// 验证上传结果
export function validateUploadResult(data: unknown): UploadResult {
  return UploadResultSchema.parse(data);
}

// 验证解析结果
export function validateParseResult(data: unknown): ParseResult {
  return ParseResultSchema.parse(data);
}

// 验证Excel鸟类数据
export function validateExcelBirdData(data: unknown): ExcelBirdData {
  return ExcelBirdDataSchema.parse(data);
}