import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { searchSpecies } from '../services/species';
import type { SpeciesDictionaryEntry } from '../schemas/speciesDictionary';

interface CategorySuggestionsProps {
  value: string;
  onChange: (value: string) => void;
  onSelect: (suggestion: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
  required?: boolean;
  maxLength?: number;
}

interface SearchState {
  suggestions: SpeciesDictionaryEntry[];
  loading: boolean;
  error: string | null;
  showSuggestions: boolean;
  highlightedIndex: number;
}

const CategorySuggestions: React.FC<CategorySuggestionsProps> = ({
  value,
  onChange,
  onSelect,
  placeholder = '请输入分类名称',
  disabled = false,
  className = '',
  id,
  required = false,
  maxLength,
}) => {
  const { theme } = useTheme();
  const [searchState, setSearchState] = useState<SearchState>({
    suggestions: [],
    loading: false,
    error: null,
    showSuggestions: false,
    highlightedIndex: -1,
  });

  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLUListElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Debounced search function
  const debouncedSearch = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setSearchState(prev => ({
          ...prev,
          suggestions: [],
          showSuggestions: false,
          loading: false,
          error: null,
        }));
        return;
      }

      // Cancel previous search
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      setSearchState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        const result = await searchSpecies({
          query: query.trim(),
          limit: 20,
        });

        // Check if this search was cancelled
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        setSearchState(prev => ({
          ...prev,
          suggestions: result?.species || [],
          showSuggestions: true,
          loading: false,
          error: null,
          highlightedIndex: -1,
        }));
      } catch (error) {
        // Check if this search was cancelled
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        console.error('Search error:', error);
        setSearchState(prev => ({
          ...prev,
          suggestions: [],
          showSuggestions: true,
          loading: false,
          error: '搜索失败，请重试',
        }));
      }
    },
    []
  );

  // Handle input change
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      onChange(newValue);

      // Clear existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Set new timeout for debounced search
      searchTimeoutRef.current = setTimeout(() => {
        debouncedSearch(newValue);
      }, 350);
    },
    [onChange, debouncedSearch]
  );

  // Handle input focus
  const handleInputFocus = useCallback(() => {
    if (searchState.suggestions.length > 0) {
      setSearchState(prev => ({ ...prev, showSuggestions: true }));
    }
  }, [searchState.suggestions.length]);

  // Handle input blur
  const handleInputBlur = useCallback(() => {
    // Delay hiding suggestions to allow for clicking
    setTimeout(() => {
      setSearchState(prev => ({ ...prev, showSuggestions: false }));
    }, 200);
  }, []);

  // Handle suggestion click
  const handleSuggestionClick = useCallback(
    (suggestion: SpeciesDictionaryEntry) => {
      onSelect(suggestion.种);
      setSearchState(prev => ({ ...prev, showSuggestions: false }));
    },
    [onSelect]
  );

  // Handle suggestion hover
  const handleSuggestionHover = useCallback((index: number) => {
    setSearchState(prev => ({ ...prev, highlightedIndex: index }));
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!searchState.showSuggestions || searchState.suggestions.length === 0) {
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSearchState(prev => ({
            ...prev,
            highlightedIndex: (prev.highlightedIndex + 1) % prev.suggestions.length,
          }));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSearchState(prev => ({
            ...prev,
            highlightedIndex:
              prev.highlightedIndex <= 0
                ? prev.suggestions.length - 1
                : prev.highlightedIndex - 1,
          }));
          break;
        case 'Enter':
          e.preventDefault();
          if (searchState.highlightedIndex >= 0) {
            const selectedSuggestion = searchState.suggestions[searchState.highlightedIndex];
            handleSuggestionClick(selectedSuggestion);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setSearchState(prev => ({ ...prev, showSuggestions: false }));
          break;
      }
    },
    [searchState.showSuggestions, searchState.suggestions, searchState.highlightedIndex, handleSuggestionClick]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Scroll highlighted item into view
  useEffect(() => {
    if (searchState.highlightedIndex >= 0 && listRef.current) {
      const highlightedItem = listRef.current.children[searchState.highlightedIndex] as HTMLElement;
      if (highlightedItem) {
        highlightedItem.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth',
        });
      }
    }
  }, [searchState.highlightedIndex]);

  return (
    <div className={`relative ${className}`}>
      <input
        ref={inputRef}
        type="text"
        id={id}
        value={value}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        maxLength={maxLength}
        className={`w-full px-3 py-2 ${theme.input.bg} ${theme.input.border} ${theme.card.rounded} shadow-sm ${theme.input.focusRing} text-sm sm:text-base ${theme.input.text} ${theme.input.placeholderText}`}
        role="combobox"
        aria-autocomplete="list"
        aria-expanded={searchState.showSuggestions}
        aria-haspopup="listbox"
        aria-owns={searchState.showSuggestions ? 'suggestions-listbox' : undefined}
        aria-activedescendant={
          searchState.highlightedIndex >= 0
            ? `suggestion-${searchState.highlightedIndex}`
            : undefined
        }
      />

      {searchState.showSuggestions && (
        <div
          className={`absolute z-10 w-full mt-1 ${theme.card.bg} ${theme.card.border} ${theme.card.rounded} shadow-lg max-h-60 overflow-auto`}
        >
          {searchState.loading && (
            <div className={`px-3 py-2 text-sm ${theme.card.text} text-center`}>
              搜索中...
            </div>
          )}

          {searchState.error && (
            <div className={`px-3 py-2 text-sm text-red-600 text-center`}>
              {searchState.error}
            </div>
          )}

          {!searchState.loading && !searchState.error && searchState.suggestions.length === 0 && (
            <div className={`px-3 py-2 text-sm ${theme.card.text} text-center`}>
              未找到匹配的种类
            </div>
          )}

          {!searchState.loading && !searchState.error && searchState.suggestions.length > 0 && (
            <ul
              ref={listRef}
              role="listbox"
              id="suggestions-listbox"
              className="py-1"
            >
              {searchState.suggestions.map((suggestion, index) => (
                <li
                  key={`${suggestion.科}-${suggestion.属}-${suggestion.种}`}
                  id={`suggestion-${index}`}
                  role="option"
                  aria-selected={index === searchState.highlightedIndex}
                  className={`px-3 py-2 cursor-pointer text-sm ${
                    index === searchState.highlightedIndex
                      ? `${theme.dropdown.itemActiveBg} ${theme.dropdown.itemActiveText}`
                      : `${theme.dropdown.itemText} ${theme.dropdown.itemHoverBg}`
                  }`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  onMouseEnter={() => handleSuggestionHover(index)}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{suggestion.种}</span>
                    <span className={`text-xs ${
                      index === searchState.highlightedIndex
                        ? theme.dropdown.itemActiveText
                        : theme.card.secondaryText
                    }`}>
                      {suggestion.科} · {suggestion.属}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default CategorySuggestions;