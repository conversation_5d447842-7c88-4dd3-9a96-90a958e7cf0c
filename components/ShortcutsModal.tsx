import React from 'react';
import { X } from 'lucide-react';

interface ShortcutsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ShortcutsModal: React.FC<ShortcutsModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  const shortcuts = [
    {
      category: '文件操作',
      items: [
        { key: 'Ctrl+N', description: '新建分类' },
        { key: 'Ctrl+I', description: '导入图片' },
        { key: 'Ctrl+E', description: '导出数据' },
        { key: 'Ctrl+Q', description: '退出应用' }
      ]
    },
    {
      category: '编辑操作',
      items: [
        { key: 'Ctrl+Z', description: '撤销' },
        { key: 'Ctrl+Shift+Z', description: '重做' },
        { key: 'Ctrl+C', description: '复制' },
        { key: 'Ctrl+V', description: '粘贴' },
        { key: 'Ctrl+A', description: '全选' },
        { key: 'Ctrl+F', description: '搜索' }
      ]
    },
    {
      category: '视图操作',
      items: [
        { key: 'Ctrl+R', description: '刷新页面' },
        { key: 'Ctrl+Shift+R', description: '强制刷新' },
        { key: 'Ctrl+0', description: '重置缩放' },
        { key: 'Ctrl++', description: '放大' },
        { key: 'Ctrl+-', description: '缩小' },
        { key: 'F11', description: '全屏切换' }
      ]
    },
    {
      category: '窗口操作',
      items: [
        { key: 'Ctrl+M', description: '最小化窗口' },
        { key: 'Ctrl+W', description: '关闭窗口' },
        { key: 'Ctrl+Shift+I', description: '开发者工具' }
      ]
    },
    {
      category: '图片查看器',
      items: [
        { key: 'ESC', description: '关闭查看器' },
        { key: '←/→', description: '切换图片' },
        { key: 'Space', description: '重置缩放' },
        { key: '+/-', description: '缩放图片' }
      ]
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={onClose}>
      <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">快捷键指南</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={24} className="text-gray-600" />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {shortcuts.map((category, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-700 mb-3 border-b border-gray-200 pb-2">
                {category.category}
              </h3>
              <div className="space-y-2">
                {category.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-center justify-between">
                    <span className="text-gray-600">{item.description}</span>
                    <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-sm font-mono text-gray-800 shadow-sm">
                      {item.key.replace('Ctrl', process.platform === 'darwin' ? 'Cmd' : 'Ctrl')}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-800 mb-2">使用提示</h4>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• 所有快捷键都支持在应用的任何界面使用</li>
            <li>• 图片查看器快捷键仅在查看图片时有效</li>
            <li>• 在macOS系统中，Ctrl键对应Command键</li>
            <li>• 可以通过菜单栏查看和使用所有功能</li>
          </ul>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShortcutsModal;