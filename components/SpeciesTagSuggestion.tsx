import React, { useState, useEffect } from 'react';
import { getSpeciesInfo } from '../services/species';
import { useTheme } from '../contexts/ThemeContext';
import type { SpeciesDictionaryEntry } from '../schemas/speciesDictionary';

interface SpeciesTagSuggestionProps {
  categoryName: string;
  onAddTags: (tags: string) => void;
  className?: string;
}

const SpeciesTagSuggestion: React.FC<SpeciesTagSuggestionProps> = ({
  categoryName,
  onAddTags,
  className = '',
}) => {
  const { theme } = useTheme();
  const [speciesData, setSpeciesData] = useState<SpeciesDictionaryEntry | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchSpeciesInfo = async () => {
      if (!categoryName.trim()) {
        setSpeciesData(null);
        return;
      }

      setIsLoading(true);
      try {
        const data = await getSpeciesInfo(categoryName);
        setSpeciesData(data);
      } catch (error) {
        console.error('获取物种信息失败:', error);
        setSpeciesData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSpeciesInfo();
  }, [categoryName]);

  const handleAddTags = () => {
    if (!speciesData) {
      return;
    }

    const taxonomicTags = [
      speciesData.目,
      speciesData.科,
      speciesData.属,
    ].filter(tag => tag && tag.trim() !== '');

    // 使用英文逗号且无空格
    const tagsString = taxonomicTags.join(',');
    onAddTags(tagsString);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleAddTags();
    }
  };

  // 如果没有匹配到物种或还在加载中，不显示任何内容
  if (!speciesData || isLoading) {
    return null;
  }

  const taxonomicInfo = [
    speciesData.目,
    speciesData.科,
    speciesData.属,
  ].filter(info => info && info.trim() !== '').join('·');

  return (
    <div
      data-testid="species-tag-suggestion"
      className={`mt-2 p-3 ${theme.card.bg} ${theme.card.border} ${theme.card.rounded} border ${className}`}
    >
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div className="flex-1">
          <p className={`text-sm ${theme.card.text}`}>
            添加{categoryName}对应目科属
            <span className={`ml-1 font-medium ${theme.brandColor}`}>
              （{taxonomicInfo}）
            </span>
            到tag中
          </p>
        </div>
        <button
          type="button"
          onClick={handleAddTags}
          onKeyDown={handleKeyDown}
          className={`px-3 py-1.5 text-sm ${theme.button.primary} ${theme.button.primaryText} ${theme.card.rounded} ${theme.button.transition} hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
          aria-label="添加物种目科属标签到标签字段"
        >
          添加到标签
        </button>
      </div>
    </div>
  );
};

export default SpeciesTagSuggestion;