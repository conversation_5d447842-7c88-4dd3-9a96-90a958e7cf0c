import React, { useState } from 'react';
import { CheckCircle, ArrowRight, ArrowLeft, X, FileImage, FolderPlus, Tags, Search } from 'lucide-react';

interface WelcomeGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

const WelcomeGuide: React.FC<WelcomeGuideProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);

  if (!isOpen) return null;

  const steps = [
    {
      title: '欢迎使用鸟类图片管理器',
      icon: <FileImage size={48} className="text-blue-500" />,
      content: (
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-4">
            本地化图片管理系统。
          </p>
          <div className="grid grid-cols-2 gap-4 mt-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">🎯 核心功能</h4>
              <ul className="text-blue-700 text-sm space-y-1 text-left">
                <li>• 本地图片存储与管理</li>
                <li>• 支持OSS云端储存照片以及数据库</li>
                <li>• 标签管理与搜索</li>
                <li>• 高质量缩略图生成</li>
              </ul>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">✨ 特色优势</h4>
              <ul className="text-green-700 text-sm space-y-1 text-left">
                <li>• 完全离线工作</li>
                <li>• 数据安全可靠</li>
                <li>• 现代化界面设计</li>
                <li>• 快捷鸟种、标签填充</li>
              </ul>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '第一步：创建分类',
      icon: <FolderPlus size={48} className="text-green-500" />,
      content: (
        <div>
          <p className="text-gray-600 mb-4">
            开始之前，我们需要创建一些分类来组织您的图片。系统已经为您预设了几个常用的鸟类分类。
          </p>
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <h4 className="font-semibold mb-2">预设分类包括：</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">🐦‍⬛ 喜鹊</span>
              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">🐦 麻雀</span>
              <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded">🕊️ 斑鸠</span>
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">💡 操作提示</h4>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• 点击主页的"新建分类"按钮创建自定义分类</li>
              <li>• 为每个分类添加详细的描述信息</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: '第二步：上传图片',
      icon: <FileImage size={48} className="text-blue-500" />,
      content: (
        <div>
          <p className="text-gray-600 mb-4">
            将您的鸟类图片上传到相应的分类中。系统会自动生成高质量缩略图并提取图片信息。
          </p>
          <div className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">📁 支持的格式</h4>
              <div className="grid grid-cols-3 gap-2 text-sm">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded">JPG</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded">PNG</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded">GIF</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded">BMP</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded">WEBP</span>
              </div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-2">⚡ 上传方式</h4>
              <ul className="text-yellow-700 text-sm space-y-1">
                <li>• 在分类页面点击"上传图片"按钮</li>
              </ul>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '第三步：添加标签',
      icon: <Tags size={48} className="text-purple-500" />,
      content: (
        <div>
          <p className="text-gray-600 mb-4">
            为图片添加描述性标签，便于后续的搜索和筛选。系统支持智能标签推荐功能。
          </p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">✏️ 标签操作</h4>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• 在图片详情页添加标签（用逗号分隔）</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: '第四步：搜索与管理',
      icon: <Search size={48} className="text-teal-500" />,
      content: (
        <div>
          <p className="text-gray-600 mb-4">
            使用强大的搜索功能快速找到您需要的图片，并进行高效的管理操作。
          </p>
          <div className="space-y-4">
            <div className="bg-teal-50 p-4 rounded-lg">
              <h4 className="font-semibold text-teal-800 mb-2">🔍 搜索功能</h4>
              <ul className="text-teal-700 text-sm space-y-1">
                <li>• 按标签搜索：输入标签名快速筛选</li>
                <li>• 按分类浏览：在分类页面查看所有图片</li>
                <li>• 全局搜索：在搜索框中输入关键词</li>
                <li>• 组合搜索：同时使用多个标签筛选</li>
              </ul>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-800 mb-2">⚙️ 管理操作</h4>
              <ul className="text-orange-700 text-sm space-y-1">
                <li>• 编辑图片信息和标签</li>
                <li>• 移动图片到其他分类</li>
                <li>• 删除不需要的图片</li>
                <li>• 导出数据备份</li>
              </ul>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '开始使用',
      icon: <CheckCircle size={48} className="text-green-500" />,
      content: (
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-6">
            恭喜！您已经了解了鸟类图片管理器的基本使用方法。
          </p>
          <div className="bg-green-50 p-6 rounded-lg mb-6">
            <h4 className="font-semibold text-green-800 mb-3">🚀 立即开始</h4>
            <p className="text-green-700 mb-4">
              现在您可以开始创建分类、上传图片、添加标签，构建您的个人鸟类图片库了！
            </p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="bg-white p-3 rounded border border-green-200">
                <strong className="text-green-800">💡 小贴士</strong>
                <p className="text-green-700 mt-1">支持本地和云端双重存储模式</p>
              </div>
              <div className="bg-white p-3 rounded border border-green-200">
                <strong className="text-green-800">🛠️ 技巧</strong>
                <p className="text-green-700 mt-1">智能物种识别，自动推荐相关标签</p>
              </div>
            </div>
          </div>
          <p className="text-gray-500 text-sm">
            如果需要再次查看此指南，可以在帮助菜单中找到。
          </p>
        </div>
      )
    }
  ];

  const currentStepData = steps[currentStep];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={onClose}>
      <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {currentStepData.icon}
            <div>
              <h2 className="text-2xl font-bold text-gray-800">{currentStepData.title}</h2>
              <p className="text-gray-500">步骤 {currentStep + 1} / {steps.length}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={24} className="text-gray-600" />
          </button>
        </div>

        {/* 进度指示器 */}
        <div className="flex items-center justify-center mb-6">
          <div className="flex space-x-2">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-blue-500'
                    : index < currentStep
                    ? 'bg-green-500'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="mb-8">
          {currentStepData.content}
        </div>

        {/* 导航按钮 */}
        <div className="flex items-center justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center space-x-2 px-4 py-2 rounded transition-colors ${
              currentStep === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <ArrowLeft size={16} />
            <span>上一步</span>
          </button>

          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              跳过引导
            </button>
            {currentStep === steps.length - 1 ? (
              <button
                onClick={onClose}
                className="flex items-center space-x-2 px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
              >
                <span>开始使用</span>
                <CheckCircle size={16} />
              </button>
            ) : (
              <button
                onClick={nextStep}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                <span>下一步</span>
                <ArrowRight size={16} />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeGuide;