import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';

interface TimelineItem {
  id: string;
  date: string;
  title: string;
  description: string;
  type: 'upload' | 'tag' | 'edit' | 'delete';
}

interface TimelineProps {
  items?: TimelineItem[];
  isLoading?: boolean;
  hasError?: boolean;
  testId?: string;
}

const Timeline: React.FC<TimelineProps> = ({
  items = [],
  isLoading = false,
  hasError = false,
  testId
}) => {
  const { theme } = useTheme();

  if (hasError) {
    return (
      <div className={`${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} p-6`} data-testid={testId}>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>活动时间线</h3>
        <p className="text-red-500">加载时间线数据时出错</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} p-6`} data-testid={testId}>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>活动时间线</h3>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-start space-x-3">
              <div className={`w-3 h-3 rounded-full ${theme.brandColor.replace('text-', 'bg-')} animate-pulse`} />
              <div className="flex-1">
                <div className={`h-4 ${theme.card.secondaryText.replace('text-', 'bg-')} rounded animate-pulse mb-2`} />
                <div className={`h-3 ${theme.card.secondaryText.replace('text-', 'bg-')} rounded animate-pulse w-3/4`} />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} p-6`} data-testid={testId}>
      <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>活动时间线</h3>
      
      {items.length === 0 ? (
        <p className={theme.card.secondaryText}>暂无活动记录</p>
      ) : (
        <div className="space-y-4">
          {items.map((item, index) => (
            <motion.div
              key={item.id}
              className="flex items-start space-x-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className={`
                w-3 h-3 rounded-full mt-1.5
                ${item.type === 'upload' ? 'bg-green-500' :
                  item.type === 'tag' ? 'bg-blue-500' :
                  item.type === 'edit' ? 'bg-yellow-500' :
                  'bg-red-500'}
              `} />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className={`text-sm font-medium ${theme.text}`}>
                    {item.title}
                  </h4>
                  <span className={`text-xs ${theme.card.secondaryText}`}>
                    {item.date}
                  </span>
                </div>
                <p className={`text-sm ${theme.card.secondaryText} mt-1`}>
                  {item.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Timeline;
