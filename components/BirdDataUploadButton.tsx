import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { selectAndUploadBirdSightingFile } from '../services/bird-sighting-api';
import type { UploadResult } from '../types';

// Upload icon component
const UploadIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
    />
  </svg>
);

// Loading spinner component (inline)
const InlineLoadingSpinner: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={`animate-spin ${className}`}
    fill="none"
    viewBox="0 0 24 24"
    aria-hidden="true"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

// Check icon component
const CheckIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 13l4 4L19 7"
    />
  </svg>
);

// Error icon component
const ErrorIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);

export type UploadState = 'idle' | 'uploading' | 'success' | 'error';

export interface BirdDataUploadButtonProps {
  onUploadComplete?: (result: UploadResult) => void;
  className?: string;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'primary' | 'secondary';
  showText?: boolean;
}

const BirdDataUploadButton: React.FC<BirdDataUploadButtonProps> = ({
  onUploadComplete,
  className = '',
  disabled = false,
  size = 'medium',
  variant = 'primary',
  showText = true,
}) => {
  const { theme } = useTheme();
  const [uploadState, setUploadState] = useState<UploadState>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Reset state after success/error
  const resetStateAfterDelay = (delay: number = 3000) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setUploadState('idle');
      setErrorMessage(null);
      setSuccessMessage(null);
    }, delay);
  };

  // 防抖处理的上传函数
  const handleUpload = useCallback(async () => {
    if (disabled || uploadState === 'uploading') {
      return;
    }

    try {
      setUploadState('uploading');
      setErrorMessage(null);
      setSuccessMessage(null);

      console.log('🔧 开始选择和上传观鸟数据文件');
      
      // 添加最小加载时间，改善用户体验
      const startTime = Date.now();
      const result = await selectAndUploadBirdSightingFile();
      const elapsed = Date.now() - startTime;
      
      // 确保至少显示500ms的加载状态
      if (elapsed < 500) {
        await new Promise(resolve => setTimeout(resolve, 500 - elapsed));
      }

      if (result.success) {
        console.log('✅ 观鸟数据上传成功:', result);
        setUploadState('success');
        setSuccessMessage(
          `上传成功！已导入 ${result.data?.importedCount || 0} 条观鸟记录`
        );
        
        // 调用回调函数
        if (onUploadComplete) {
          onUploadComplete(result);
        }

        // 3秒后重置状态
        resetStateAfterDelay(3000);
      } else {
        // 用户取消不显示错误
        if (result.error === '用户取消文件选择') {
          console.log('🚫 用户取消了文件选择');
          setUploadState('idle');
          return;
        }

        console.error('❌ 观鸟数据上传失败:', result.error);
        setUploadState('error');
        setErrorMessage(result.error || '上传失败，请重试');

        // 5秒后重置状态
        resetStateAfterDelay(5000);
      }
    } catch (error) {
      console.error('❌ 上传过程中发生错误:', error);
      setUploadState('error');
      
      // 改善错误信息，更加用户友好
      let friendlyErrorMessage = '上传失败，请重试';
      if (error instanceof Error) {
        const errorMsg = error.message.toLowerCase();
        if (errorMsg.includes('network') || errorMsg.includes('连接')) {
          friendlyErrorMessage = '网络连接失败，请检查网络后重试';
        } else if (errorMsg.includes('format') || errorMsg.includes('格式')) {
          friendlyErrorMessage = '文件格式不支持，请选择Excel文件(.xlsx, .xls)';
        } else if (errorMsg.includes('size') || errorMsg.includes('大小')) {
          friendlyErrorMessage = '文件过大，请选择小于50MB的文件';
        } else if (errorMsg.includes('permission') || errorMsg.includes('权限')) {
          friendlyErrorMessage = '文件访问权限不足，请检查文件权限';
        } else {
          friendlyErrorMessage = error.message;
        }
      }
      
      setErrorMessage(friendlyErrorMessage);

      // 5秒后重置状态
      resetStateAfterDelay(5000);
    }
  }, [disabled, uploadState]);

  // Size styles
  const sizeStyles = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-6 py-3 text-lg',
  };

  // Button variant styles
  const variantStyles = {
    primary: `${theme.button.primary} ${theme.button.primaryText}`,
    secondary: `${theme.button.secondary} ${theme.button.secondaryText}`,
  };

  // State-specific styles
  const getStateStyles = () => {
    switch (uploadState) {
      case 'uploading':
        return 'opacity-90 cursor-not-allowed';
      case 'success':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'error':
        return 'bg-red-600 hover:bg-red-700 text-white';
      default:
        return '';
    }
  };

  // Get icon based on state
  const getIcon = () => {
    switch (uploadState) {
      case 'uploading':
        return <InlineLoadingSpinner className="w-5 h-5" />;
      case 'success':
        return <CheckIcon className="w-5 h-5" />;
      case 'error':
        return <ErrorIcon className="w-5 h-5" />;
      default:
        return <UploadIcon className="w-5 h-5" />;
    }
  };

  // Get button text based on state
  const getButtonText = () => {
    if (!showText) return null;

    switch (uploadState) {
      case 'uploading':
        return '上传中...';
      case 'success':
        return '上传成功';
      case 'error':
        return '上传失败';
      default:
        return '上传观鸟数据';
    }
  };

  // Get status message
  const getStatusMessage = () => {
    if (successMessage) {
      return (
        <div className="mt-2 text-sm text-green-600 dark:text-green-400">
          {successMessage}
        </div>
      );
    }

    if (errorMessage) {
      return (
        <div className="mt-2 text-sm text-red-600 dark:text-red-400">
          上传失败：{errorMessage}
        </div>
      );
    }

    return null;
  };

  const isDisabled = disabled || uploadState === 'uploading';

  return (
    <div className="flex flex-col items-start">
      <button
        type="button"
        onClick={handleUpload}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        aria-describedby={errorMessage || successMessage ? 'upload-status-message' : undefined}
        className={`
          inline-flex items-center gap-2 font-medium
          ${sizeStyles[size]}
          ${uploadState === 'success' || uploadState === 'error' 
            ? getStateStyles() 
            : variantStyles[variant]
          }
          ${theme.card.rounded}
          ${theme.button.transition}
          disabled:opacity-50 disabled:cursor-not-allowed
          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
          active:scale-95
          ${getStateStyles()}
          ${className}
        `.trim()}
      >
        {getIcon()}
        {getButtonText()}
      </button>

      {/* Status message */}
      {(successMessage || errorMessage) && (
        <div id="upload-status-message" role="status" aria-live="polite">
          {getStatusMessage()}
        </div>
      )}
    </div>
  );
};

export default BirdDataUploadButton;