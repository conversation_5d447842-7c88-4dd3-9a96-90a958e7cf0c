import { motion } from 'framer-motion';
import React, { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import Masonry from 'react-masonry-css';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { searchImagesByTag, deleteTag } from '../services/api';
import type { ApiError, ImageRead } from '../types';
import ErrorDisplay from './ErrorDisplay';
import { PhotoIcon, TrashIcon, ExclamationTriangleIcon } from './icons';
import ImageCard from './ImageCard';
import ImageCardSkeleton from './ImageCardSkeleton';
import ImageDetailModal from './ImageDetailModal';
import LoadingSpinner from './LoadingSpinner';

const ITEMS_PER_PAGE = 20;
const SESSION_STORAGE_SCROLL_PREFIX = 'tagPageScrollPos_';
const SESSION_STORAGE_COUNT_PREFIX = 'tagPageItemCount_';

const TagPage: React.FC = () => {
  const { tagName: encodedTagName } = useParams<{ tagName: string }>();
  const { theme } = useTheme();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const [allImages, setAllImages] = useState<ImageRead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<ApiError | string | null>(null);

  const [selectedImage, setSelectedImage] = useState<ImageRead | null>(null);

  // 删除功能状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<number | null>(null);
  const [isImageDetailModalOpen, setIsImageDetailModalOpen] = useState(false);

  const [displayCount, setDisplayCount] = useState(ITEMS_PER_PAGE);
  const [initialSessionStateRestored, setInitialSessionStateRestored] = useState(false);

  const decodedTagName = useMemo(() => {
    try {
      return encodedTagName ? decodeURIComponent(encodedTagName) : '';
    } catch (e) {
      console.error('Failed to decode tagName:', encodedTagName, e);
      return encodedTagName || ''; // Fallback to encoded if decoding fails
    }
  }, [encodedTagName]);

  const scrollPositionKey = useMemo(
    () => `${SESSION_STORAGE_SCROLL_PREFIX}${decodedTagName}`,
    [decodedTagName]
  );
  const itemCountKey = useMemo(
    () => `${SESSION_STORAGE_COUNT_PREFIX}${decodedTagName}`,
    [decodedTagName]
  );

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedCount = sessionStorage.getItem(itemCountKey);
      if (storedCount) {
        setDisplayCount(Math.max(ITEMS_PER_PAGE, parseInt(storedCount, 10)));
      }
    }
  }, [itemCountKey]);

  const fetchImagesByTag = useCallback(async () => {
    if (!decodedTagName) {
      setError({ message: 'Tag name is missing.' });
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const data = await searchImagesByTag(decodedTagName);
      setAllImages(data);
    } catch (err) {
      setError(err as ApiError);
      setAllImages([]);
    } finally {
      setIsLoading(false);
    }
  }, [decodedTagName]);

  useEffect(() => {
    fetchImagesByTag();
  }, [fetchImagesByTag]);

  // 删除标签功能
  const handleDeleteTag = useCallback(async () => {
    if (!decodedTagName) {
      setDeleteError('标签名称无效');
      return;
    }

    try {
      setIsDeleting(true);
      setDeleteError(null);
      console.log('🗑️ 删除标签:', decodedTagName);

      // 首先根据标签名称获取标签信息
      console.log('🔍 根据名称获取标签:', decodedTagName);
      const tag = await window.electronAPI?.getTagByName?.(decodedTagName);
      
      if (!tag) {
        throw new Error(`未找到标签: ${decodedTagName}`);
      }

      console.log('✅ 找到标签ID:', tag.id);

      // 使用标签ID删除标签
      await deleteTag(tag.id);

      console.log('✅ 标签删除成功');

      // 删除成功后导航到标签列表页面
      navigate('/tags');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete tag';
      console.error('❌ 删除标签失败:', errorMessage);
      setDeleteError(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  }, [decodedTagName, navigate]);

  const openDeleteDialog = useCallback(() => {
    setShowDeleteDialog(true);
    setDeleteError(null);
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setShowDeleteDialog(false);
    setDeleteError(null);
  }, []);

  const confirmDelete = useCallback(() => {
    handleDeleteTag();
  }, [handleDeleteTag]);

  useLayoutEffect(() => {
    if (
      !isLoading &&
      allImages.length > 0 &&
      !initialSessionStateRestored &&
      typeof window !== 'undefined'
    ) {
      const savedScroll = sessionStorage.getItem(scrollPositionKey);
      if (savedScroll) {
        requestAnimationFrame(() => window.scrollTo(0, parseInt(savedScroll, 10)));
      }
      setInitialSessionStateRestored(true);
    }
  }, [isLoading, allImages, initialSessionStateRestored, scrollPositionKey]);

  useEffect(() => {
    const saveState = () => {
      if (typeof window !== 'undefined' && initialSessionStateRestored) {
        sessionStorage.setItem(scrollPositionKey, String(window.scrollY));
        sessionStorage.setItem(itemCountKey, String(displayCount));
      }
    };

    // Save on unmount
    return () => {
      saveState();
    };
  }, [displayCount, scrollPositionKey, itemCountKey, initialSessionStateRestored]);

  const displayedImages = useMemo(() => {
    return allImages.slice(0, displayCount);
  }, [allImages, displayCount]);

  const handleLoadMore = () => {
    const newCount = Math.min(displayCount + ITEMS_PER_PAGE, allImages.length);
    setDisplayCount(newCount);
  };

  const openImageModal = (image: ImageRead, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
    setIsImageDetailModalOpen(true);
  };

  const handlePreviousImage = () => {
    if (displayedImages && currentImageIndex !== null && currentImageIndex > 0) {
      const newIndex = currentImageIndex - 1;
      setSelectedImage(displayedImages[newIndex]);
      setCurrentImageIndex(newIndex);
    }
  };

  const handleNextImage = () => {
    if (
      displayedImages &&
      currentImageIndex !== null &&
      currentImageIndex < displayedImages.length - 1
    ) {
      const newIndex = currentImageIndex + 1;
      setSelectedImage(displayedImages[newIndex]);
      setCurrentImageIndex(newIndex);
    }
  };

  const handleImageUpdateInModal = (updatedImage: ImageRead) => {
    setAllImages(prevImages =>
      prevImages.map(img => (img.id === updatedImage.id ? updatedImage : img))
    );
    if (selectedImage && selectedImage.id === updatedImage.id) {
      setSelectedImage(updatedImage);
    }
  };

  const handleImageDeleteInModal = (deletedImageId: string) => {
    setAllImages(prevImages => prevImages.filter(img => img.id !== deletedImageId));
    if (selectedImage && selectedImage.id === deletedImageId) {
      setIsImageDetailModalOpen(false);
      setSelectedImage(null);
      setCurrentImageIndex(null);
    }
  };

  const breakpointColumnsObj = {
    default: 5,
    1536: 5, // 2xl
    1280: 4, // xl
    1024: 3, // lg
    768: 3, // md
    640: 2, // sm
  };

  const renderContent = () => {
    if (isLoading && allImages.length === 0) {
      return (
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className='my-masonry-grid'
          columnClassName='my-masonry-grid_column'
        >
          {Array.from({ length: ITEMS_PER_PAGE / 2 }).map((_, index) => (
            <ImageCardSkeleton key={index} />
          ))}
        </Masonry>
      );
    }

    if (error) {
      return <ErrorDisplay error={error} onRetry={fetchImagesByTag} />;
    }

    if (allImages.length === 0) {
      return (
        <div className={`text-center py-10 ${theme.card.secondaryText} animate-fadeIn`}>
          <PhotoIcon className={`w-16 h-16 mx-auto mb-4 opacity-50 ${theme.card.secondaryText}`} />
          <p className='text-lg sm:text-xl'>No images found for tag: "{decodedTagName}"</p>
        </div>
      );
    }

    return (
      <>
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className='my-masonry-grid'
          columnClassName='my-masonry-grid_column'
        >
          {displayedImages.map((image, index) => (
            <motion.div
              key={image.id}
              layout
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{
                duration: 0.4,
                ease: [0.25, 1, 0.5, 1],
                delay: (index % ITEMS_PER_PAGE) * 0.03,
              }}
            >
              <ImageCard
                image={image}
                onClick={() => openImageModal(image, index)}
                forceSquare={false}
              />
            </motion.div>
          ))}
        </Masonry>
        {displayCount < allImages.length && (
          <div className='mt-8 text-center'>
            <button
              onClick={handleLoadMore}
              className={`px-6 py-3 ${theme.button.primary} ${theme.button.primaryText} ${theme.card.rounded} ${theme.button.transition} text-sm font-semibold hover:opacity-90 active:scale-95`}
            >
              Load More ({allImages.length - displayCount} remaining)
            </button>
          </div>
        )}
      </>
    );
  };

  const titleTextClass =
    theme.name === 'Neon Galaxy' || theme.name === 'RetroTech Dark' ? theme.brandColor : theme.text;

  return (
    <div className='space-y-6 sm:space-y-8'>
      <div
        className={`p-4 sm:p-5 ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} ${theme.card.border || ''} animate-fadeInUp`}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className={`text-2xl sm:text-3xl font-bold ${titleTextClass} mb-1`}>
              Tag: {decodedTagName}
            </h1>
            <p className={`${theme.card.secondaryText} text-sm sm:text-base`}>
              {isLoading
                ? 'Loading images...'
                : `${allImages.length} image${allImages.length !== 1 ? 's' : ''} found.`}
            </p>
          </div>

          {/* 删除按钮 */}
          {decodedTagName && isAuthenticated && (
            <button
              onClick={openDeleteDialog}
              disabled={isDeleting}
              className={`
                ml-4 px-3 py-2 rounded-lg text-sm font-medium
                bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 text-white
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors duration-200
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500
                flex items-center space-x-2
                ${theme.name === 'RetroTech Dark' ? 'shadow-lg shadow-red-500/25' : ''}
                ${theme.name === 'Neon Galaxy' ? 'shadow-lg shadow-red-500/30' : ''}
              `}
              title="删除标签"
              data-testid="delete-tag-button"
            >
              <TrashIcon className="w-4 h-4" />
              <span>删除标签</span>
            </button>
          )}
        </div>
      </div>

      {renderContent()}

      {selectedImage && isImageDetailModalOpen && (
        <ImageDetailModal
          image={selectedImage}
          isOpen={isImageDetailModalOpen}
          onClose={() => setIsImageDetailModalOpen(false)}
          onUpdate={handleImageUpdateInModal}
          onDelete={handleImageDeleteInModal}
          onThumbnailUpdated={fetchImagesByTag} // Or a more targeted update if possible
          isCurrentlyCategoryThumbnail={false} // Tag pages don't directly manage category thumbnails
          allowSetCategoryThumbnail={false} // Cannot set category thumbnail from tag page
          onPreviousImage={handlePreviousImage}
          onNextImage={handleNextImage}
          hasPreviousImage={currentImageIndex !== null && currentImageIndex > 0}
          hasNextImage={
            currentImageIndex !== null && currentImageIndex < displayedImages.length - 1
          }
          isAuthenticated={isAuthenticated}
        />
      )}

      {/* 删除确认对话框 */}
      {showDeleteDialog && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          data-testid="delete-confirmation-dialog"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className={`
              ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow}
              ${theme.card.border || ''} p-6 max-w-md w-full mx-4
            `}
          >
            <div className="flex items-center mb-4">
              <ExclamationTriangleIcon className="w-6 h-6 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold">确认删除标签</h3>
            </div>

            <p className={`${theme.card.secondaryText} mb-2`}>
              确定要删除标签 <span className="font-semibold">"{decodedTagName}"</span> 吗？
            </p>

            <p className={`${theme.card.secondaryText} text-sm mb-6`}>
              此操作将删除标签以及所有相关的图片关联，且无法撤销。
            </p>

            {deleteError && (
              <div
                className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-lg"
                data-testid="delete-error-message"
              >
                <p className="text-red-700 dark:text-red-300 text-sm">{deleteError}</p>
              </div>
            )}

            {isDeleting && (
              <div className="mb-4 flex items-center justify-center" data-testid="deleting-spinner">
                <LoadingSpinner size="sm" />
                <span className={`ml-2 text-sm ${theme.card.secondaryText}`}>正在删除...</span>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={closeDeleteDialog}
                disabled={isDeleting}
                className={`
                  flex-1 px-4 py-2 rounded-lg text-sm font-medium
                  ${theme.button.secondary} ${theme.button.secondaryText}
                  ${theme.button.transition} hover:opacity-80
                  disabled:opacity-50 disabled:cursor-not-allowed
                  focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme.input.focusRing}
                `}
                data-testid="cancel-delete-button"
              >
                取消
              </button>

              <button
                onClick={confirmDelete}
                disabled={isDeleting}
                className={`
                  flex-1 px-4 py-2 rounded-lg text-sm font-medium
                  bg-red-600 hover:bg-red-700 text-white
                  transition-colors duration-200
                  disabled:opacity-50 disabled:cursor-not-allowed
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500
                `}
                data-testid="confirm-delete-button"
              >
                {isDeleting ? '删除中...' : '确认删除'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default TagPage;
