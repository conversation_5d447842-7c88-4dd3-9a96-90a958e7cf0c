import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import LoadingSpinner from './LoadingSpinner';

interface StatCardProps {
  icon: React.ReactNode;
  title: string;
  value: number;
  isLoading?: boolean;
  hasError?: boolean;
  testId?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  icon,
  title,
  value,
  isLoading = false,
  hasError = false,
  testId
}) => {
  const { theme } = useTheme();

  const formatValue = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getIconBgClass = () => {
    switch (theme.name) {
      case 'Modern Clean Pro':
        return 'bg-blue-50 dark:bg-blue-900/20';
      case 'Nature Inspired':
        return 'bg-green-50 dark:bg-green-900/20';
      case 'Neon Galaxy':
        return 'bg-cyan-900/20 dark:bg-cyan-800/30';
      case 'Arcade Flash':
        return 'bg-yellow-100 dark:bg-yellow-900/30';
      case 'RetroTech Dark':
        return 'bg-emerald-900/20 dark:bg-emerald-800/30';
      default:
        return 'bg-blue-50 dark:bg-blue-900/20';
    }
  };

  return (
    <motion.div
      className={`
        ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} 
        ${theme.card.border || ''} p-4 sm:p-6
        transition-all duration-200 hover:scale-105
      `}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      data-testid={testId}
    >
      <div className="flex items-center">
        <div className={`
          p-3 rounded-lg mr-4
          ${getIconBgClass()}
          ${theme.brandColor}
        `}>
          {React.cloneElement(icon as React.ReactElement, {
            className: 'w-6 h-6'
          })}
        </div>
        
        <div className="flex-1">
          <p className={`text-sm font-medium ${theme.card.secondaryText}`}>
            {title}
          </p>
          
          {isLoading ? (
            <div className="flex items-center mt-1">
              <LoadingSpinner size="sm" />
              <span className={`ml-2 text-sm ${theme.card.secondaryText}`}>
                加载中...
              </span>
            </div>
          ) : hasError ? (
            <p className="text-2xl font-bold text-red-500 mt-1">
              错误
            </p>
          ) : (
            <p className={`text-2xl font-bold ${theme.text} mt-1`}>
              {formatValue(value)}
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default StatCard;
