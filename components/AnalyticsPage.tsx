import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import { fadeInVariants, staggerContainerVariants, staggerItemVariants, getAnimationConfig } from '../utils/animations';
import {
  loadDataFile,
  DATA_FILES,
  type BirdSightingRecord,
  type TopBirdData
} from '../utils/dataLoader';
import {
  DocumentTextIcon,
  BirdIcon,
  HashtagIcon
} from './icons';
import PieChart from './PieChart';
import BirdSightingTimeline from './BirdSightingTimeline';
import LoadingSpinner from './LoadingSpinner';
import ErrorDisplay from './ErrorDisplay';
import TagsDisplay from './TagsDisplay';
import StatCard from './StatCard';
import BirdDataUploadStatus from './BirdDataUploadStatus';
import BirdDataUploadButton from './BirdDataUploadButton';
import { getDatabaseStats, type DatabaseStats } from '../services/database-stats';
import { getBirdSightingDataStatus, getBirdSightingData } from '../services/bird-sighting-api';
import type { BirdSightingStats, BirdSightingRecord as DatabaseBirdSightingRecord } from '../types';


const AnalyticsPage: React.FC = () => {
  const { theme, isDarkMode } = useTheme();

  // 数据库统计数据状态
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Timeline数据状态
  const [allBirdSightings, setAllBirdSightings] = useState<BirdSightingRecord[]>([]);
  const [isLoadingBirdData, setIsLoadingBirdData] = useState(true);
  const [birdDataError, setBirdDataError] = useState<string | null>(null);

  // Top Birds数据状态
  const [topBirdsData, setTopBirdsData] = useState<TopBirdData[]>([]);
  const [isLoadingTopBirds, setIsLoadingTopBirds] = useState(true);
  const [topBirdsError, setTopBirdsError] = useState<string | null>(null);

  // 观鸟数据状态
  const [hasBirdSightingData, setHasBirdSightingData] = useState(false);
  const [birdSightingStats, setBirdSightingStats] = useState<BirdSightingStats | null>(null);
  const [isLoadingBirdSightingStatus, setIsLoadingBirdSightingStatus] = useState(true);
  const [birdSightingStatusError, setBirdSightingStatusError] = useState<string | null>(null);

  // 获取数据库统计数据
  const fetchDatabaseStats = useCallback(async () => {
    try {
      setIsLoadingStats(true);
      setStatsError(null);
      console.log('📊 获取数据库统计数据...');

      const stats = await getDatabaseStats();
      setDatabaseStats(stats);

      console.log('✅ 数据库统计数据获取成功:', stats);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load database stats';
      console.error('❌ 数据库统计数据获取失败:', errorMessage);
      setStatsError(errorMessage);
    } finally {
      setIsLoadingStats(false);
    }
  }, []);

  // 获取观鸟数据状态
  const fetchBirdSightingStatus = useCallback(async () => {
    try {
      setIsLoadingBirdSightingStatus(true);
      setBirdSightingStatusError(null);
      console.log('🐦 检查观鸟数据状态...');

      const status = await getBirdSightingDataStatus();
      setHasBirdSightingData(status.hasData);
      setBirdSightingStats(status.stats || null);

      console.log('✅ 观鸟数据状态检查完成:', status);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to check bird sighting data status';
      console.error('❌ 观鸟数据状态检查失败:', errorMessage);
      setBirdSightingStatusError(errorMessage);
    } finally {
      setIsLoadingBirdSightingStatus(false);
    }
  }, []);

  // 获取观鸟数据记录
  const fetchBirdSightingRecords = useCallback(async () => {
    try {
      setIsLoadingBirdData(true);
      setBirdDataError(null);
      console.log('🐦 获取观鸟数据记录...');

      const response = await getBirdSightingData();
      
      if (response.success && response.data) {
        // 转换数据库记录格式为组件期望的格式
        const convertedRecords: BirdSightingRecord[] = response.data.map((record: DatabaseBirdSightingRecord) => ({
          id: record.id,
          chineseName: record.chineseName,
          latinName: record.latinName,
          englishName: record.englishName,
          order: record.order,
          family: record.family,
          count: record.count,
          recordDate: record.recordDate
        }));

        setAllBirdSightings(convertedRecords);
        console.log('✅ 观鸟数据记录获取成功:', convertedRecords.length, '条记录');
      } else {
        throw new Error(response.error || 'Failed to fetch bird sighting data');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch bird sighting records';
      console.error('❌ 观鸟数据记录获取失败:', errorMessage);
      setBirdDataError(errorMessage);
      setAllBirdSightings([]); // 清空数据
    } finally {
      setIsLoadingBirdData(false);
    }
  }, []);

  const fetchData = useCallback(
    async <TData,>(
      filename: string,
      dataSetter: (data: TData) => void,
      currentErrorSetter: (error: string | null) => void,
      currentLoadingSetter: (loading: boolean) => void
    ) => {
      currentLoadingSetter(true);
      currentErrorSetter(null);

      try {
        const result = await loadDataFile<TData>(filename);
        if (result.success && result.data) {
          dataSetter(result.data);
        } else {
          throw new Error(result.error || `Failed to load data from ${filename}`);
        }
      } catch (error: any) {
        console.error(`Failed to fetch data from ${filename}:`, error);
        currentErrorSetter(error.message || `Failed to load data from ${filename}.`);
      } finally {
        currentLoadingSetter(false);
      }
    },
    []
  );

  useEffect(() => {
    // 获取数据库统计数据
    fetchDatabaseStats();

    // 检查观鸟数据状态
    fetchBirdSightingStatus();

    // 获取Top Birds数据
    fetchData<TopBirdData[]>(
      DATA_FILES.TOP_BIRDS,
      setTopBirdsData,
      setTopBirdsError,
      setIsLoadingTopBirds
    );
  }, [fetchData, fetchDatabaseStats, fetchBirdSightingStatus]);

  // 当观鸟数据状态发生变化时，获取观鸟数据记录
  useEffect(() => {
    if (hasBirdSightingData && !isLoadingBirdSightingStatus && !birdSightingStatusError) {
      console.log('🔄 观鸟数据存在，获取时间线记录...');
      fetchBirdSightingRecords();
    } else if (!hasBirdSightingData && !isLoadingBirdSightingStatus) {
      console.log('📭 无观鸟数据，清空时间线记录');
      setAllBirdSightings([]);
      setBirdDataError(null);
      setIsLoadingBirdData(false);
    }
  }, [hasBirdSightingData, isLoadingBirdSightingStatus, birdSightingStatusError, fetchBirdSightingRecords]);





  const pieChartColors = useMemo(() => {
    const modernLightColors = [
      '#3B82F6',
      '#60A5FA',
      '#93C5FD',
      '#A0AEC0',
      '#CBD5E0',
      '#34D399',
      '#6EE7B7',
      '#FBBF24',
      '#F472B6',
      '#C4B5FD',
    ];
    const modernDarkColors = [
      '#60A5FA',
      '#93C5FD',
      '#BFDBFE',
      '#A0AEC0',
      '#718096',
      '#6EE7B7',
      '#A7F3D0',
      '#FCD34D',
      '#F9A8D4',
      '#A78BFA',
    ];

    const natureLightColors = [
      '#4CAF50',
      '#8BC34A',
      '#CDDC39',
      '#AED581',
      '#795548',
      '#A1887F',
      '#2196F3',
      '#64B5F6',
      '#FFC107',
      '#FF9800',
    ];
    const natureDarkColors = [
      '#66BB6A',
      '#9CCC65',
      '#D4E157',
      '#C5E1A5',
      '#8D6E63',
      '#BCAAA4',
      '#42A5F5',
      '#90CAF9',
      '#FFEE58',
      '#FFB74D',
    ];

    const neonGalaxyColors = [
      '#22D3EE',
      '#06B6D4',
      '#4338CA',
      '#312E81',
      '#EC4899',
      '#DB2777',
      '#8B5CF6',
      '#A78BFA',
      '#FDE047',
      '#34D399',
    ];

    const arcadeFlashColors = [
      '#EF4444',
      '#FACC15',
      '#F97316',
      '#F87171',
      '#FDE047',
      '#FB923C',
      '#FEF3C7',
      '#DC2626',
      '#EAB308',
      '#FFFBEB',
    ];

    const retroTechDarkColors = [
      // Mint green shades and neutral grays
      '#2EE59D', // emerald-400
      '#10B981', // emerald-500
      '#6EE7B7', // emerald-300
      '#059669', // emerald-600
      '#A7F3D0', // emerald-200
      '#4A5568', // slate-600 (Tailwind zinc-600 equivalent)
      '#718096', // slate-500 (Tailwind zinc-500 equivalent)
      '#94A3B8', // slate-400 (Tailwind zinc-400 equivalent)
      '#047857', // emerald-700
      '#334155', // slate-700 (Tailwind zinc-700 equivalent)
    ];

    switch (theme.name) {
      case 'Modern Clean Pro':
        return isDarkMode ? modernDarkColors : modernLightColors;
      case 'Nature Inspired':
        return isDarkMode ? natureDarkColors : natureLightColors;
      case 'Neon Galaxy':
        return neonGalaxyColors;
      case 'Arcade Flash':
        return arcadeFlashColors;
      case 'RetroTech Dark':
        return retroTechDarkColors;
      default:
        return modernLightColors;
    }
  }, [theme.name, isDarkMode]);



  return (
    <motion.div
      className={`py-4 sm:py-5 ${theme.text} space-y-6`}
      variants={getAnimationConfig(staggerContainerVariants)}
      initial="hidden"
      animate="visible"
    >
      {/* 页面标题 */}
      <motion.div
        className={`${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} ${theme.card.border || ''} p-4 sm:p-6`}
        variants={getAnimationConfig(staggerItemVariants)}
      >
        <motion.h1
          className='text-2xl sm:text-3xl font-semibold mb-1'
          variants={getAnimationConfig(fadeInVariants)}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          数据分析
        </motion.h1>
        <motion.p
          className={`${theme.card.secondaryText} text-sm sm:text-base`}
          variants={getAnimationConfig(fadeInVariants)}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          查看您的观鸟数据统计和分析
        </motion.p>
      </motion.div>

      {/* 统计卡片 */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
        variants={getAnimationConfig(staggerItemVariants)}
        data-testid="stats-cards-container"
      >
        <StatCard
          icon={<BirdIcon />}
          title="种类数"
          value={databaseStats?.categoriesCount || 0}
          isLoading={isLoadingStats}
          hasError={!!statsError}
          testId="stats-card-categories"
        />
        <StatCard
          icon={<DocumentTextIcon />}
          title="图片数"
          value={databaseStats?.imagesCount || 0}
          isLoading={isLoadingStats}
          hasError={!!statsError}
          testId="stats-card-images"
        />
        <StatCard
          icon={<HashtagIcon />}
          title="标签数量"
          value={databaseStats?.tagsCount || 0}
          isLoading={isLoadingStats}
          hasError={!!statsError}
          testId="stats-card-tags"
        />
      </motion.div>
      {/* Top 10 Bird 和 标签展示 */}
      <motion.div
        className="grid grid-cols-1 lg:grid-cols-4 gap-5 sm:gap-6"
        variants={getAnimationConfig(staggerItemVariants)}
      >
        {/* Top 10 Bird */}
        <div
          className={`lg:col-span-1 ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} ${theme.card.border || ''} p-4 sm:p-6`}
        >
          <div className='flex flex-col sm:flex-row justify-between sm:items-center mb-1 sm:mb-2'>
            <div>
              <h2 className='text-xl sm:text-2xl font-semibold'>Top 10 Bird</h2>
            </div>
          </div>
          {isLoadingTopBirds ? (
            <div className='flex justify-center items-center h-[360px] sm:h-[400px] md:h-[420px]'>
              <LoadingSpinner />
            </div>
          ) : topBirdsError ? (
            <ErrorDisplay error={topBirdsError} />
          ) : (
            <div className='h-[360px] sm:h-[400px] md:h-[420px]'>
              <PieChart data={topBirdsData} colors={pieChartColors} />
            </div>
          )}
        </div>

        {/* 标签展示 - 替换原来的Bird Sighting Regions */}
        <div
          className={`lg:col-span-3 ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} ${theme.card.border || ''} p-4 sm:p-6`}
          data-testid="tags-section"
        >
          <TagsDisplay
            className="w-full"
            maxHeight="h-[360px] sm:h-[400px] md:h-[420px]"
          />
        </div>
      </motion.div>
      {/* Timeline / Bird Data Upload Status */}
      <motion.div
        className={`w-full ${theme.card.bg} ${theme.card.rounded} ${theme.card.shadow} ${theme.card.border || ''} p-4 sm:p-6`}
        variants={getAnimationConfig(staggerItemVariants)}
        data-testid="timeline-section"
      >
        <div className='flex items-center justify-between mb-3 sm:mb-4'>
          <div className='flex items-center'>
            <DocumentTextIcon className={`w-6 h-6 sm:w-7 sm:h-7 mr-2 sm:mr-3 ${theme.brandColor}`} />
            <h2 className='text-xl sm:text-2xl font-semibold'>观鸟数据</h2>
          </div>
          {/* 观鸟数据上传按钮 */}
          <BirdDataUploadButton
            onUploadComplete={(result) => {
              console.log('📊 标题栏上传完成:', result);
              if (result.success) {
                // 刷新观鸟数据状态
                fetchBirdSightingStatus();
              }
            }}
            size="small"
            variant="secondary"
            showText={false}
            className="!px-2 !py-1.5"
          />
        </div>
        
        {/* 根据观鸟数据状态显示不同内容 */}
        {isLoadingBirdSightingStatus ? (
          <div className='flex justify-center items-center h-40'>
            <LoadingSpinner />
            <span className={`ml-3 ${theme.card.secondaryText}`}>检查观鸟数据状态...</span>
          </div>
        ) : birdSightingStatusError ? (
          <ErrorDisplay error={birdSightingStatusError} />
        ) : !hasBirdSightingData ? (
          // 没有观鸟数据时显示上传状态组件
          <BirdDataUploadStatus 
            onDataChange={(hasData, stats) => {
              console.log('📊 观鸟数据状态变化:', { hasData, stats });
              setHasBirdSightingData(hasData);
              setBirdSightingStats(stats || null);
              
              // 如果现在有数据了，获取观鸟记录
              if (hasData) {
                fetchBirdSightingRecords();
              }
            }}
            showFieldRequirements={true}
            showRefreshButton={true}
            hideUploadButton={true}
          />
        ) : (
          // 有观鸟数据时显示时间线
          <div>
            {/* 观鸟数据统计信息 */}
            {birdSightingStats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className={`${theme.card.text} text-2xl font-bold`}>
                    {birdSightingStats.totalRecords}
                  </div>
                  <div className={`${theme.card.secondaryText} text-sm`}>
                    观测记录
                  </div>
                </div>
                <div className="text-center">
                  <div className={`${theme.card.text} text-2xl font-bold`}>
                    {birdSightingStats.totalSpecies}
                  </div>
                  <div className={`${theme.card.secondaryText} text-sm`}>
                    鸟类物种
                  </div>
                </div>
                <div className="text-center">
                  <div className={`${theme.card.text} text-2xl font-bold`}>
                    {birdSightingStats.totalObservations}
                  </div>
                  <div className={`${theme.card.secondaryText} text-sm`}>
                    总观测次数
                  </div>
                </div>
                <div className="text-center">
                  <div className={`${theme.card.text} text-lg font-bold`}>
                    {birdSightingStats.latestRecord}
                  </div>
                  <div className={`${theme.card.secondaryText} text-sm`}>
                    最新记录
                  </div>
                </div>
              </div>
            )}
            
            {/* 时间线组件 */}
            {isLoadingBirdData ? (
              <div className='flex justify-center items-center h-40'>
                <LoadingSpinner />
                <span className={`ml-3 ${theme.card.secondaryText}`}>加载时间线数据...</span>
              </div>
            ) : birdDataError ? (
              <ErrorDisplay error={birdDataError} />
            ) : (
              <BirdSightingTimeline records={allBirdSightings} />
            )}
          </div>
        )}
      </motion.div>

    </motion.div>
  );
};

export default AnalyticsPage;
