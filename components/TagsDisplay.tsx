import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import { getAllTags } from '../services/api';
import type { TagRead, ApiError } from '../types';
import LoadingSpinner from './LoadingSpinner';
import { TagIcon, RefreshIcon } from './icons';

interface TagsDisplayProps {
  className?: string;
  maxHeight?: string;
}

const TagsDisplay: React.FC<TagsDisplayProps> = ({ 
  className = '', 
  maxHeight = 'h-[360px] sm:h-[400px] md:h-[420px]' 
}) => {
  const { theme } = useTheme();
  const navigate = useNavigate();

  const [tags, setTags] = useState<TagRead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTags = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('🏷️ 获取标签列表...');
      
      const tagsData = await getAllTags();
      setTags(tagsData);
      
      console.log(`✅ 成功获取 ${tagsData.length} 个标签`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load tags';
      console.error('❌ 获取标签失败:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  const handleTagClick = useCallback((tagName: string) => {
    const encodedTagName = encodeURIComponent(tagName);
    navigate(`/tags/${encodedTagName}`);
  }, [navigate]);

  const handleTagKeyDown = useCallback((event: React.KeyboardEvent, tagName: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTagClick(tagName);
    }
  }, [handleTagClick]);

  const handleRetry = useCallback(() => {
    fetchTags();
  }, [fetchTags]);

  // 主题适配的tag样式配置
  const getTagThemeStyles = () => {
    switch (theme.name) {
      case 'Modern Clean Pro':
        return {
          rounded: 'rounded-full',
          border: 'border border-transparent',
          shadow: '',
          transition: 'hover:shadow-md hover:scale-[1.02]'
        };
      case 'Nature Inspired':
        return {
          rounded: 'rounded-full',
          border: 'border border-lime-300/60 dark:border-green-600/60',
          shadow: '',
          transition: 'hover:shadow-lg hover:scale-[1.02] hover:border-lime-400/80 dark:hover:border-green-500/80'
        };
      case 'Neon Galaxy':
        return {
          rounded: 'rounded-lg',
          border: 'border border-cyan-500/40 dark:border-cyan-400/40',
          shadow: 'shadow-sm shadow-cyan-500/10 dark:shadow-cyan-400/10',
          transition: 'hover:shadow-cyan-500/30 hover:shadow-lg hover:scale-[1.02] hover:border-cyan-400/60 dark:hover:border-cyan-300/60'
        };
      case 'Arcade Flash':
        return {
          rounded: 'rounded',
          border: 'border-2 border-black dark:border-white',
          shadow: 'shadow-[2px_2px_0px_#000000] dark:shadow-[2px_2px_0px_#FFFFFF]',
          transition: 'hover:shadow-[3px_3px_0px_#000000] dark:hover:shadow-[3px_3px_0px_#FFFFFF] hover:translate-x-[-1px] hover:translate-y-[-1px] active:translate-x-[1px] active:translate-y-[1px] active:shadow-[1px_1px_0px_#000000] dark:active:shadow-[1px_1px_0px_#FFFFFF]'
        };
      case 'RetroTech Dark':
        return {
          rounded: 'rounded-lg',
          border: 'border border-slate-600/60',
          shadow: 'shadow-[1px_1px_0px_#2EE59D]',
          transition: 'hover:shadow-[2px_2px_0px_#6EE7B7] hover:scale-[1.02] hover:border-emerald-400/60'
        };
      default:
        return {
          rounded: 'rounded-full',
          border: 'border border-transparent',
          shadow: '',
          transition: 'hover:shadow-md hover:scale-[1.02]'
        };
    }
  };

  // 主题适配的样式配置
  const getThemeStyles = () => {
    switch (theme.name) {
      case 'Modern Clean Pro':
        return {
          container: 'bg-white dark:bg-slate-900',
          tag: 'bg-blue-50/80 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-800/30 text-blue-700 dark:text-blue-300',
          tagHover: 'hover:shadow-md hover:scale-[1.02]',
          emptyIcon: 'text-blue-400 dark:text-blue-500',
          errorButton: 'bg-blue-600 hover:bg-blue-700 text-white'
        };
      case 'Nature Inspired':
        return {
          container: 'bg-green-50 dark:bg-green-900/20',
          tag: 'bg-lime-100/80 hover:bg-lime-200 dark:bg-green-800/30 dark:hover:bg-green-700/40 text-green-800 dark:text-lime-200',
          tagHover: 'hover:shadow-lg hover:scale-[1.02]',
          emptyIcon: 'text-green-400 dark:text-lime-400',
          errorButton: 'bg-green-600 hover:bg-green-700 text-white'
        };
      case 'Neon Galaxy':
        return {
          container: 'bg-slate-900/50 dark:bg-slate-800/30',
          tag: 'bg-cyan-900/20 hover:bg-cyan-800/30 dark:bg-cyan-700/15 dark:hover:bg-cyan-600/25 text-cyan-200 dark:text-cyan-100',
          tagHover: 'hover:shadow-cyan-500/20 hover:shadow-lg hover:scale-[1.02]',
          emptyIcon: 'text-cyan-400 dark:text-cyan-300',
          errorButton: 'bg-cyan-600 hover:bg-cyan-500 text-white'
        };
      case 'Arcade Flash':
        return {
          container: 'bg-yellow-100 dark:bg-yellow-900/20',
          tag: 'bg-yellow-200/80 hover:bg-yellow-300 dark:bg-yellow-700/30 dark:hover:bg-yellow-600/40 text-yellow-900 dark:text-yellow-200',
          tagHover: 'hover:shadow-lg hover:scale-[1.02]',
          emptyIcon: 'text-yellow-500 dark:text-yellow-400',
          errorButton: 'bg-yellow-600 hover:bg-yellow-700 text-white'
        };
      case 'RetroTech Dark':
        return {
          container: 'bg-slate-800',
          tag: 'bg-slate-700/80 hover:bg-slate-600 text-emerald-400 hover:text-emerald-300',
          tagHover: 'hover:shadow-emerald-500/20 hover:shadow-lg hover:scale-[1.02]',
          emptyIcon: 'text-emerald-400',
          errorButton: 'bg-emerald-600 hover:bg-emerald-500 text-white'
        };
      default:
        return {
          container: theme.card.bg,
          tag: `${theme.button.secondary} ${theme.button.secondaryText}`,
          tagHover: 'hover:shadow-md hover:scale-[1.02]',
          emptyIcon: theme.brandColor,
          errorButton: `${theme.button.primary} ${theme.button.primaryText}`
        };
    }
  };

  const themeStyles = getThemeStyles();
  const tagThemeStyles = getTagThemeStyles();

  const renderContent = () => {
    if (isLoading) {
      return (
        <div 
          className="flex items-center justify-center h-full"
          data-testid="tags-loading"
        >
          <LoadingSpinner />
        </div>
      );
    }

    if (error) {
      return (
        <div 
          className="flex flex-col items-center justify-center h-full space-y-4"
          data-testid="tags-error-state"
        >
          <div className={`text-center ${theme.card.secondaryText}`}>
            <h3 className="text-lg font-semibold mb-2">加载标签失败</h3>
            <p className="text-sm">{error}</p>
          </div>
          <button
            onClick={handleRetry}
            className={`px-4 py-2 rounded-lg ${themeStyles.errorButton} ${theme.button.transition} hover:opacity-90`}
          >
            重试
          </button>
        </div>
      );
    }

    if (tags.length === 0) {
      return (
        <div 
          className="flex flex-col items-center justify-center h-full space-y-4"
          data-testid="tags-empty-state"
        >
          <TagIcon className={`w-16 h-16 ${themeStyles.emptyIcon} opacity-50`} />
          <div className={`text-center ${theme.card.secondaryText}`}>
            <h3 className="text-lg font-semibold mb-1">暂无标签</h3>
            <p className="text-sm">还没有创建任何标签</p>
          </div>
        </div>
      );
    }

    return (
      <div 
        className="flex flex-wrap gap-2 sm:gap-3 p-3"
        data-testid="tags-grid"
      >
        {tags.map((tag, index) => (
          <motion.div
            key={tag.id}
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{
              duration: 0.3,
              ease: [0.25, 1, 0.5, 1],
              delay: index * 0.05,
            }}
            className={`
              ${themeStyles.tag}
              ${tagThemeStyles.rounded}
              ${tagThemeStyles.border}
              ${tagThemeStyles.shadow}
              px-3 py-1.5 cursor-pointer
              transition-all duration-200 ease-in-out
              ${tagThemeStyles.transition}
              focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme.input.focusRing}
              text-sm font-medium
              select-none
              inline-flex items-center justify-center
              min-w-fit
            `}
            onClick={() => handleTagClick(tag.name)}
            onKeyDown={(e) => handleTagKeyDown(e, tag.name)}
            tabIndex={0}
            role="button"
            aria-label={`跳转到标签: ${tag.name}`}
            data-testid={`tag-item-${tag.id}`}
          >
            <span className="whitespace-nowrap" title={tag.name}>
              {tag.name}
            </span>
          </motion.div>
        ))}
      </div>
    );
  };

  return (
    <div className={`${className}`}>
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl sm:text-2xl font-semibold">Tags</h2>
        <button
          onClick={fetchTags}
          disabled={isLoading}
          className={`
            p-2 rounded-lg ${theme.button.secondary} ${theme.button.secondaryText}
            ${theme.button.transition} hover:opacity-80 disabled:opacity-50
            focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme.input.focusRing}
          `}
          title="刷新标签"
          data-testid="tags-refresh-button"
        >
          <RefreshIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* 标签容器 - 高度与Top 10 Bird绑定 */}
      <div 
        className={`
          ${maxHeight} ${themeStyles.container} ${theme.card.rounded} 
          ${theme.card.shadow} ${theme.card.border || ''} 
          overflow-y-auto overflow-x-hidden
        `}
        data-testid="tags-display-container"
      >
        {renderContent()}
      </div>
    </div>
  );
};

export default TagsDisplay;
