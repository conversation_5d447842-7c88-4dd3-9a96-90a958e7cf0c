import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { getBirdSightingDataStatus } from '../services/bird-sighting-api';
import BirdDataUploadButton from './BirdDataUploadButton';
import LoadingSpinner from './LoadingSpinner';
import type { BirdSightingStats, UploadResult } from '../types';

// Info icon component
const InfoIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);

// Chart icon component
const ChartIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
    />
  </svg>
);

// Refresh icon component
const RefreshIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
    />
  </svg>
);

// Document icon component
const DocumentIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
    />
  </svg>
);

export interface DataStatus {
  hasData: boolean;
  stats?: BirdSightingStats;
  suggestions: string[];
}

export interface BirdDataUploadStatusProps {
  className?: string;
  hideUploadButton?: boolean;
  compact?: boolean;
  showRefreshButton?: boolean;
  showFieldRequirements?: boolean;
  onDataChange?: (hasData: boolean, stats?: BirdSightingStats) => void;
}

const BirdDataUploadStatus: React.FC<BirdDataUploadStatusProps> = ({
  className = '',
  hideUploadButton = false,
  compact = false,
  showRefreshButton = false,
  showFieldRequirements = false,
  onDataChange,
}) => {
  const { theme } = useTheme();
  const [dataStatus, setDataStatus] = useState<DataStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 优化的数据状态缓存
  const dataStatusCache = useMemo(() => ({ data: null, timestamp: 0 }), []);
  const CACHE_DURATION = 5000; // 5秒缓存

  // Load data status with caching and performance optimization
  const loadDataStatus = useCallback(async () => {
    // 检查缓存
    const now = Date.now();
    if (dataStatusCache.data && (now - dataStatusCache.timestamp) < CACHE_DURATION) {
      console.log('📊 使用缓存的观鸟数据状态');
      setDataStatus(dataStatusCache.data);
      if (onDataChange) {
        onDataChange(dataStatusCache.data.hasData, dataStatusCache.data.stats);
      }
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('📊 获取观鸟数据状态');
      const status = await getBirdSightingDataStatus();
      
      // 更新缓存
      dataStatusCache.data = status;
      dataStatusCache.timestamp = now;
      
      setDataStatus(status);
      
      // Notify parent component about data change
      if (onDataChange) {
        onDataChange(status.hasData, status.stats);
      }
      
      console.log('📊 观鸟数据状态加载完成:', status);
    } catch (err) {
      console.error('❌ 获取观鸟数据状态失败:', err);
      const errorMessage = err instanceof Error ? err.message : '获取数据状态失败';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [onDataChange, dataStatusCache, CACHE_DURATION]);

  // Load data status on component mount
  useEffect(() => {
    loadDataStatus();
  }, [loadDataStatus]);

  // Handle upload completion
  const handleUploadComplete = useCallback((result: UploadResult) => {
    if (result.success) {
      console.log('✅ 上传完成，刷新数据状态');
      // Refresh data status after successful upload
      loadDataStatus();
    }
  }, [loadDataStatus]);

  // Handle retry
  const handleRetry = () => {
    loadDataStatus();
  };

  // Render loading state
  if (isLoading) {
    return (
      <div 
        className={`
          ${compact ? 'p-4' : 'p-6'} 
          ${theme.card.bg} 
          ${theme.card.rounded} 
          ${theme.card.shadow}
          ${className}
          transition-all duration-300 ease-in-out
          animate-pulse
        `.trim()}
        data-testid="bird-data-upload-status"
      >
        <div className="flex items-center justify-center space-x-3">
          <LoadingSpinner size="sm" />
          <span className={`${theme.card.text} text-sm`}>
            加载观鸟数据状态中...
          </span>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div 
        className={`
          ${compact ? 'p-4' : 'p-6'} 
          ${theme.card.bg} 
          ${theme.card.rounded} 
          ${theme.card.shadow}
          ${className}
          transition-all duration-300 ease-in-out
          transform hover:scale-105
        `.trim()}
        data-testid="bird-data-upload-status"
        role="region"
        aria-label="观鸟数据状态"
      >
        <div className="text-center">
          <div className="flex justify-center mb-3">
            <InfoIcon className="w-8 h-8 text-red-500" />
          </div>
          <h3 className={`${theme.card.text} font-medium mb-2`}>
            加载失败
          </h3>
          <p className={`${theme.card.secondaryText} text-sm mb-4`}>
            {error}
          </p>
          <button
            onClick={handleRetry}
            className={`
              inline-flex items-center gap-2 px-4 py-2
              ${theme.button.primary} 
              ${theme.button.primaryText}
              ${theme.card.rounded}
              transition-colors duration-200
              hover:opacity-90
            `.trim()}
          >
            <RefreshIcon className="w-4 h-4" />
            重试
          </button>
        </div>
      </div>
    );
  }

  // Render no data state
  if (!dataStatus?.hasData) {
    return (
      <div 
        className={`
          ${compact ? 'p-4' : 'p-6'} 
          ${theme.card.bg}
          transition-all duration-500 ease-in-out
          transform hover:shadow-lg 
          ${theme.card.rounded} 
          ${theme.card.shadow}
          ${className}
        `.trim()}
        data-testid="bird-data-upload-status"
        role="region"
        aria-label="观鸟数据状态"
      >
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <ChartIcon className={`w-12 h-12 ${theme.card.secondaryText}`} />
          </div>
          
          <h3 className={`${theme.card.text} text-lg font-medium mb-3`}>
            尚无观鸟数据
          </h3>
          
          <div role="status" className="space-y-2 mb-6">
            {dataStatus?.suggestions.map((suggestion, index) => (
              <p 
                key={index}
                className={`${theme.card.secondaryText} text-sm leading-relaxed`}
              >
                {suggestion}
              </p>
            ))}
          </div>

          {/* Upload button */}
          {!hideUploadButton && (
            <div className="mb-6">
              <BirdDataUploadButton
                onUploadComplete={handleUploadComplete}
                size={compact ? 'small' : 'medium'}
              />
            </div>
          )}

          {/* File format requirements */}
          <div className={`${theme.card.border ? theme.card.border : 'border border-gray-200'} ${theme.card.rounded} p-4 bg-gray-50 dark:bg-gray-800`}>
            <div className="flex items-start space-x-3">
              <DocumentIcon className={`w-5 h-5 ${theme.card.secondaryText} mt-0.5`} />
              <div className="flex-1">
                <h4 className={`${theme.card.text} font-medium text-sm mb-2`}>
                  支持的格式
                </h4>
                <p className={`${theme.card.secondaryText} text-xs mb-2`}>
                  .xlsx、.xls 格式的Excel文件
                </p>
                
                {showFieldRequirements && (
                  <div className="mt-3">
                    <h5 className={`${theme.card.text} font-medium text-xs mb-1`}>
                      必需字段
                    </h5>
                    <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                      <span className={theme.card.secondaryText}>鸟种编号</span>
                      <span className={theme.card.secondaryText}>中文名</span>
                      <span className={theme.card.secondaryText}>拉丁名</span>
                      <span className={theme.card.secondaryText}>英文名</span>
                      <span className={theme.card.secondaryText}>目</span>
                      <span className={theme.card.secondaryText}>科</span>
                      <span className={theme.card.secondaryText}>记录次数</span>
                      <span className={theme.card.secondaryText}>记录时间</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render data exists state
  return (
    <div 
      className={`
        ${compact ? 'p-4' : 'p-6'} 
        ${theme.card.bg} 
        ${theme.card.rounded} 
        ${theme.card.shadow}
        ${className}
        transition-all duration-500 ease-in-out
        transform hover:shadow-lg hover:-translate-y-1
      `.trim()}
      data-testid="bird-data-upload-status"
      role="region"
      aria-label="观鸟数据状态"
    >
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <ChartIcon className={`w-10 h-10 text-green-500`} />
        </div>
        
        <h3 className={`${theme.card.text} text-lg font-medium mb-4`}>
          观鸟数据已加载
        </h3>

        {/* Statistics display */}
        {dataStatus.stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center transform transition-all duration-300 hover:scale-110">
              <div className={`${theme.card.text} text-2xl font-bold animate-fade-in`}>
                {dataStatus.stats.totalRecords}
              </div>
              <div className={`${theme.card.secondaryText} text-sm`}>
                条记录
              </div>
            </div>
            <div className="text-center transform transition-all duration-300 hover:scale-110">
              <div className={`${theme.card.text} text-2xl font-bold animate-fade-in`}>
                {dataStatus.stats.totalSpecies}
              </div>
              <div className={`${theme.card.secondaryText} text-sm`}>
                个物种
              </div>
            </div>
            <div className="text-center transform transition-all duration-300 hover:scale-110">
              <div className={`${theme.card.text} text-2xl font-bold animate-fade-in`}>
                {dataStatus.stats.totalObservations}
              </div>
              <div className={`${theme.card.secondaryText} text-sm`}>
                次观测
              </div>
            </div>
            <div className="text-center transform transition-all duration-300 hover:scale-110">
              <div className={`${theme.card.text} text-lg font-bold animate-fade-in`}>
                {dataStatus.stats.latestRecord}
              </div>
              <div className={`${theme.card.secondaryText} text-sm`}>
                最新记录
              </div>
            </div>
          </div>
        )}

        {/* Status suggestions */}
        <div role="status" className="space-y-2 mb-6">
          {dataStatus.suggestions.map((suggestion, index) => (
            <p 
              key={index}
              className={`${theme.card.secondaryText} text-sm leading-relaxed`}
            >
              {suggestion}
            </p>
          ))}
        </div>

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          {!hideUploadButton && (
            <BirdDataUploadButton
              onUploadComplete={handleUploadComplete}
              size={compact ? 'small' : 'medium'}
              variant="secondary"
              showText={true}
            />
          )}
          
          {showRefreshButton && (
            <button
              onClick={loadDataStatus}
              className={`
                inline-flex items-center gap-2 px-4 py-2
                ${theme.button.secondary} 
                ${theme.button.secondaryText}
                ${theme.card.rounded}
                transition-colors duration-200
                hover:opacity-90
              `.trim()}
            >
              <RefreshIcon className="w-4 h-4" />
              刷新
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default BirdDataUploadStatus;