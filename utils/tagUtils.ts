/**
 * 标签处理工具函数
 */

/**
 * 去重并清理标签字符串
 * @param tagsString 逗号分隔的标签字符串
 * @returns 去重后的标签字符串
 */
export function deduplicateTags(tagsString: string): string {
  if (!tagsString || typeof tagsString !== 'string') {
    return '';
  }

  // 分割标签，去除空白字符，过滤空标签
  const tags = tagsString
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);

  // 使用Set去重（不区分大小写）
  const uniqueTags = new Set<string>();
  const result: string[] = [];

  for (const tag of tags) {
    const lowerTag = tag.toLowerCase();
    if (!uniqueTags.has(lowerTag)) {
      uniqueTags.add(lowerTag);
      result.push(tag); // 保持原始大小写
    }
  }

  return result.join(', ');
}

/**
 * 合并标签字符串并去重
 * @param existingTags 现有标签字符串
 * @param newTags 新增标签字符串
 * @returns 合并后去重的标签字符串
 */
export function mergeTags(existingTags: string, newTags: string): string {
  const combined = existingTags && newTags 
    ? `${existingTags}, ${newTags}`
    : existingTags || newTags || '';
    
  return deduplicateTags(combined);
}

/**
 * 验证标签字符串格式
 * @param tagsString 标签字符串
 * @returns 验证结果
 */
export function validateTags(tagsString: string): { isValid: boolean; error?: string } {
  if (!tagsString) {
    return { isValid: true };
  }

  const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  
  // 检查标签长度
  for (const tag of tags) {
    if (tag.length > 50) {
      return { isValid: false, error: `标签 "${tag}" 过长，最多50个字符` };
    }
    
    // 检查特殊字符（只允许字母、数字、中文、空格、连字符、下划线）
    if (!/^[\w\u4e00-\u9fa5\s\-_]+$/.test(tag)) {
      return { isValid: false, error: `标签 "${tag}" 包含无效字符` };
    }
  }

  // 检查标签总数
  if (tags.length > 20) {
    return { isValid: false, error: '标签数量不能超过20个' };
  }

  return { isValid: true };
}