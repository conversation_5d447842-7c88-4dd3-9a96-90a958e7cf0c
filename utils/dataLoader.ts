/**
 * 跨平台数据文件访问工具
 * 提供统一的数据文件加载接口，支持开发环境和生产环境的路径适配
 */

export interface DataLoadResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * 统一的数据文件路径获取函数 - 跨平台兼容
 */
export const getDataPath = (filename: string): string => {
  // 检查是否在Electron环境中
  if (typeof window !== 'undefined' && (window as any).electronAPI) {
    // Electron环境，使用相对路径
    return `./data/${filename}`;
  }
  
  // 检查是否在开发环境中
  if (process.env.NODE_ENV === 'development') {
    // 数据文件在data目录
    return `/data/${filename}`;
  }
  
  // 生产环境 (Web环境)
  return `/data/${filename}`;
};

/**
 * 平台特定的路径处理
 */
export const getPlatformSpecificPath = (basePath: string): string => {
  if (typeof process !== 'undefined' && process.platform === 'win32') {
    return basePath.replace(/\//g, '\\');
  }
  return basePath;
};

/**
 * 带重试机制的数据加载函数
 */
export const loadDataWithRetry = async <T>(
  url: string, 
  retries: number = 3,
  retryDelay: number = 1000
): Promise<DataLoadResult<T>> => {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      // 检查响应内容类型
      const contentType = response.headers.get('content-type');
      const text = await response.text();
      
      // 如果响应是HTML页面，这通常意味着路径被重定向到了应用的根页面
      if (text.startsWith('<!DOCTYPE') || text.startsWith('<html')) {
        console.error(`收到HTML页面而非JSON文件 from ${url}:`, text.substring(0, 100));
        throw new Error(`Path ${url} returned HTML instead of JSON file - check if file exists in public directory`);
      }
      
      // 尝试解析JSON
      try {
        const data: T = JSON.parse(text);
        return {
          success: true,
          data
        };
      } catch (parseError) {
        console.error(`JSON解析失败 from ${url}:`, text.substring(0, 100));
        throw new Error(`Invalid JSON from ${url}: ${parseError}`);
      }
    } catch (error: any) {
      console.warn(`数据加载尝试 ${i + 1}/${retries} 失败:`, error.message);
      
      if (i === retries - 1) {
        return {
          success: false,
          error: `Failed to load data from ${url}: ${error.message}`
        };
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, retryDelay * (i + 1)));
    }
  }
  
  return {
    success: false,
    error: `Failed to load data after ${retries} attempts`
  };
};

/**
 * 加载数据文件的主要函数
 */
export const loadDataFile = async <T>(
  filename: string,
  retries: number = 3
): Promise<DataLoadResult<T>> => {
  const primaryUrl = getDataPath(filename);
  console.log(`正在加载数据文件: ${primaryUrl}`);
  
  // 首先尝试主要路径
  let result = await loadDataWithRetry<T>(primaryUrl, 1);
  
  if (!result.success) {
    // 如果主要路径失败，尝试备用路径
    const alternativePaths = [];
    
    // 在开发模式下，尝试不同的路径组合
    if (process.env.NODE_ENV === 'development') {
      alternativePaths.push(`/data/${filename}`, `/public/data/${filename}`);
    } else {
      // 生产模式下的备用路径
      alternativePaths.push(`./data/${filename}`, `/data/${filename}`);
    }
    
    // 尝试每个备用路径
    for (const altPath of alternativePaths) {
      console.log(`尝试备用路径: ${altPath}`);
      result = await loadDataWithRetry<T>(altPath, 1);
      if (result.success) {
        console.log(`成功通过备用路径加载: ${altPath}`);
        break;
      }
    }
  }
  
  return result;
};

/**
 * 验证数据文件是否可访问
 */
export const validateDataFile = async (filename: string): Promise<boolean> => {
  try {
    const url = getDataPath(filename);
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error(`数据文件验证失败: ${filename}`, error);
    return false;
  }
};

/**
 * 批量验证数据文件
 */
export const validateAllDataFiles = async (
  filenames: string[]
): Promise<{ [filename: string]: boolean }> => {
  const results: { [filename: string]: boolean } = {};
  
  await Promise.all(
    filenames.map(async (filename) => {
      results[filename] = await validateDataFile(filename);
    })
  );
  
  return results;
};

/**
 * 获取数据文件的完整URL（用于调试）
 */
export const getDataFileUrl = (filename: string): string => {
  const path = getDataPath(filename);
  
  if (typeof window !== 'undefined') {
    return new URL(path, window.location.origin).href;
  }
  
  return path;
};

/**
 * 数据加载进度回调类型
 */
export type ProgressCallback = (loaded: number, total: number) => void;

/**
 * 带进度反馈的数据加载
 */
export const loadDataWithProgress = async <T>(
  filename: string,
  onProgress?: ProgressCallback
): Promise<DataLoadResult<T>> => {
  const url = getDataPath(filename);
  
  try {
    onProgress?.(0, 100);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    onProgress?.(50, 100);
    
    const data: T = await response.json();
    
    onProgress?.(100, 100);
    
    return {
      success: true,
      data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message
    };
  }
};

// 导出常用的数据文件名常量
export const DATA_FILES = {
  BIRD_SIGHTINGS: 'bird_sightings.json',
  TOP_BIRDS: 'top_birds.json'
} as const;

// 类型定义
export interface BirdSightingRecord {
  id: string;
  chineseName: string;
  latinName: string;
  englishName: string;
  order: string;
  family: string;
  count: number;
  recordDate: string;
}

export interface TopBirdData {
  name: string;
  value: number;
}