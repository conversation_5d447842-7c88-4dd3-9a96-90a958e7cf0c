/**
 * 观鸟数据 API 服务层
 * 
 * 提供与Electron主进程通信的观鸟数据管理API
 * 支持双模式：Electron本地模式和Web远程模式
 * 
 * 功能包括：
 * - Excel文件上传和数据导入
 * - 观鸟记录获取和管理
 * - 观鸟统计信息查询
 * - 数据清空和状态检查
 */

import { IS_ELECTRON } from '../constants';
import type {
  BirdSightingRecord,
  BirdSightingStats,
  BirdSightingListResponse,
  UploadResult,
  ParseResult,
  DatabaseResult,
} from '../types';

// 导入Schema验证函数
import {
  BirdSightingListResponseSchema,
  BirdSightingStatsSchema,
  UploadResultSchema,
  DatabaseResultSchema,
} from '../schemas';

import {
  safeParseApiResponse,
  formatApiErrorWithZod
} from './api-validation';

/**
 * 上传Excel文件并导入观鸟数据（覆盖模式）
 * @param filePath Excel文件路径
 * @returns 上传结果，包含导入的记录数量
 */
export async function uploadBirdSightingData(filePath: string): Promise<UploadResult> {
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.uploadBirdSightingData) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API上传观鸟数据');
      response = await window.electronAPI.uploadBirdSightingData(filePath);
    } else {
      // Web模式暂不支持，抛出错误
      console.warn('⚠️ API: Web模式暂不支持观鸟数据上传功能');
      throw new Error('Web模式暂不支持观鸟数据上传功能，请使用Electron桌面版本');
    }

    const result = safeParseApiResponse(response, UploadResultSchema, '上传观鸟数据响应数据格式错误');
    
    if (result.success) {
      console.log(`✅ API: 观鸟数据上传成功，导入 ${result.data?.importedCount} 条记录`);
    } else {
      console.error('❌ API: 观鸟数据上传失败:', result.error);
    }

    return result;
  } catch (error) {
    const errorMessage = `观鸟数据上传失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    throw formatApiErrorWithZod(error, 'upload-bird-sighting-data');
  }
}

/**
 * 获取所有观鸟数据记录
 * @returns 观鸟记录列表
 */
export async function getBirdSightingData(): Promise<BirdSightingListResponse> {
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.getBirdSightingData) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API获取观鸟数据');
      response = await window.electronAPI.getBirdSightingData();
    } else {
      // Web模式暂不支持，返回空数组
      console.warn('⚠️ API: Web模式暂不支持观鸟数据功能');
      response = { success: true, data: [] };
    }

    if (response.success && response.data) {
      // response.data 已经是正确格式的记录数组，直接使用
      const result = safeParseApiResponse(response.data, BirdSightingListResponseSchema, '获取观鸟数据响应数据格式错误');
      console.log(`📊 API: 获取观鸟数据成功，共 ${result.length} 条记录`);
      return { success: true, data: result };
    } else {
      console.error('❌ API: 获取观鸟数据失败:', response.error);
      return { success: false, error: response.error || '获取观鸟数据失败' };
    }
  } catch (error) {
    const errorMessage = `获取观鸟数据失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    throw formatApiErrorWithZod(error, 'get-bird-sighting-data');
  }
}

/**
 * 获取观鸟数据统计信息
 * @returns 统计信息包括总记录数、物种数、观测次数等
 */
export async function getBirdSightingStats(): Promise<BirdSightingStats> {
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.getBirdSightingStats) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API获取观鸟统计');
      response = await window.electronAPI.getBirdSightingStats();
    } else {
      // Web模式暂不支持，返回空统计
      console.warn('⚠️ API: Web模式暂不支持观鸟统计功能');
      response = {
        success: true,
        data: {
          totalRecords: 0,
          totalSpecies: 0,
          totalObservations: 0,
          latestRecord: undefined
        }
      };
    }

    if (response.success && response.data) {
      const result = safeParseApiResponse(response.data, BirdSightingStatsSchema, '获取观鸟统计响应数据格式错误');
      console.log('📊 API: 观鸟统计信息:', result);
      return result;
    } else {
      console.error('❌ API: 获取观鸟统计失败:', response.error);
      throw new Error(response.error || '获取观鸟统计失败');
    }
  } catch (error) {
    const errorMessage = `获取观鸟统计失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    throw formatApiErrorWithZod(error, 'get-bird-sighting-stats');
  }
}

/**
 * 检查是否有观鸟数据
 * @returns 如果有数据返回true，否则返回false
 */
export async function hasBirdSightingData(): Promise<boolean> {
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.hasBirdSightingData) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API检查观鸟数据状态');
      response = await window.electronAPI.hasBirdSightingData();
    } else {
      // Web模式暂不支持，返回false
      console.warn('⚠️ API: Web模式暂不支持观鸟数据功能');
      return false;
    }

    console.log(`📊 API: 观鸟数据状态检查结果: ${response ? '有数据' : '无数据'}`);
    return Boolean(response);
  } catch (error) {
    const errorMessage = `检查观鸟数据状态失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    // 检查失败时默认返回false，不抛出错误
    return false;
  }
}

/**
 * 清空所有观鸟数据
 * @returns 操作结果
 */
export async function clearBirdSightingData(): Promise<DatabaseResult> {
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.clearBirdSightingData) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API清空观鸟数据');
      
      // 显示确认对话框
      const confirmed = window.confirm('确定要清空所有观鸟数据吗？此操作不可撤销。');
      if (!confirmed) {
        console.log('🚫 API: 用户取消清空观鸟数据操作');
        return {
          success: false,
          error: '用户取消操作'
        };
      }
      
      response = await window.electronAPI.clearBirdSightingData();
    } else {
      // Web模式暂不支持
      console.warn('⚠️ API: Web模式暂不支持观鸟数据清空功能');
      throw new Error('Web模式暂不支持观鸟数据清空功能，请使用Electron桌面版本');
    }

    const result = safeParseApiResponse(response, DatabaseResultSchema, '清空观鸟数据响应数据格式错误');
    
    if (result.success) {
      console.log('✅ API: 观鸟数据清空成功');
    } else {
      console.error('❌ API: 观鸟数据清空失败:', result.error);
    }

    return result;
  } catch (error) {
    const errorMessage = `清空观鸟数据失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    throw formatApiErrorWithZod(error, 'clear-bird-sighting-data');
  }
}

/**
 * 显示文件选择对话框并上传Excel文件
 * @returns 上传结果
 */
export async function selectAndUploadBirdSightingFile(): Promise<UploadResult> {
  try {
    if (!IS_ELECTRON || !window.electronAPI?.showOpenDialog) {
      throw new Error('文件选择功能仅在Electron桌面版本中可用');
    }

    console.log('📁 API: 显示文件选择对话框');
    
    // 显示文件选择对话框
    const dialogResult = await window.electronAPI.showOpenDialog({
      title: '选择观鸟数据Excel文件',
      filters: [
        { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
        { name: '所有文件', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (dialogResult.canceled || !dialogResult.filePaths || dialogResult.filePaths.length === 0) {
      console.log('🚫 API: 用户取消文件选择');
      return {
        success: false,
        error: '用户取消文件选择'
      };
    }

    const filePath = dialogResult.filePaths[0];
    console.log('📁 API: 用户选择文件:', filePath);

    // 上传选中的文件
    return await uploadBirdSightingData(filePath);
  } catch (error) {
    const errorMessage = `文件选择和上传失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    throw formatApiErrorWithZod(error, 'select-and-upload-bird-sighting-file');
  }
}

/**
 * 批量操作：检查数据状态并提供相应的操作建议
 * @returns 数据状态和操作建议
 */
export async function getBirdSightingDataStatus(): Promise<{
  hasData: boolean;
  stats?: BirdSightingStats;
  suggestions: string[];
}> {
  try {
    console.log('📊 API: 获取观鸟数据完整状态');
    
    const hasData = await hasBirdSightingData();
    const suggestions: string[] = [];
    let stats: BirdSightingStats | undefined;

    if (hasData) {
      try {
        stats = await getBirdSightingStats();
        suggestions.push('数据已加载，可以查看统计信息和时间线图表');
        if (stats.totalRecords > 0) {
          suggestions.push(`当前共有 ${stats.totalRecords} 条观测记录，涵盖 ${stats.totalSpecies} 个物种`);
        }
        suggestions.push('可以上传新的Excel文件来更新数据（将覆盖现有数据）');
      } catch (error) {
        console.warn('⚠️ API: 获取统计信息失败:', error);
        suggestions.push('数据存在但获取统计信息失败，建议检查数据完整性');
      }
    } else {
      suggestions.push('当前没有观鸟数据，点击"上传数据"按钮导入Excel文件');
      suggestions.push('Excel文件应包含鸟种编号、中文名、拉丁名、英文名、目、科、记录次数、记录时间等字段');
    }

    const result = {
      hasData,
      stats,
      suggestions
    };

    console.log('📊 API: 观鸟数据状态:', result);
    return result;
  } catch (error) {
    const errorMessage = `获取观鸟数据状态失败: ${error instanceof Error ? error.message : String(error)}`;
    console.error('❌ API:', errorMessage);
    
    // 状态检查失败时返回默认状态
    return {
      hasData: false,
      suggestions: ['数据状态检查失败，请刷新页面重试']
    };
  }
}