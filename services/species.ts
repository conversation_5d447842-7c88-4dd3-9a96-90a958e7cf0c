import type { 
  SpeciesDictionarySearchOptions, 
  SpeciesDictionarySearchResult,
  SpeciesDictionaryEntry 
} from '../schemas/speciesDictionary';

declare global {
  interface Window {
    electronAPI?: {
      species: {
        searchSpecies: (options: SpeciesDictionarySearchOptions) => Promise<SpeciesDictionarySearchResult>;
        getSpeciesInfo: (speciesName: string) => Promise<SpeciesDictionaryEntry | null>;
        getSpeciesByFamily: (familyName: string, options?: { limit?: number }) => Promise<SpeciesDictionaryEntry[]>;
        getSpeciesByGenus: (genusName: string, options?: { limit?: number }) => Promise<SpeciesDictionaryEntry[]>;
      };
    };
  }
}

/**
 * 搜索物种
 * @param options 搜索选项
 * @returns 搜索结果
 */
export async function searchSpecies(options: SpeciesDictionarySearchOptions): Promise<SpeciesDictionarySearchResult> {
  if (!window.electronAPI?.species) {
    throw new Error('Species API not available');
  }

  try {
    const response = await window.electronAPI.species.searchSpecies(options);
    
    // Handle the wrapped response format from IPC
    if (response && typeof response === 'object' && 'success' in response) {
      if (!response.success) {
        throw new Error(response.error || 'Search failed');
      }
      return response.data;
    }
    
    // Fallback for direct response
    return response;
  } catch (error) {
    console.error('Species search error:', error);
    throw new Error(`搜索物种失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 获取物种详细信息
 * @param speciesName 物种名称
 * @returns 物种信息
 */
export async function getSpeciesInfo(speciesName: string): Promise<SpeciesDictionaryEntry | null> {
  if (!window.electronAPI?.species) {
    throw new Error('Species API not available');
  }

  try {
    const response = await window.electronAPI.species.getSpeciesInfo(speciesName);
    
    // Handle the wrapped response format from IPC
    if (response && typeof response === 'object' && 'success' in response) {
      if (!response.success) {
        throw new Error(response.error || 'Get species info failed');
      }
      return response.data;
    }
    
    // Fallback for direct response
    return response;
  } catch (error) {
    console.error('Get species info error:', error);
    throw new Error(`获取物种信息失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 按科查询物种
 * @param familyName 科名
 * @param options 查询选项
 * @returns 物种列表
 */
export async function getSpeciesByFamily(familyName: string, options?: { limit?: number }): Promise<SpeciesDictionaryEntry[]> {
  if (!window.electronAPI?.species) {
    throw new Error('Species API not available');
  }

  try {
    const response = await window.electronAPI.species.getSpeciesByFamily(familyName, options);
    
    // Handle the wrapped response format from IPC
    if (response && typeof response === 'object' && 'success' in response) {
      if (!response.success) {
        throw new Error(response.error || 'Get species by family failed');
      }
      return response.data;
    }
    
    // Fallback for direct response
    return response;
  } catch (error) {
    console.error('Get species by family error:', error);
    throw new Error(`按科查询物种失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 按属查询物种
 * @param genusName 属名
 * @param options 查询选项
 * @returns 物种列表
 */
export async function getSpeciesByGenus(genusName: string, options?: { limit?: number }): Promise<SpeciesDictionaryEntry[]> {
  if (!window.electronAPI?.species) {
    throw new Error('Species API not available');
  }

  try {
    const response = await window.electronAPI.species.getSpeciesByGenus(genusName, options);
    
    // Handle the wrapped response format from IPC
    if (response && typeof response === 'object' && 'success' in response) {
      if (!response.success) {
        throw new Error(response.error || 'Get species by genus failed');
      }
      return response.data;
    }
    
    // Fallback for direct response
    return response;
  } catch (error) {
    console.error('Get species by genus error:', error);
    throw new Error(`按属查询物种失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}