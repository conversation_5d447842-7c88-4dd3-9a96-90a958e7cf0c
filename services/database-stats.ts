/**
 * 数据库统计信息服务
 * 提供统一的数据库统计数据获取接口
 */

import { IS_ELECTRON } from '../constants';

// 数据库统计信息接口
export interface DatabaseStats {
  categoriesCount: number;
  imagesCount: number;
  tagsCount: number;
}

// 原始数据库统计响应接口
interface RawDatabaseStatsResponse {
  success: boolean;
  data?: {
    categories: { count: number };
    images: { count: number };
    tags: { count: number };
  };
  error?: string;
}

/**
 * 验证统计数据格式
 */
function validateStatsData(data: any): boolean {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const { categories, images, tags } = data;

  // 检查必需字段是否存在
  if (!categories || !images || !tags) {
    return false;
  }

  // 检查count字段是否为有效数字
  if (
    typeof categories.count !== 'number' ||
    typeof images.count !== 'number' ||
    typeof tags.count !== 'number'
  ) {
    return false;
  }

  // 检查是否为非负整数
  if (
    !Number.isInteger(categories.count) ||
    !Number.isInteger(images.count) ||
    !Number.isInteger(tags.count) ||
    categories.count < 0 ||
    images.count < 0 ||
    tags.count < 0
  ) {
    return false;
  }

  return true;
}

/**
 * 转换原始统计数据为标准格式
 */
function transformStatsData(rawData: RawDatabaseStatsResponse['data']): DatabaseStats {
  if (!rawData) {
    throw new Error('No database stats data received');
  }

  const { categories, images, tags } = rawData;

  // 首先检查必需字段是否存在
  if (!categories || !images || !tags) {
    throw new Error('Missing required database stats fields');
  }

  // 然后验证数据格式
  if (!validateStatsData(rawData)) {
    throw new Error('Invalid database stats format');
  }

  return {
    categoriesCount: categories.count,
    imagesCount: images.count,
    tagsCount: tags.count
  };
}

/**
 * 获取数据库统计信息
 * @returns Promise<DatabaseStats> 数据库统计信息
 * @throws Error 当获取失败或环境不支持时
 */
export async function getDatabaseStats(): Promise<DatabaseStats> {
  // 检查是否在Electron环境中
  if (!IS_ELECTRON || typeof window === 'undefined' || !window.electronAPI?.getDatabaseStats) {
    throw new Error('Database stats are only available in Electron environment');
  }

  try {
    console.log('📊 获取数据库统计信息...');
    
    const response: RawDatabaseStatsResponse = await window.electronAPI.getDatabaseStats();
    
    if (!response.success) {
      const errorMessage = response.error || 'Failed to get database stats';
      console.error('❌ 获取数据库统计信息失败:', errorMessage);
      throw new Error(errorMessage);
    }

    const stats = transformStatsData(response.data);
    
    console.log('✅ 数据库统计信息获取成功:', stats);
    return stats;
    
  } catch (error) {
    console.error('❌ 数据库统计信息获取异常:', error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error('Unknown error occurred while getting database stats');
  }
}

/**
 * 获取数据库统计信息（带缓存）
 * 注意：这是一个简单的内存缓存实现，仅在组件生命周期内有效
 */
let statsCache: { data: DatabaseStats; timestamp: number } | null = null;
const CACHE_DURATION = 30000; // 30秒缓存

export async function getDatabaseStatsWithCache(): Promise<DatabaseStats> {
  const now = Date.now();
  
  // 检查缓存是否有效
  if (statsCache && (now - statsCache.timestamp) < CACHE_DURATION) {
    console.log('📊 使用缓存的数据库统计信息');
    return statsCache.data;
  }
  
  // 获取新数据并缓存
  const stats = await getDatabaseStats();
  statsCache = {
    data: stats,
    timestamp: now
  };
  
  return stats;
}

/**
 * 清除统计信息缓存
 * 在数据发生变化时调用（如添加/删除分类、图片、标签）
 */
export function clearStatsCache(): void {
  statsCache = null;
  console.log('🗑️ 数据库统计信息缓存已清除');
}

/**
 * 格式化统计数字显示
 */
export function formatStatsNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * 获取统计信息摘要文本
 */
export function getStatsSummary(stats: DatabaseStats): string {
  const { categoriesCount, imagesCount, tagsCount } = stats;
  return `${categoriesCount} 个分类，${imagesCount} 张图片，${tagsCount} 个标签`;
}

// DatabaseStats 类型已在文件顶部导出
