/**
 * API 服务层 - 图片管理相关的 PUT 请求修复
 * 
 * 修复前的问题：
 * - 发送不必要的 null 值字段（如 category_id: null）
 * - 可能导致后端误解更新意图
 * - 请求体包含过多无用信息
 * 
 * 修复后的改进：
 * - 只发送需要更新的字段
 * - 使用 undefined 表示不更新的字段
 * - 过滤掉不必要的 null 值
 * - 提供更清晰的 API 调用语义
 */

import axios, { AxiosError } from 'axios';
import { API_BASE_URL, IS_ELECTRON } from '../constants';
import type {
  CategoryCreate,
  CategoryRead,
  CategoryReadWithImages,
  CategoryUpdate,
  CategoryListResponse,
  ImageRead,
  ImageUpdate,
  BodyUploadImage,
  SpeciesRead,
  SpeciesSuggestionsResponse,
  HTTPValidationError,
  ApiError,
  ValidationError,
  TagRead, // Added TagRead here
  TagCreate,
  TagUpdate,
} from '../types';
// 导入 Zod 相关函数和 Schema
import { 
  TokenResponseSchema,
  CategoryReadSchema,
  CategoryListResponseSchema,
  CategoryReadWithImagesSchema,
  ImageReadSchema,
  ImageListResponseSchema,
  TagReadSchema,
  TagListResponseSchema,
  SpeciesReadSchema,
  SpeciesSuggestionsResponseSchema,
  // 导入从 Schema 推导的类型
  type TokenResponse
} from '../schemas';
import { 
  safeParseApiResponse, 
  formatApiErrorWithZod
} from './api-validation';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
});

apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('access_token');
    const isAuthEndpoint =
      config.url?.includes('/api/send-verification') || config.url?.includes('/api/verify');

    if (
      token &&
      config.method &&
      !['get', 'head', 'options'].includes(config.method.toLowerCase()) &&
      !isAuthEndpoint
    ) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

function formatApiError(error: AxiosError | any, url: string = 'API'): ApiError {
  if (axios.isAxiosError(error)) {
    const serverError = error.response?.data as Partial<
      HTTPValidationError & { message?: string; error?: string; detail?: any }
    >;
    let message = `API 请求 ${error.config?.url || url} 失败。状态码: ${error.response?.status || 'N/A'}`;
    let details: ValidationError[] | undefined = undefined;

    if (error.response?.status === 401) {
      message = '未认证或Token已过期，请重新登录。';
    } else if (error.response?.status === 422) {
      message = '请求参数验证失败。';
      if (
        serverError?.detail &&
        Array.isArray(serverError.detail) &&
        serverError.detail.length > 0
      ) {
        details = serverError.detail as ValidationError[];
        const firstDetailMsg = serverError.detail[0]?.msg || '请检查输入。';
        const firstDetailLoc = serverError.detail[0]?.loc
          ? ` (字段: ${Array.isArray(serverError.detail[0].loc) ? serverError.detail[0].loc.join('->') : serverError.detail[0].loc})`
          : '';
        message = `验证错误: ${firstDetailMsg}${firstDetailLoc}`;
        if (
          serverError.detail[0]?.msg?.toLowerCase().includes('email') &&
          serverError.detail[0]?.msg?.toLowerCase().includes('not allowed')
        ) {
          message = '邮箱不允许。';
        }
      } else {
        message = '邮箱不允许或请求参数无效。';
      }
    } else if (serverError) {
      if (typeof serverError.detail === 'string') {
        message = serverError.detail;
      } else if (serverError.message) {
        message = serverError.message;
      } else if (serverError.error) {
        message = serverError.error;
      } else if (
        Array.isArray(serverError.detail) &&
        serverError.detail.length > 0 &&
        serverError.detail[0].msg
      ) {
        details = serverError.detail as ValidationError[];
        message = `API 错误: ${serverError.detail[0].msg}`;
      } else {
        message = error.message || 'API 请求失败，响应体无法解析。';
      }
    } else {
      message = error.message || '发生未知网络错误。';
      if (
        error.message.toLowerCase().includes('network error') ||
        error.message.toLowerCase().includes('failed to fetch')
      ) {
        message = `无法连接到服务器 (${API_BASE_URL})。请检查网络连接或服务器状态。`;
      }
    }
    return { message, status: error.response?.status, details };
  }
  return { message: error.message || '发生未知错误。', status: undefined };
}

// Auth Endpoints
export async function sendVerificationCode(email: string): Promise<any> {
  const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
  
  if (isElectron) {
    // Electron模式：模拟发送验证码成功
    console.log('✅ Electron模式：模拟发送验证码到', email);
    return { message: 'Verification code sent (simulated in local mode)' };
  }
  
  // Web模式：使用远程API
  const url = `/api/send-verification?email=${encodeURIComponent(email)}`;
  try {
    const response = await apiClient.post(url);
    return response.data;
  } catch (error) {
    throw formatApiError(error, url);
  }
}

export async function verifyCodeAndGetToken(email: string, code: string): Promise<TokenResponse> {
  const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
  
  if (isElectron) {
    // Electron模式：模拟验证码验证成功，返回本地token
    console.log('✅ Electron模式：验证码验证 (模拟)，邮箱:', email, '验证码:', code);
    const localTokenResponse: TokenResponse = {
      access_token: 'electron-local-token',
      token_type: 'Bearer',
      expires_in: 86400 // 24小时
    };
    return localTokenResponse;
  }
  
  // Web模式：使用远程API
  const url = `/api/verify?email=${encodeURIComponent(email)}&code=${encodeURIComponent(code)}`;
  try {
    const response = await apiClient.post(url);
    // 使用 Zod 验证响应数据
    return safeParseApiResponse(response.data, TokenResponseSchema, '登录响应数据格式错误');
  } catch (error) {
    const formattedError = formatApiErrorWithZod(error, url);
    // 特殊错误信息处理
    if (
      formattedError.status === 422 &&
      formattedError.message.includes('Invalid verification code')
    ) {
      formattedError.message = '验证码错误或已过期。';
    } else if (formattedError.status === 422) {
      formattedError.message = '验证失败，请检查邮箱和验证码。';
    }
    throw formattedError;
  }
}

// Category Endpoints
export async function createCategory(categoryData: CategoryCreate): Promise<CategoryRead> {
  const url = `/api/categories/`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.createCategory) {
      // 使用本地Electron API
      response = await window.electronAPI.createCategory!(categoryData);
    } else {
      // 使用远程HTTP API
      const httpResponse = await apiClient.post(url, categoryData);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, CategoryReadSchema, '创建分类响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function getCategories(
  skip: number = 0,
  limit: number = 100
): Promise<CategoryListResponse> {
  const url = `/api/categories/?skip=${skip}&limit=${limit}`;
  
  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' && 
                     window.electronAPI && 
                     window.electronAPI.isElectron === true && 
                     typeof window.electronAPI.getCategories === 'function';
  
  try {
    let response;
    if (isElectron) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API获取分类');
      response = await window.electronAPI.getCategories!(skip, limit);
      console.log('📥 前端收到的分类数据:', response.length, '条');
      console.log('📋 前端收到的详细数据:', response.map((cat: any) => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        created_at: cat.created_at
      })));
    } else {
      // 使用远程HTTP API
      console.log('⚠️ API: 使用远程HTTP API获取分类');
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    const result = safeParseApiResponse(response, CategoryListResponseSchema, '获取分类列表响应数据格式错误');
    console.log('🎯 API返回给组件的最终数据:', result.length, '条');
    return result;
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function getCategoryWithImages(categoryId: string): Promise<CategoryReadWithImages> {
  const url = `/api/categories/${categoryId}/`;
  try {
    const isElectron = typeof window !== 'undefined' && 
                      window.electronAPI && 
                      window.electronAPI.isElectron === true && 
                      typeof window.electronAPI.getCategoryWithImages === 'function';
    
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API获取分类详情(含图片)');
      const response = await window.electronAPI.getCategoryWithImages!(categoryId);
      return safeParseApiResponse(response, CategoryReadWithImagesSchema, '获取分类详情响应数据格式错误');
    } else {
      console.log('⚠️ API: 使用远程HTTP API获取分类详情(含图片)');
      const response = await apiClient.get(url);
      return safeParseApiResponse(response.data, CategoryReadWithImagesSchema, '获取分类详情响应数据格式错误');
    }
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function getCategoryByName(categoryName: string): Promise<CategoryRead> {
  const url = `/api/categories/by-name/${encodeURIComponent(categoryName)}/`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.getCategories) {
      // 使用本地Electron API - 搜索匹配的分类
      console.log('✅ API: 使用本地Electron API根据名称获取分类');
      const categories = await window.electronAPI.getCategories!(0, 2000);
      const category = categories.find((cat: any) => 
        cat.name.toLowerCase().includes(categoryName.toLowerCase()) ||
        cat.id === categoryName
      );
      if (!category) {
        throw new Error(`未找到名称包含 "${categoryName}" 的分类`);
      }
      response = category;
    } else {
      // 使用远程HTTP API
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, CategoryReadSchema, '根据名称获取分类响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function updateCategory(
  categoryId: string,
  categoryData: CategoryUpdate
): Promise<CategoryRead> {
  const url = `/api/categories/${categoryId}/`;
  try {
    const response = await apiClient.patch(url, categoryData);
    return safeParseApiResponse(response.data, CategoryReadSchema, '更新分类响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function deleteCategory(categoryId: string): Promise<any> {
  const url = `/api/categories/${categoryId}/`;
  try {
    if (IS_ELECTRON && window.electronAPI?.deleteCategory) {
      // 使用本地Electron API - 返回详细的删除结果
      console.log('✅ API: 使用本地Electron API删除分类');
      const result = await window.electronAPI.deleteCategory!(categoryId);
      console.log('✅ API: 分类删除结果', result);
      return result;
    } else {
      // 使用远程HTTP API
      console.log('⚠️ API: 使用远程HTTP API删除分类');
      await apiClient.delete(url);
      return { success: true, message: '分类删除成功' };
    }
  } catch (error) {
    throw formatApiError(error, url);
  }
}

// Image Endpoints
export async function uploadImage(imageData: BodyUploadImage): Promise<ImageRead> {
  const url = `/api/images/upload/`;
  
  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' && 
                     window.electronAPI && 
                     window.electronAPI.isElectron === true && 
                     typeof window.electronAPI.uploadImage === 'function';
  
  try {
    if (isElectron) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API上传图片');
      
      // 将File对象转换为Buffer
      const arrayBuffer = await imageData.file.arrayBuffer();
      const fileBuffer = new Uint8Array(arrayBuffer);
      
      const response = await window.electronAPI.uploadImage!(
        imageData.category_id,
        fileBuffer,
        imageData.file.name,
        imageData.file.type,
        imageData.set_as_category_thumbnail || false
      );
      
      // 如果需要更新图片标题、描述或标签，调用updateImage
      if (imageData.title || imageData.description || imageData.tags) {
        console.log('API: 需要更新图片元数据');
        const updateData: any = {};
        if (imageData.title) updateData.title = imageData.title;
        if (imageData.description) updateData.description = imageData.description;
        if (imageData.tags) updateData.tags = imageData.tags;
        
        console.log('API: 更新数据:', updateData);
        return await window.electronAPI.updateImage!(response.id, updateData);
      }
      
      return safeParseApiResponse(response, ImageReadSchema, '图片上传响应数据格式错误');
    } else {
      // 使用远程HTTP API
      console.log('⚠️ API: 使用远程HTTP API上传图片');
      const formData = new FormData();
      formData.append('file', imageData.file);
      formData.append('category_id', imageData.category_id);
      if (imageData.title) {
        formData.append('title', imageData.title);
      }
      if (imageData.description) {
        formData.append('description', imageData.description);
      }
      if (imageData.tags) {
        formData.append('tags', imageData.tags);
      }
      if (
        imageData.set_as_category_thumbnail !== null &&
        imageData.set_as_category_thumbnail !== undefined
      ) {
        formData.append('set_as_category_thumbnail', String(imageData.set_as_category_thumbnail));
      }

      const response = await apiClient.post(url, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return safeParseApiResponse(response.data, ImageReadSchema, '上传图片响应数据格式错误');
    }
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function getImage(imageId: string): Promise<ImageRead> {
  const url = `/api/images/${imageId}/`;
  
  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' && 
                     window.electronAPI && 
                     window.electronAPI.isElectron === true && 
                     typeof window.electronAPI.getImageById === 'function';
  
  try {
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API获取图片详情');
      const response = await window.electronAPI.getImageById!(imageId);
      return safeParseApiResponse(response, ImageReadSchema, '获取图片详情响应数据格式错误');
    } else {
      console.log('⚠️ API: 使用远程HTTP API获取图片详情');
      const response = await apiClient.get(url);
      return safeParseApiResponse(response.data, ImageReadSchema, '获取图片详情响应数据格式错误');
    }
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

/**
 * 构建图片更新请求数据，只包含需要更新的字段
 * @param imageData 部分图片更新数据
 * @returns 清理后的更新数据
 */
function buildImageUpdatePayload(imageData: Partial<ImageUpdate>): Record<string, any> {
  const payload: Record<string, any> = {};
  
  // 只有当字段不是 undefined 时才包含在请求中
  if (imageData.title !== undefined) {
    payload.title = imageData.title;
  }
  if (imageData.description !== undefined) {
    payload.description = imageData.description;
  }
  if (imageData.tags !== undefined) {
    payload.tags = imageData.tags;
  }
  if (imageData.category_id !== undefined) {
    payload.category_id = imageData.category_id;
  }
  if (imageData.set_as_category_thumbnail !== undefined) {
    payload.set_as_category_thumbnail = imageData.set_as_category_thumbnail;
  }
  
  return payload;
}

export async function updateImageMetadata(
  imageId: string,
  imageData: Partial<ImageUpdate>
): Promise<ImageRead> {
  const url = `/api/images/${imageId}/`;
  
  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' && 
                     window.electronAPI && 
                     window.electronAPI.isElectron === true && 
                     typeof window.electronAPI.updateImage === 'function';
  
  try {
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API更新图片元数据');
      const response = await window.electronAPI.updateImage!(imageId, imageData);
      return safeParseApiResponse(response, ImageReadSchema, '更新图片元数据响应数据格式错误');
    } else {
      console.log('⚠️ API: 使用远程HTTP API更新图片元数据');
      
      // 构建清理后的请求数据
      const cleanedImageData = buildImageUpdatePayload(imageData);
      
      // 验证是否有实际需要更新的数据
      if (Object.keys(cleanedImageData).length === 0) {
        throw new Error('没有提供需要更新的字段');
      }
      
      const response = await apiClient.put(url, cleanedImageData);
      return safeParseApiResponse(response.data, ImageReadSchema, '更新图片元数据响应数据格式错误');
    }
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function deleteImage(imageId: string): Promise<any> {
  const url = `/api/images/${imageId}/`;

  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' &&
                     window.electronAPI &&
                     window.electronAPI.isElectron === true &&
                     typeof window.electronAPI.deleteImage === 'function';

  try {
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API删除图片');
      const result = await window.electronAPI.deleteImage!(imageId);

      if (result.success) {
        console.log('✅ API: 图片删除成功', {
          imageId,
          storageDeleted: result.storageDeleted,
          databaseDeleted: result.databaseDeleted
        });
      } else {
        console.error('❌ API: 图片删除失败', { imageId, error: result.error });
        throw new Error(result.error || '图片删除失败');
      }

      return result;
    } else {
      console.log('⚠️ API: 使用远程HTTP API删除图片');
      await apiClient.delete(url);
      return { success: true, message: '图片删除成功' };
    }
  } catch (error) {
    console.error('❌ API: 删除图片时发生错误', { imageId, error });
    throw formatApiError(error, url);
  }
}

/**
 * 批量删除图片
 */
export async function deleteImages(imageIds: string[]): Promise<any> {
  const url = `/api/images/batch-delete/`;

  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' &&
                     window.electronAPI &&
                     window.electronAPI.isElectron === true &&
                     typeof window.electronAPI.deleteImages === 'function';

  try {
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API批量删除图片', { count: imageIds.length });
      const result = await window.electronAPI.deleteImages!(imageIds);

      console.log('✅ API: 批量删除结果', {
        total: result.totalCount,
        success: result.successCount,
        failed: result.failedCount
      });

      return result;
    } else {
      console.log('⚠️ API: 使用远程HTTP API批量删除图片');
      await apiClient.post(url, { imageIds });
      return {
        success: true,
        message: '批量删除成功',
        totalCount: imageIds.length,
        successCount: imageIds.length,
        failedCount: 0
      };
    }
  } catch (error) {
    console.error('❌ API: 批量删除图片时发生错误', { imageIds, error });
    throw formatApiError(error, url);
  }
}

/**
 * 验证删除条件
 */
export async function validateDeleteConditions(imageId: string): Promise<any> {
  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' &&
                     window.electronAPI &&
                     window.electronAPI.isElectron === true &&
                     typeof window.electronAPI.validateDeleteConditions === 'function';

  try {
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API验证删除条件');
      const result = await window.electronAPI.validateDeleteConditions!(imageId);
      console.log('✅ API: 删除条件验证结果', {
        valid: result.valid,
        errors: result.errors.length,
        warnings: result.warnings.length
      });
      return result;
    } else {
      console.log('⚠️ API: 远程API不支持删除条件验证');
      return { valid: true, errors: [], warnings: [] };
    }
  } catch (error) {
    console.error('❌ API: 验证删除条件时发生错误', { imageId, error });
    throw error;
  }
}

/**
 * 显示删除确认对话框
 */
export async function showDeleteConfirmation(options: {
  type?: 'single' | 'batch';
  count?: number;
  itemName?: string;
}): Promise<{ confirmed: boolean }> {
  // 检测Electron环境
  const isElectron = typeof window !== 'undefined' &&
                     window.electronAPI &&
                     window.electronAPI.isElectron === true &&
                     typeof window.electronAPI.showDeleteConfirmation === 'function';

  try {
    if (isElectron) {
      console.log('✅ API: 使用本地Electron API显示删除确认对话框');
      const result = await window.electronAPI.showDeleteConfirmation!(options);
      console.log('✅ API: 删除确认结果', { confirmed: result.confirmed });
      return result;
    } else {
      console.log('⚠️ API: 远程API使用浏览器确认对话框');
      const { type = 'single', count = 1, itemName = '图片' } = options;
      const message = type === 'single'
        ? `确定要删除这张${itemName}吗？`
        : `确定要删除这 ${count} 张${itemName}吗？`;
      const confirmed = window.confirm(message);
      return { confirmed };
    }
  } catch (error) {
    console.error('❌ API: 显示删除确认对话框时发生错误', { options, error });
    throw error;
  }
}

export async function searchImagesByTag(tagQuery: string): Promise<ImageRead[]> {
  const tags = tagQuery
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
  if (tags.length === 0) {
    return Promise.resolve([]);
  }

  try {
    let imageIds: string[];
    if (IS_ELECTRON && window.electronAPI?.searchImagesByTags) {
      // 使用本地Electron API
      imageIds = await window.electronAPI.searchImagesByTags(tags);
    } else {
      // 使用远程HTTP API
      const queryParams = new URLSearchParams();
      tags.forEach(tag => queryParams.append('tag', tag));
      const url = `/api/images/by-tags/?${queryParams.toString()}`;
      const response = await apiClient.get(url);
      const images = safeParseApiResponse(response.data, ImageListResponseSchema, '按标签搜索图片响应数据格式错误');
      return images;
    }
    
    // 如果是本地API，需要根据图片ID获取完整的图片信息
    if (IS_ELECTRON && imageIds.length > 0) {
      const images: ImageRead[] = [];
      for (const imageId of imageIds) {
        if (window.electronAPI?.getImageById) {
          const image = await window.electronAPI.getImageById(imageId);
          if (image) {
            images.push(image);
          }
        }
      }
      return images;
    }
    
    return [];
  } catch (error) {
    const url = `/api/images/by-tags/?tag=${tags.join('&tag=')}`;
    throw formatApiErrorWithZod(error, url);
  }
}

// Tag Endpoints
export async function getAllTags(): Promise<TagRead[]> {
  const url = `/api/tags/`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.getAllTags) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API获取标签');
      response = await window.electronAPI.getAllTags();
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API获取标签');
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, TagListResponseSchema, '获取标签列表响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function createTag(tagData: TagCreate): Promise<TagRead> {
  const url = `/api/tags/`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.createTag) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API创建标签');
      response = await window.electronAPI.createTag(tagData);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API创建标签');
      const httpResponse = await apiClient.post(url, tagData);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, TagReadSchema, '创建标签响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function updateTag(tagId: string, tagData: TagUpdate): Promise<TagRead> {
  const url = `/api/tags/${tagId}/`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.updateTag) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API更新标签');
      response = await window.electronAPI.updateTag(tagId, tagData);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API更新标签');
      const httpResponse = await apiClient.patch(url, tagData);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, TagReadSchema, '更新标签响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function deleteTag(tagId: string): Promise<void> {
  const url = `/api/tags/${tagId}/`;
  try {
    if (IS_ELECTRON && window.electronAPI?.deleteTag) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API删除标签');
      await window.electronAPI.deleteTag(tagId);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API删除标签');
      await apiClient.delete(url);
    }
  } catch (error) {
    throw formatApiError(error, url);
  }
}

export async function searchTags(query: string): Promise<TagRead[]> {
  const url = `/api/tags/search/?q=${encodeURIComponent(query)}`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.searchTags) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API搜索标签');
      response = await window.electronAPI.searchTags(query);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API搜索标签');
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, TagListResponseSchema, '搜索标签响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function addTagToImage(imageId: string, tagId: string): Promise<void> {
  const url = `/api/images/${imageId}/tags/${tagId}/`;
  try {
    if (IS_ELECTRON && window.electronAPI?.addTagToImage) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API为图片添加标签');
      await window.electronAPI.addTagToImage(imageId, tagId);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API为图片添加标签');
      await apiClient.post(url);
    }
  } catch (error) {
    throw formatApiError(error, url);
  }
}

export async function removeTagFromImage(imageId: string, tagId: string): Promise<void> {
  const url = `/api/images/${imageId}/tags/${tagId}/`;
  try {
    if (IS_ELECTRON && window.electronAPI?.removeTagFromImage) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API从图片移除标签');
      await window.electronAPI.removeTagFromImage(imageId, tagId);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API从图片移除标签');
      await apiClient.delete(url);
    }
  } catch (error) {
    throw formatApiError(error, url);
  }
}

export async function getTagsForImage(imageId: string): Promise<TagRead[]> {
  const url = `/api/images/${imageId}/tags/`;
  try {
    let response;
    if (IS_ELECTRON && window.electronAPI?.getTagsForImage) {
      // 使用本地Electron API
      console.log('✅ API: 使用本地Electron API获取图片标签');
      response = await window.electronAPI.getTagsForImage(imageId);
    } else {
      // 使用远程HTTP API
      console.log('🌐 API: 使用远程HTTP API获取图片标签');
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, TagListResponseSchema, '获取图片标签响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

// Species Information Endpoints
export async function getSpeciesSuggestions(
  query: string,
  limit: number = 10
): Promise<SpeciesSuggestionsResponse> {
  const url = `/api/suggestions?q=${encodeURIComponent(query)}&limit=${limit}`;
  try {
    let response;
    if (IS_ELECTRON) {
      // 本地模式：返回一个占位符响应，表示物种识别功能需要联网
      console.log('🔌 API: 本地模式 - 物种识别功能需要联网');
      response = {
        suggestions: [],
        message: '物种识别功能需要网络连接，请在Web版本中使用此功能。'
      };
    } else {
      // 使用远程HTTP API
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, SpeciesSuggestionsResponseSchema, '获取物种建议响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}

export async function getSpeciesDetails(chineseName: string): Promise<SpeciesRead> {
  const url = `/api/details/${encodeURIComponent(chineseName)}`;
  try {
    let response;
    if (IS_ELECTRON) {
      // 本地模式：返回一个占位符响应，表示物种识别功能需要联网
      console.log('🔌 API: 本地模式 - 物种详情功能需要联网');
      response = {
        id: 'local-placeholder',
        chinese_name: chineseName,
        scientific_name: '本地模式不可用',
        description: '物种详情功能需要网络连接，请在Web版本中使用此功能。',
        habitat: '',
        characteristics: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    } else {
      // 使用远程HTTP API
      const httpResponse = await apiClient.get(url);
      response = httpResponse.data;
    }
    return safeParseApiResponse(response, SpeciesReadSchema, '获取物种详情响应数据格式错误');
  } catch (error) {
    throw formatApiErrorWithZod(error, url);
  }
}
