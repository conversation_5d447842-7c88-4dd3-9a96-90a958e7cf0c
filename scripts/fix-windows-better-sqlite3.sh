#!/bin/bash

# 修复Windows版本better-sqlite3的脚本
# 用于解决Linux编译的.node文件无法在Windows环境运行的问题
#
# 使用方法:
#   ./scripts/fix-windows-better-sqlite3.sh [ABI_VERSION]
#
# 参数:
#   ABI_VERSION: Electron ABI版本号，默认为119
#
# 示例:
#   ./scripts/fix-windows-better-sqlite3.sh 119
#   ./scripts/fix-windows-better-sqlite3.sh 118

set -e

# 显示使用帮助
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "📚 Windows Better-SQLite3 修复脚本"
    echo ""
    echo "用法: $0 [ABI_VERSION]"
    echo ""
    echo "参数:"
    echo "  ABI_VERSION    Electron ABI版本号 (默认: 119)"
    echo ""
    echo "常用ABI版本:"
    echo "  119 - Electron 28.x"
    echo "  118 - Electron 27.x"
    echo "  116 - Electron 25.x"
    echo ""
    echo "示例:"
    echo "  $0 119      # 使用ABI v119"
    echo "  $0 118      # 使用ABI v118"
    echo ""
    echo "注意: 请先运行 'npm run electron:dist:win' 构建Windows版本"
    exit 0
fi

echo "🔧 开始修复Windows版本的better-sqlite3..."

# 检查参数
ELECTRON_ABI=${1:-119}  # 默认使用v119
SQLITE_VERSION="11.10.0"

echo "📦 使用Electron ABI版本: v${ELECTRON_ABI}"

# 定义缓存路径
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
CACHE_DIR="${PROJECT_ROOT}/cache/better-sqlite3"
CACHE_VERSION_DIR="${CACHE_DIR}/v${SQLITE_VERSION}-electron-v${ELECTRON_ABI}-win32-x64"
CACHED_BINARY="${CACHE_VERSION_DIR}/better_sqlite3.node"

echo "📂 缓存目录: ${CACHE_VERSION_DIR}"

# 检查缓存文件是否存在
if [ -f "${CACHED_BINARY}" ]; then
    echo "✅ 找到缓存文件: ${CACHED_BINARY}"
    echo "📋 文件信息:"
    ls -lh "${CACHED_BINARY}"
    file "${CACHED_BINARY}"
else
    echo "❌ 缓存文件不存在，开始下载..."
    
    # 创建缓存目录
    mkdir -p "${CACHE_VERSION_DIR}"
    
    # 下载对应版本
    DOWNLOAD_URL="https://github.com/WiseLibs/better-sqlite3/releases/download/v${SQLITE_VERSION}/better-sqlite3-v${SQLITE_VERSION}-electron-v${ELECTRON_ABI}-win32-x64.tar.gz"
    TEMP_TAR="/tmp/better-sqlite3-win32-${ELECTRON_ABI}.tar.gz"
    
    echo "⬇️ 下载预编译文件: ${DOWNLOAD_URL}"
    wget -O "${TEMP_TAR}" "${DOWNLOAD_URL}"
    
    # 解压到临时目录
    echo "📂 解压文件到临时目录..."
    TEMP_EXTRACT_DIR="/tmp/better-sqlite3-extract-${ELECTRON_ABI}"
    rm -rf "${TEMP_EXTRACT_DIR}"
    mkdir -p "${TEMP_EXTRACT_DIR}"
    tar -xzf "${TEMP_TAR}" -C "${TEMP_EXTRACT_DIR}"
    
    # 复制到缓存目录
    echo "💾 保存到缓存目录..."
    cp "${TEMP_EXTRACT_DIR}/build/Release/better_sqlite3.node" "${CACHED_BINARY}"
    
    # 清理临时文件
    rm -f "${TEMP_TAR}"
    rm -rf "${TEMP_EXTRACT_DIR}"
    
    echo "✅ 缓存文件已保存: ${CACHED_BINARY}"
fi

# 自动检测目标路径
# 首先尝试找到release目录下的Windows构建
RELEASE_DIR="${PROJECT_ROOT}/release"
WIN_UNPACKED_DIR=""

# 查找Windows构建目录 (支持多种命名模式)
for dir in "${RELEASE_DIR}"/win-unpacked "${RELEASE_DIR}"/Pokedex-*-win32-x64-unpacked "${RELEASE_DIR}"/win-ia32-unpacked; do
    if [ -d "$dir" ]; then
        WIN_UNPACKED_DIR="$dir"
        break
    fi
done

if [ -z "${WIN_UNPACKED_DIR}" ]; then
    echo "❌ 未找到Windows构建目录"
    echo "请先运行 npm run electron:dist:win 构建Windows版本"
    echo "查找路径: ${RELEASE_DIR}/win-unpacked"
    exit 1
fi

TARGET_PATH="${WIN_UNPACKED_DIR}/resources/app.asar.unpacked/node_modules/better-sqlite3/build/Release/better_sqlite3.node"

echo "🔍 检查目标路径: ${WIN_UNPACKED_DIR}"
echo "📂 Windows构建目录: $(basename "${WIN_UNPACKED_DIR}")"

if [ ! -f "${TARGET_PATH}" ]; then
    echo "❌ 目标文件不存在: ${TARGET_PATH}"
    echo ""
    echo "请确认已构建Windows版本："
    echo "   npm run electron:dist:win"
    echo ""
    echo "构建完成后，Windows文件应位于："
    echo "   ${RELEASE_DIR}/win-unpacked/ 或"
    echo "   ${RELEASE_DIR}/Pokedex-*-win32-x64-unpacked/"
    exit 1
fi

# 备份原文件
echo "💾 备份原始文件..."
cp "${TARGET_PATH}" "${TARGET_PATH}.backup"

# 替换文件
echo "🔄 替换better-sqlite3模块..."
cp "${CACHED_BINARY}" "${TARGET_PATH}"

# 验证替换
echo "✅ 验证文件类型..."
file "${TARGET_PATH}"

if file "${TARGET_PATH}" | grep -q "PE32+"; then
    echo "🎉 成功！better-sqlite3已替换为Windows版本"
    echo "📍 位置: ${TARGET_PATH}"
    echo ""
    echo "🧪 现在可以测试Windows应用："
    echo "   export DISPLAY=:99"
    echo "   cd \"${WIN_UNPACKED_DIR}\""
    # 查找.exe文件
    EXE_FILE=$(find "${WIN_UNPACKED_DIR}" -name "*.exe" -type f | head -1)
    if [ -n "${EXE_FILE}" ]; then
        EXE_NAME=$(basename "${EXE_FILE}")
        echo "   wine \"${EXE_NAME}\" --no-sandbox"
    else
        echo "   wine <应用名>.exe --no-sandbox"
    fi
else
    echo "❌ 替换失败，文件格式不正确"
    echo "🔄 恢复备份文件..."
    cp "${TARGET_PATH}.backup" "${TARGET_PATH}"
    exit 1
fi

echo "✨ 修复完成！"
echo "📦 缓存文件位置: ${CACHED_BINARY}"
echo "🎯 目标文件位置: ${TARGET_PATH}"
echo "💡 下次运行将直接使用缓存文件，无需重新下载"