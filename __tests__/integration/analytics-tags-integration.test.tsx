import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import React from 'react';
import AnalyticsPage from '../../components/AnalyticsPage';
import TagsDisplay from '../../components/TagsDisplay';
import TagPage from '../../components/TagPage';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';
import type { DatabaseStats } from '../../services/database-stats';

// Mock services
vi.mock('../../services/database-stats', () => ({
  getDatabaseStats: vi.fn(),
  formatStatsNumber: vi.fn((num: number) => num.toString()),
}));

vi.mock('../../services/api', () => ({
  searchImagesByTag: vi.fn(),
  deleteTag: vi.fn(),
  getAllTags: vi.fn(),
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ tagName: '鸟类摄影' }),
  };
});

// Mock AuthContext
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: '1', username: 'testuser' }
  })
}));

// Mock utils
vi.mock('../../utils/dataLoader', () => ({
  loadDataFile: vi.fn(),
  DATA_FILES: {
    BIRD_SIGHTINGS: 'bird-sightings.json',
    TOP_BIRDS: 'top-birds.json'
  }
}));

vi.mock('../../utils/animations', () => ({
  fadeInVariants: {},
  staggerContainerVariants: {},
  staggerItemVariants: {},
  getAnimationConfig: (variants: any) => variants
}));

// Mock components
vi.mock('../../components/PieChart', () => ({
  default: ({ data, colors }: { data: any; colors: any }) => (
    <div data-testid="pie-chart">Mocked PieChart</div>
  )
}));

vi.mock('../../components/BirdSightingTimeline', () => ({
  default: ({ records }: { records: any }) => (
    <div data-testid="bird-sighting-timeline">Mocked Timeline</div>
  )
}));

vi.mock('../../components/StatCard', () => ({
  default: ({ icon, title, value, isLoading, hasError, testId }: { 
    icon: any; 
    title: string; 
    value: number; 
    isLoading?: boolean; 
    hasError?: boolean; 
    testId?: string; 
  }) => (
    <div data-testid={testId}>
      <span data-testid="stat-title">{title}</span>
      <span data-testid="stat-value">{isLoading ? 'Loading...' : hasError ? 'Error' : value}</span>
    </div>
  )
}));

vi.mock('../../components/LoadingSpinner', () => ({
  default: ({ size }: { size?: string }) => (
    <div data-testid="loading-spinner">Loading...</div>
  )
}));

vi.mock('../../components/ErrorDisplay', () => ({
  default: ({ error }: { error: string }) => (
    <div data-testid="error-display">Error: {error}</div>
  )
}));

import { getDatabaseStats } from '../../services/database-stats';
import { getAllTags, deleteTag, searchImagesByTag } from '../../services/api';

const mockGetDatabaseStats = getDatabaseStats as any;
const mockGetAllTags = getAllTags as any;
const mockDeleteTag = deleteTag as any;
const mockSearchImagesByTag = searchImagesByTag as any;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('Analytics-Tags Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('持续测试覆盖范围', () => {
    describe('数据库统计数据与实际数据一致性', () => {
      it('应该确保统计数据与实际标签数量一致', async () => {
        const mockStats: DatabaseStats = {
          categoriesCount: 15,
          imagesCount: 250,
          tagsCount: 5
        };

        const mockTags = [
          { id: '1', name: '鸟类摄影', imageCount: 50 },
          { id: '2', name: '风景摄影', imageCount: 30 },
          { id: '3', name: '微距摄影', imageCount: 20 },
          { id: '4', name: '街拍', imageCount: 15 },
          { id: '5', name: '人像摄影', imageCount: 10 }
        ];

        mockGetDatabaseStats.mockResolvedValue(mockStats);
        mockGetAllTags.mockResolvedValue(mockTags);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        // 等待数据加载
        await waitFor(() => {
          expect(screen.getByText('5')).toBeInTheDocument(); // 标签数量
        });

        // 验证统计数据显示正确
        expect(screen.getByText('15')).toBeInTheDocument(); // 种类数
        expect(screen.getByText('250')).toBeInTheDocument(); // 图片数
        expect(screen.getByText('5')).toBeInTheDocument(); // 标签数

        // 验证API调用
        expect(mockGetDatabaseStats).toHaveBeenCalledTimes(1);
      });

      it('应该正确处理空数据状态', async () => {
        const mockStats: DatabaseStats = {
          categoriesCount: 0,
          imagesCount: 0,
          tagsCount: 0
        };

        mockGetDatabaseStats.mockResolvedValue(mockStats);
        mockGetAllTags.mockResolvedValue([]);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        // 等待数据加载
        await waitFor(() => {
          expect(screen.getAllByText('0')).toHaveLength(3);
        });

        // 验证所有统计数据都为0
        const statValues = screen.getAllByText('0');
        expect(statValues).toHaveLength(3); // 种类数、图片数、标签数
      });
    });

    describe('标签删除后统计数据更新', () => {
      it('应该在删除标签后正确更新统计数据', async () => {
        // 初始状态
        const initialStats: DatabaseStats = {
          categoriesCount: 15,
          imagesCount: 250,
          tagsCount: 5
        };

        // 删除后状态
        const updatedStats: DatabaseStats = {
          categoriesCount: 15,
          imagesCount: 230, // 减少了20张图片
          tagsCount: 4 // 减少了1个标签
        };

        mockGetDatabaseStats
          .mockResolvedValueOnce(initialStats)
          .mockResolvedValueOnce(updatedStats);
        
        mockSearchImagesByTag.mockResolvedValue([
          { id: '1', filename: 'bird1.jpg', tags: ['鸟类摄影'] },
          { id: '2', filename: 'bird2.jpg', tags: ['鸟类摄影'] }
        ]);
        
        mockDeleteTag.mockResolvedValue(undefined);

        // 模拟删除操作的完整流程
        const deleteResult = await mockDeleteTag('鸟类摄影');
        expect(deleteResult).toBeUndefined();
        expect(mockDeleteTag).toHaveBeenCalledWith('鸟类摄影');
      });
    });

    describe('跨组件数据同步', () => {
      it('应该确保TagsDisplay和统计数据保持同步', async () => {
        const mockStats: DatabaseStats = {
          categoriesCount: 15,
          imagesCount: 250,
          tagsCount: 3
        };

        const mockTags = [
          { id: '1', name: '鸟类摄影', imageCount: 50 },
          { id: '2', name: '风景摄影', imageCount: 30 },
          { id: '3', name: '微距摄影', imageCount: 20 }
        ];

        mockGetDatabaseStats.mockResolvedValue(mockStats);
        mockGetAllTags.mockResolvedValue(mockTags);

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        // 等待标签加载
        await waitFor(() => {
          expect(mockGetAllTags).toHaveBeenCalledTimes(1);
        });

        // 验证标签数量与统计数据一致
        expect(mockTags).toHaveLength(mockStats.tagsCount);
      });
    });

    describe('关键业务流程完整性', () => {
      it('应该支持完整的标签管理流程', async () => {
        const mockTags = [
          { id: '1', name: '鸟类摄影', imageCount: 50 }
        ];

        mockGetAllTags.mockResolvedValue(mockTags);
        mockSearchImagesByTag.mockResolvedValue([
          { id: '1', filename: 'bird1.jpg', tags: ['鸟类摄影'] }
        ]);
        mockDeleteTag.mockResolvedValue(undefined);

        // 1. 查看标签列表
        expect(mockTags).toHaveLength(1);
        expect(mockTags[0].name).toBe('鸟类摄影');

        // 2. 查看标签详情
        const tagImages = await mockSearchImagesByTag('鸟类摄影');
        expect(tagImages).toHaveLength(1);
        expect(mockSearchImagesByTag).toHaveBeenCalledWith('鸟类摄影');

        // 3. 删除标签
        await mockDeleteTag('鸟类摄影');
        expect(mockDeleteTag).toHaveBeenCalledWith('鸟类摄影');
      });
    });
  });

  describe('一次性验证项目（不需要持续测试）', () => {
    describe('界面跳转流程', () => {
      it('应该支持从Analytics页面跳转到标签页面', () => {
        // 这个测试验证路由配置正确
        expect(mockNavigate).toBeDefined();
        
        // 模拟点击标签跳转
        mockNavigate('/tags/鸟类摄影');
        expect(mockNavigate).toHaveBeenCalledWith('/tags/鸟类摄影');
      });
    });

    describe('基本性能指标', () => {
      it('应该在合理时间内加载数据', async () => {
        const startTime = Date.now();
        
        mockGetDatabaseStats.mockResolvedValue({
          categoriesCount: 15,
          imagesCount: 250,
          tagsCount: 5
        });

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('5')).toBeInTheDocument();
        });

        const endTime = Date.now();
        const loadTime = endTime - startTime;
        
        // 验证加载时间在合理范围内（小于1秒）
        expect(loadTime).toBeLessThan(1000);
      });
    });
  });
});
