import { vi, describe, it, expect, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import ImageUploadForm from '../../components/ImageUploadForm';
import { CategoryProvider } from '../../contexts/CategoryContext';
import { ThemeProvider } from '../../contexts/ThemeContext';
import * as speciesService from '../../services/species';
import * as apiService from '../../services/api';
import type { 
  SpeciesDictionaryEntry
} from '../../schemas/speciesDictionary';
import type { 
  BodyUploadImage, 
  ApiError,
  CategoryReadWithImages
} from '../../types';

// Mock dependencies
vi.mock('../../services/species', () => ({
  getSpeciesInfo: vi.fn(),
}));

vi.mock('../../services/api', () => ({
  getCategories: vi.fn(),
}));

// Mock providers
const mockCategories: CategoryReadWithImages[] = [
  {
    id: '1',
    name: '喜鹊',
    description: '鸦科鸟类',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    thumbnail_path: null,
    thumbnail_url: null,
    images: [],
  },
  {
    id: '2',
    name: '白头鹎',
    description: '鹎科鸟类',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    thumbnail_path: null,
    thumbnail_url: null,
    images: [],
  },
  {
    id: '3',
    name: '不存在的鸟',
    description: '测试用鸟类',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    thumbnail_path: null,
    thumbnail_url: null,
    images: [],
  },
];

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    <CategoryProvider>
      {children}
    </CategoryProvider>
  </ThemeProvider>
);

describe('物种标签匹配集成测试', () => {
  const mockOnSubmit = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    categoryId: '1',
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    isLoading: false,
    error: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock API服务
    vi.mocked(apiService.getCategories).mockResolvedValue(mockCategories);
  });

  describe('完整用户流程测试', () => {
    it('应该完成完整的物种标签匹配和添加流程', async () => {
      const mockSpeciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待组件加载和分类数据获取
      await waitFor(() => {
        expect(vi.mocked(apiService.getCategories)).toHaveBeenCalled();
      });

      // 等待物种标签建议组件出现
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      // 验证物种信息显示
      expect(screen.getByText(/添加喜鹊对应目科属/)).toBeInTheDocument();
      expect(screen.getByText(/雀形目·鸦科·鹊属/)).toBeInTheDocument();

      // 点击添加按钮
      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);

      // 验证标签字段已更新
      const tagsInput = screen.getByLabelText('Tags (Optional, comma-separated)');
      expect(tagsInput).toHaveValue('雀形目,鸦科,鹊属');

      // 验证API调用
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('喜鹊');
    });

    it('应该在已有标签的情况下正确追加新标签', async () => {
      const mockSpeciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待组件加载
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      // 先输入一些标签
      const tagsInput = screen.getByLabelText('标签（可选，逗号分隔）');
      fireEvent.change(tagsInput, { target: { value: '鸟类摄影, 野生动物' } });

      // 点击添加物种标签
      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);

      // 验证标签正确追加
      expect(tagsInput).toHaveValue('鸟类摄影, 野生动物,雀形目,鸦科,鹊属');
    });

    it('应该处理多个不同物种的切换', async () => {
      const mockSpeciesData1: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      const mockSpeciesData2: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鹎科',
        属: '白头鹎属',
        种: '白头鹎',
      };

      vi.mocked(speciesService.getSpeciesInfo)
        .mockResolvedValueOnce(mockSpeciesData1)
        .mockResolvedValueOnce(mockSpeciesData2);

      const { rerender } = render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待第一个物种信息加载
      await waitFor(() => {
        expect(screen.getByText(/雀形目·鸦科·鹊属/)).toBeInTheDocument();
      });

      // 切换到白头鹎
      rerender(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} categoryId="2" />
        </TestWrapper>
      );

      // 等待第二个物种信息加载
      await waitFor(() => {
        expect(screen.getByText(/添加白头鹎对应目科属/)).toBeInTheDocument();
        expect(screen.getByText(/雀形目·鹎科·白头鹎属/)).toBeInTheDocument();
      });

      // 验证API调用
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('喜鹊');
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('白头鹎');
    });
  });

  describe('组件间协作测试', () => {
    it('应该在CategoryProvider加载完成后正确显示分类名称', async () => {
      const mockSpeciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待CategoryProvider加载分类数据
      await waitFor(() => {
        expect(vi.mocked(apiService.getCategories)).toHaveBeenCalled();
      });

      // 验证物种查询API被正确调用
      await waitFor(() => {
        expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('喜鹊');
      });

      // 验证组件正确显示
      expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
    });

    it('应该在分类不存在时不显示物种标签建议', async () => {
      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} categoryId="999" />
        </TestWrapper>
      );

      // 等待CategoryProvider加载完成
      await waitFor(() => {
        expect(vi.mocked(apiService.getCategories)).toHaveBeenCalled();
      });

      // 验证物种查询API没有被调用
      expect(vi.mocked(speciesService.getSpeciesInfo)).not.toHaveBeenCalled();

      // 验证组件不显示
      expect(screen.queryByTestId('species-tag-suggestion')).not.toBeInTheDocument();
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成物种匹配', async () => {
      const mockSpeciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      // 模拟延迟响应
      vi.mocked(speciesService.getSpeciesInfo).mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 50)); // 50ms延迟
        return mockSpeciesData;
      });

      const startTime = Date.now();

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待组件渲染完成
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const endTime = Date.now();
      const loadTime = endTime - startTime;

      // 验证加载时间小于100ms（不包括人为延迟）
      expect(loadTime).toBeLessThan(200);
    });

    it('应该正确处理并发物种查询', async () => {
      const mockSpeciesData1: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      const mockSpeciesData2: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鹎科',
        属: '白头鹎属',
        种: '白头鹎',
      };

      // 模拟不同的查询结果
      vi.mocked(speciesService.getSpeciesInfo)
        .mockResolvedValueOnce(mockSpeciesData1)
        .mockResolvedValueOnce(mockSpeciesData2)
        .mockResolvedValueOnce(mockSpeciesData1);

      const { rerender } = render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待第一次加载完成
      await waitFor(() => {
        expect(screen.getByText(/添加喜鹊对应目科属/)).toBeInTheDocument();
      });

      // 快速切换分类
      rerender(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} categoryId="2" />
        </TestWrapper>
      );

      // 等待第二次加载完成
      await waitFor(() => {
        expect(screen.getByText(/添加白头鹎对应目科属/)).toBeInTheDocument();
      });

      rerender(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} categoryId="1" />
        </TestWrapper>
      );

      // 等待最终状态稳定
      await waitFor(() => {
        expect(screen.getByText(/添加喜鹊对应目科属/)).toBeInTheDocument();
      });

      // 验证API调用次数
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledTimes(3);
    });
  });

  describe('边界情况处理', () => {
    it('应该处理网络错误', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockRejectedValue(new Error('网络错误'));

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待错误处理完成
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('获取物种信息失败:', expect.any(Error));
      });

      // 验证组件不显示
      expect(screen.queryByTestId('species-tag-suggestion')).not.toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('应该处理空的物种数据', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(null);

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待API调用完成
      await waitFor(() => {
        expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalled();
      });

      // 验证组件不显示
      expect(screen.queryByTestId('species-tag-suggestion')).not.toBeInTheDocument();
    });

    it('应该处理部分缺失的分类学信息', async () => {
      const incompleteSpeciesData: SpeciesDictionaryEntry = {
        目: '',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(incompleteSpeciesData);

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待组件渲染
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      // 点击添加按钮
      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);

      // 验证只添加了有效的分类学信息
      const tagsInput = screen.getByLabelText('标签（可选，逗号分隔）');
      expect(tagsInput).toHaveValue('鸦科,鹊属');
    });

    it('应该处理表单提交流程', async () => {
      const mockSpeciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };

      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);

      render(
        <TestWrapper>
          <ImageUploadForm {...defaultProps} />
        </TestWrapper>
      );

      // 等待组件加载
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      // 添加物种标签
      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);

      // 创建模拟文件
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const fileInput = screen.getByLabelText('图片文件 *');
      fireEvent.change(fileInput, { target: { files: [file] } });

      // 等待文件上传完成
      await waitFor(() => {
        expect(screen.getByText('Selected: test.jpg')).toBeInTheDocument();
      });

      // 提交表单
      const submitButton = screen.getByText('Upload Image');
      fireEvent.submit(submitButton.closest('form')!);

      // 验证提交数据包含物种标签
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          file,
          category_id: '1',
          title: null,
          description: null,
          tags: '雀形目,鸦科,鹊属',
          set_as_category_thumbnail: false,
        });
      });
    });
  });
});