import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Components
import Layout from '../../components/Layout';
import AnalyticsPage from '../../components/AnalyticsPage';

// Test Providers and Contexts
const createMockThemeContext = () => ({
  theme: {
    name: 'Modern Clean Pro',
    bg: 'bg-white',
    text: 'text-gray-900',
    brandColor: 'text-blue-600 dark:text-blue-400',
    headerBg: 'bg-white',
    headerText: 'text-gray-900',
    navLink: 'text-gray-600 hover:text-gray-900',
    navLinkActive: 'text-blue-600 bg-blue-50',
    footerText: 'text-gray-600',
    footerHeartColor: 'text-red-500',
    iconButton: 'text-gray-500 hover:text-gray-700',
    card: {
      bg: 'bg-white',
      text: 'text-gray-900',
      secondaryText: 'text-gray-500',
      rounded: 'rounded-lg',
      border: 'border border-gray-200',
      shadow: 'shadow-md',
    },
    button: {
      primary: 'bg-blue-600 hover:bg-blue-700',
      primaryText: 'text-white',
      secondary: 'bg-gray-200 hover:bg-gray-300',
      secondaryText: 'text-gray-700',
      transition: 'transition-all duration-200',
    },
    input: {
      border: 'border-gray-300',
      focusRing: 'focus:ring-2 focus:ring-blue-500',
    },
    dropdown: {
      bg: 'bg-white',
      itemText: 'text-gray-900',
      itemHoverText: 'text-gray-900',
      itemHoverBg: 'hover:bg-gray-100',
      itemActiveText: 'text-blue-600',
      itemActiveBg: 'bg-blue-50',
    },
    modal: {
      rounded: 'rounded-lg',
      shadow: 'shadow-xl',
    },
  },
  themeName: 'modern',
  setThemeName: vi.fn(),
  isDarkMode: false,
});

const createMockAuthContext = () => ({
  isAuthenticated: true,
  user: null,
  login: vi.fn(),
  logout: vi.fn(),
  loading: false,
});

const createMockCategoryContext = () => ({
  categories: [
    { id: '1', name: '喜鹊', description: '测试分类', imageCount: 5, thumbnailPath: '/test.jpg' },
    { id: '2', name: '麻雀', description: '测试分类2', imageCount: 3, thumbnailPath: '/test2.jpg' },
  ],
  isLoading: false,
  error: null,
  fetchCategories: vi.fn(),
  searchCategories: vi.fn(),
  currentCategory: null,
  setCurrentCategory: vi.fn(),
});

// Mock dependencies
vi.mock('../../contexts/ThemeContext', () => ({
  useTheme: () => createMockThemeContext(),
  themeSettings: {
    modern: { name: 'Modern Clean Pro' },
    nature: { name: 'Nature Inspired' },
  },
}));

vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => createMockAuthContext(),
}));

vi.mock('../../contexts/CategoryContext', () => ({
  useCategories: () => createMockCategoryContext(),
}));

vi.mock('../../constants', () => ({
  API_BASE_URL: '/',
  IMAGE_BASE_URL: 'http://localhost:8000',
  MAX_CATEGORIES_TO_LOAD_IMAGES_FROM: 2000,
  isElectronEnvironment: () => true,
  getPlatform: () => 'electron',
  isPWAEnvironment: () => false,
  IS_ELECTRON: true,
  IS_PWA_ENVIRONMENT: false,
  PLATFORM: 'electron',
}));

// Mock services
vi.mock('../../services/bird-sighting-api', () => ({
  selectAndUploadBirdSightingFile: vi.fn(),
  getBirdSightingDataStatus: vi.fn(),
  getBirdSightingData: vi.fn(),
  hasBirdSightingData: vi.fn(),
}));

vi.mock('../../services/database-stats', () => ({
  getDatabaseStats: vi.fn(() => Promise.resolve({
    categoriesCount: 2,
    imagesCount: 8,
    tagsCount: 5,
  })),
}));

vi.mock('../../utils/dataLoader', () => ({
  loadDataFile: vi.fn(),
  DATA_FILES: {
    TOP_BIRDS: 'top_birds.json',
    BIRD_SIGHTINGS: 'bird_sightings.json',
  },
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock icons - comprehensive mock for all icons used in components
vi.mock('../../components/icons', () => {
  const mockIcon = ({ className, 'data-testid': testId }: { className?: string; 'data-testid'?: string }) => 
    <div data-testid={testId} className={className} />;
  
  return {
    DocumentTextIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'document-text-icon' }),
    BirdIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'bird-icon' }),
    HashtagIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'hashtag-icon' }),
    SearchIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'search-icon' }),
    PlusIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'plus-icon' }),
    EditIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'edit-icon' }),
    TrashIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'trash-icon' }),
    UploadIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'upload-icon' }),
    ChevronDownIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'chevron-down-icon' }),
    XMarkIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'x-mark-icon' }),
    EyeIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'eye-icon' }),
    TagIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'tag-icon' }),
    RefreshIcon: (props: any) => mockIcon({ ...props, 'data-testid': 'refresh-icon' }),
  };
});

// Mock router
const TestWrapper: React.FC<{ children: React.ReactNode; initialRoute?: string }> = ({ 
  children, 
  initialRoute = '/species' 
}) => (
  <MemoryRouter initialEntries={[initialRoute]}>
    {children}
  </MemoryRouter>
);

// Import mocked modules
import * as birdSightingApi from '../../services/bird-sighting-api';

describe('观鸟数据上传集成测试', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // 默认模拟：无观鸟数据状态
    vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockResolvedValue({
      hasData: false,
      suggestions: [
        '当前没有观鸟数据，点击"上传数据"按钮导入Excel文件',
        'Excel文件应包含鸟种编号、中文名、拉丁名、英文名、目、科、记录次数、记录时间等字段'
      ]
    });

    vi.mocked(birdSightingApi.getBirdSightingData).mockResolvedValue({
      success: true,
      data: []
    });
    
    // Mock console methods
    console.log = vi.fn();
    console.error = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('完整上传流程测试', () => {
    it('应该显示完整的上传流程：Layout按钮 -> 上传 -> AnalyticsPage状态更新', async () => {
      // 1. 渲染完整应用，包含Layout和AnalyticsPage
      render(
        <TestWrapper>
          <Layout>
            <AnalyticsPage />
          </Layout>
        </TestWrapper>
      );

      // 2. 验证初始状态 - Layout中应该有上传按钮
      await waitFor(() => {
        const layoutUploadButton = screen.queryByRole('button', { name: /上传观鸟数据/i });
        // 上传按钮可能在小屏幕上隐藏，或者只显示图标
        expect(layoutUploadButton || screen.queryByTestId('bird-upload-button')).toBeTruthy();
      });

      // 3. 验证AnalyticsPage显示"无数据状态"
      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
        expect(screen.getByText(/当前没有观鸟数据/)).toBeInTheDocument();
      });

      // 4. 模拟成功上传
      vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile).mockResolvedValueOnce({
        success: true,
        data: { importedCount: 5, totalCount: 5 }
      });

      // 5. 模拟上传后的数据状态
      vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockResolvedValueOnce({
        hasData: true,
        stats: {
          totalRecords: 5,
          totalSpecies: 4,
          totalObservations: 12,
          latestRecord: '2025/7/20'
        },
        suggestions: [
          '数据已加载，可以查看统计信息和时间线图表',
          '当前共有 5 条观测记录，涵盖 4 个物种'
        ]
      });

      vi.mocked(birdSightingApi.getBirdSightingData).mockResolvedValueOnce({
        success: true,
        data: [
          {
            id: '1',
            chineseName: '喜鹊',
            latinName: 'Pica pica',
            englishName: 'Eurasian Magpie',
            order: '雀形目',
            family: '鸦科',
            count: 3,
            recordDate: '2025/7/20'
          }
        ]
      });

      // 6. 找到并点击AnalyticsPage中的上传按钮
      const analyticsUploadButton = screen.getByRole('button', { name: /上传观鸟数据/i });
      await user.click(analyticsUploadButton);

      // 7. 验证上传API被调用
      await waitFor(() => {
        expect(vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile)).toHaveBeenCalledTimes(1);
      });

      // 8. 验证界面状态切换到"有数据状态"
      await waitFor(() => {
        expect(screen.getByText(/观鸟数据已加载/i)).toBeInTheDocument();
        expect(screen.getByText(/5.*观测记录/)).toBeInTheDocument();
        expect(screen.getByText(/4.*鸟类物种/)).toBeInTheDocument();
      }, { timeout: 3000 });

      // 9. 验证时间线数据被获取
      expect(vi.mocked(birdSightingApi.getBirdSightingData)).toHaveBeenCalled();
    });

    it('应该在Layout上传按钮点击后更新AnalyticsPage状态', async () => {
      // Mock Layout中的上传成功
      vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile).mockResolvedValueOnce({
        success: true,
        data: { importedCount: 3, totalCount: 3 }
      });

      render(
        <TestWrapper>
          <Layout>
            <AnalyticsPage />
          </Layout>
        </TestWrapper>
      );

      // 等待初始加载完成
      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
      });

      // 查找Layout中的上传按钮（可能只有图标）
      const layoutButtons = screen.getAllByRole('button');
      const uploadButton = layoutButtons.find(button => 
        button.getAttribute('aria-label')?.includes('上传') ||
        button.textContent?.includes('上传') ||
        button.querySelector('svg') // 可能只有图标
      );

      if (uploadButton) {
        await user.click(uploadButton);

        // 验证上传API被调用
        await waitFor(() => {
          expect(vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile)).toHaveBeenCalled();
        });
      }
    });
  });

  describe('界面状态切换验证', () => {
    it('应该正确切换"无数据"到"有数据"状态', async () => {
      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 1. 验证初始"无数据"状态
      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
        expect(screen.getByText(/支持的格式/i)).toBeInTheDocument();
      });

      // 2. 模拟上传完成，状态变为"有数据"
      vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockResolvedValueOnce({
        hasData: true,
        stats: {
          totalRecords: 8,
          totalSpecies: 6,
          totalObservations: 24,
          latestRecord: '2025/7/20'
        }
      });

      vi.mocked(birdSightingApi.getBirdSightingData).mockResolvedValueOnce({
        success: true,
        data: [
          {
            id: '1',
            chineseName: '喜鹊',
            latinName: 'Pica pica',
            englishName: 'Eurasian Magpie',
            order: '雀形目',
            family: '鸦科',
            count: 5,
            recordDate: '2025/7/20'
          }
        ]
      });

      // 3. 模拟上传成功
      vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile).mockResolvedValueOnce({
        success: true,
        data: { importedCount: 8, totalCount: 8 }
      });

      // 4. 点击上传按钮
      const uploadButton = screen.getByRole('button', { name: /上传观鸟数据/i });
      await user.click(uploadButton);

      // 5. 验证状态切换
      await waitFor(() => {
        expect(screen.getByText(/观鸟数据已加载/i)).toBeInTheDocument();
        expect(screen.getByText(/8.*观测记录/)).toBeInTheDocument();
        expect(screen.getByText(/6.*鸟类物种/)).toBeInTheDocument();
      }, { timeout: 3000 });

      // 6. 验证"无数据"状态内容消失
      expect(screen.queryByText(/尚无观鸟数据/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/支持的格式/i)).not.toBeInTheDocument();
    });

    it('应该正确显示加载状态', async () => {
      // 模拟延迟响应
      let resolveStatus: (value: any) => void;
      const statusPromise = new Promise((resolve) => {
        resolveStatus = resolve;
      });
      vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockReturnValue(statusPromise);

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证加载状态显示
      expect(screen.getByText(/检查观鸟数据状态/i)).toBeInTheDocument();

      // 解决promise
      resolveStatus!({
        hasData: false,
        suggestions: ['无数据']
      });

      // 验证加载完成
      await waitFor(() => {
        expect(screen.queryByText(/检查观鸟数据状态/i)).not.toBeInTheDocument();
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
      });
    });
  });

  describe('数据刷新机制测试', () => {
    it('应该在上传成功后自动刷新数据', async () => {
      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 等待初始加载
      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
      });

      // 重置mock调用计数
      vi.clearAllMocks();

      // 模拟上传成功
      vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile).mockResolvedValueOnce({
        success: true,
        data: { importedCount: 5, totalCount: 5 }
      });

      vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockResolvedValueOnce({
        hasData: true,
        stats: { totalRecords: 5, totalSpecies: 4, totalObservations: 12, latestRecord: '2025/7/20' }
      });

      vi.mocked(birdSightingApi.getBirdSightingData).mockResolvedValueOnce({
        success: true,
        data: [
          {
            id: '1',
            chineseName: '喜鹊',
            latinName: 'Pica pica',
            englishName: 'Eurasian Magpie',
            order: '雀形目',
            family: '鸦科',
            count: 3,
            recordDate: '2025/7/20'
          }
        ]
      });

      // 点击上传
      const uploadButton = screen.getByRole('button', { name: /上传观鸟数据/i });
      await user.click(uploadButton);

      // 验证数据刷新API被调用
      await waitFor(() => {
        expect(vi.mocked(birdSightingApi.getBirdSightingDataStatus)).toHaveBeenCalled();
      });

      await waitFor(() => {
        expect(vi.mocked(birdSightingApi.getBirdSightingData)).toHaveBeenCalled();
      }, { timeout: 3000 });
    });

    it('应该支持手动刷新功能', async () => {
      // 模拟有数据状态
      vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockResolvedValue({
        hasData: true,
        stats: { totalRecords: 3, totalSpecies: 2, totalObservations: 6, latestRecord: '2025/7/19' }
      });

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 等待加载并显示刷新按钮
      await waitFor(() => {
        expect(screen.getByText(/观鸟数据已加载/i)).toBeInTheDocument();
      });

      // 查找刷新按钮（可能在BirdDataUploadStatus组件中）
      const refreshButton = screen.queryByRole('button', { name: /刷新/i });
      
      if (refreshButton) {
        vi.clearAllMocks();

        await user.click(refreshButton);

        // 验证刷新API被调用
        await waitFor(() => {
          expect(vi.mocked(birdSightingApi.getBirdSightingDataStatus)).toHaveBeenCalled();
        });
      }
    });
  });

  describe('错误恢复流程验证', () => {
    it('应该正确处理上传失败情况', async () => {
      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
      });

      // 模拟上传失败
      vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile).mockResolvedValueOnce({
        success: false,
        error: '文件格式不正确'
      });

      const uploadButton = screen.getByRole('button', { name: /上传观鸟数据/i });
      await user.click(uploadButton);

      // 验证错误状态显示
      await waitFor(() => {
        expect(screen.getByText(/上传失败/i)).toBeInTheDocument();
        expect(screen.getByText(/文件格式不正确/i)).toBeInTheDocument();
      });

      // 验证仍然可以重试
      await waitFor(() => {
        const retryButton = screen.queryByRole('button', { name: /上传失败/i }) || 
                           screen.queryByRole('button', { name: /上传观鸟数据/i });
        expect(retryButton).toBeInTheDocument();
      });
    });

    it('应该正确处理数据获取失败情况', async () => {
      // 模拟状态检查失败
      vi.mocked(birdSightingApi.getBirdSightingDataStatus).mockRejectedValueOnce(
        new Error('网络连接失败')
      );

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证错误状态显示
      await waitFor(() => {
        expect(screen.getByText(/加载失败/i)).toBeInTheDocument();
        expect(screen.getByText(/网络连接失败/)).toBeInTheDocument();
      });

      // 验证重试按钮存在
      expect(screen.getByRole('button', { name: /重试/i })).toBeInTheDocument();
    });

    it('应该支持错误后的重试机制', async () => {
      // 第一次调用失败，第二次成功
      vi.mocked(birdSightingApi.getBirdSightingDataStatus)
        .mockRejectedValueOnce(new Error('网络错误'))
        .mockResolvedValueOnce({
          hasData: false,
          suggestions: ['重试成功']
        });

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 等待错误状态
      await waitFor(() => {
        expect(screen.getByText(/加载失败/i)).toBeInTheDocument();
      });

      // 点击重试
      const retryButton = screen.getByRole('button', { name: /重试/i });
      await user.click(retryButton);

      // 验证重试成功
      await waitFor(() => {
        expect(screen.queryByText(/加载失败/i)).not.toBeInTheDocument();
        expect(screen.getByText(/重试成功/)).toBeInTheDocument();
      });
    });
  });

  describe('用户体验验证', () => {
    it('应该提供适当的状态反馈', async () => {
      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证初始加载状态
      expect(screen.getByText(/检查观鸟数据状态/i)).toBeInTheDocument();

      // 等待加载完成
      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
      });

      // 模拟上传中状态
      let resolveUpload: (value: any) => void;
      const uploadPromise = new Promise((resolve) => {
        resolveUpload = resolve;
      });
      vi.mocked(birdSightingApi.selectAndUploadBirdSightingFile).mockReturnValue(uploadPromise);

      const uploadButton = screen.getByRole('button', { name: /上传观鸟数据/i });
      await user.click(uploadButton);

      // 验证上传中状态
      await waitFor(() => {
        expect(screen.getByText(/上传中/i)).toBeInTheDocument();
      });

      // 完成上传
      resolveUpload!({
        success: true,
        data: { importedCount: 3, totalCount: 3 }
      });

      // 验证成功状态
      await waitFor(() => {
        expect(screen.getByText(/上传成功/i)).toBeInTheDocument();
      });
    });

    it('应该提供清晰的使用指引', async () => {
      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/尚无观鸟数据/i)).toBeInTheDocument();
      });

      // 验证使用指引显示
      expect(screen.getByText(/点击.*上传数据.*按钮导入Excel文件/)).toBeInTheDocument();
      expect(screen.getByText(/Excel文件应包含.*字段/)).toBeInTheDocument();
      expect(screen.getByText(/支持的格式/i)).toBeInTheDocument();
      expect(screen.getByText(/\.xlsx.*\.xls/)).toBeInTheDocument();
    });
  });
});