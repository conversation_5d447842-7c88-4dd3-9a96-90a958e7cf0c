import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import path from 'path';
import fs from 'fs/promises';
import { SpeciesService } from '../../../electron/services/SpeciesService';
import type { SpeciesDictionarySearchOptions } from '../../../schemas/speciesDictionary';

// 测试数据路径
const PERFORMANCE_DATA_PATH = path.join(__dirname, '../../fixtures/performance-species.json');

// 生成大量测试数据（模拟真实的11194条记录）
function generateLargeSpeciesData(count: number = 1000) {
  const orders = ['雀形目', '鹰形目', '鸽形目', '鹤形目', '鸻形目', '鸊鷉目', '鹈形目', '鸬鹚目'];
  const families = ['鸦科', '麻雀科', '鹰科', '隼科', '鸠鸽科', '鹤科', '鸻科', '鸊鷉科'];
  const genera = ['鸦属', '麻雀属', '鹰属', '隼属', '鸽属', '鹤属', '鸻属', '鸊鷉属'];
  
  const species = [];
  for (let i = 0; i < count; i++) {
    const orderIndex = i % orders.length;
    const familyIndex = i % families.length;
    const genusIndex = i % genera.length;
    
    species.push({
      "目": orders[orderIndex],
      "科": families[familyIndex],
      "属": genera[genusIndex],
      "种": `测试物种${i + 1}`
    });
  }
  
  // 添加一些真实的测试数据
  species.push(
    { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "白头鹎" },
    { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "黑头鹎" },
    { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "家麻雀" },
    { "目": "鹰形目", "科": "鹰科", "属": "鹰属", "种": "苍鹰" },
    { "目": "鸽形目", "科": "鸠鸽科", "属": "鸽属", "种": "岩鸽" }
  );
  
  return species;
}

describe('Species Performance Tests', () => {
  let speciesService: SpeciesService;
  const performanceResults: any = {};

  beforeAll(async () => {
    // 生成大量测试数据
    const largeDataset = generateLargeSpeciesData(1000);
    await fs.writeFile(PERFORMANCE_DATA_PATH, JSON.stringify(largeDataset, null, 2));
    
    console.log(`📊 性能测试数据生成完成: ${largeDataset.length} 条记录`);
  });

  afterAll(async () => {
    // 清理测试数据
    try {
      await fs.unlink(PERFORMANCE_DATA_PATH);
    } catch {
      // 忽略文件不存在的错误
    }
    
    // 输出性能测试报告
    console.log('\n📈 性能测试报告:');
    console.log('================');
    Object.entries(performanceResults).forEach(([test, result]: [string, any]) => {
      console.log(`${test}: ${result}`);
    });
  });

  describe('数据加载性能', () => {
    it('首次加载时间应该在1000ms以内', async () => {
      const startTime = Date.now();
      
      speciesService = new SpeciesService(PERFORMANCE_DATA_PATH);
      await speciesService.ensureLoaded();
      
      const loadTime = Date.now() - startTime;
      performanceResults['首次加载时间'] = `${loadTime}ms`;
      
      console.log(`⏱️ 首次加载时间: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(1000);
      
      // 验证数据正确加载
      const stats = speciesService.getStats();
      expect(stats.totalSpecies).toBeGreaterThan(1000);
      expect(stats.loaded).toBe(true);
    });

    it('索引构建时间应该在合理范围内', async () => {
      const stats = speciesService.getStats();
      const indexBuildTime = stats.indexBuildTime;
      
      performanceResults['索引构建时间'] = `${indexBuildTime}ms`;
      
      console.log(`🔧 索引构建时间: ${indexBuildTime}ms`);
      expect(indexBuildTime).toBeLessThan(500); // 500ms以内
    });
  });

  describe('查询性能', () => {
    beforeAll(async () => {
      if (!speciesService) {
        speciesService = new SpeciesService(PERFORMANCE_DATA_PATH);
        await speciesService.ensureLoaded();
      }
    });

    it('单次查询响应时间应该在50ms以内', async () => {
      const queries = ['白头', '麻雀', '鹰', '测试', '物种'];
      const times: number[] = [];

      for (const query of queries) {
        const startTime = Date.now();
        const result = await speciesService.searchSpecies({ query, limit: 20 });
        const queryTime = Date.now() - startTime;
        
        times.push(queryTime);
        expect(result.searchTime).toBeLessThan(50);
        expect(queryTime).toBeLessThan(50);
      }

      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      performanceResults['平均查询时间'] = `${avgTime.toFixed(2)}ms`;
      
      console.log(`🔍 平均查询时间: ${avgTime.toFixed(2)}ms`);
      expect(avgTime).toBeLessThan(25); // 平均25ms以内
    });

    it('复杂查询性能测试', async () => {
      const complexQueries = [
        { query: '测试物种1', limit: 1 },     // 精确匹配
        { query: '测试', limit: 100 },        // 前缀匹配
        { query: 'ceshi', limit: 50 },        // 拼音匹配
        { query: 'cs', limit: 30 },           // 首字母匹配
        { query: '物', limit: 200 },          // N-gram匹配
      ];

      const times: number[] = [];
      
      for (const options of complexQueries) {
        const startTime = Date.now();
        const result = await speciesService.searchSpecies(options);
        const queryTime = Date.now() - startTime;
        
        times.push(queryTime);
        expect(queryTime).toBeLessThan(100); // 复杂查询100ms以内
        
        console.log(`🔎 查询 "${options.query}" (limit: ${options.limit}): ${queryTime}ms, 结果: ${result.species.length}条`);
      }

      const maxTime = Math.max(...times);
      performanceResults['最大查询时间'] = `${maxTime}ms`;
      
      expect(maxTime).toBeLessThan(100);
    });

    it('按科属查询性能测试', async () => {
      const startTime1 = Date.now();
      const familyResult = await speciesService.getSpeciesByFamily('鸦科', { limit: 100 });
      const familyTime = Date.now() - startTime1;

      const startTime2 = Date.now();
      const genusResult = await speciesService.getSpeciesByGenus('鸦属', { limit: 100 });
      const genusTime = Date.now() - startTime2;

      performanceResults['按科查询时间'] = `${familyTime}ms`;
      performanceResults['按属查询时间'] = `${genusTime}ms`;

      console.log(`🏷️ 按科查询时间: ${familyTime}ms, 结果: ${familyResult.length}条`);
      console.log(`🏷️ 按属查询时间: ${genusTime}ms, 结果: ${genusResult.length}条`);

      expect(familyTime).toBeLessThan(20);
      expect(genusTime).toBeLessThan(20);
    });
  });

  describe('并发性能', () => {
    beforeAll(async () => {
      if (!speciesService) {
        speciesService = new SpeciesService(PERFORMANCE_DATA_PATH);
        await speciesService.ensureLoaded();
      }
    });

    it('应该支持100个并发查询', async () => {
      const concurrentQueries = Array(100).fill(0).map((_, index) => ({
        query: `测试${index % 10}`,
        limit: 10
      }));

      const startTime = Date.now();
      const promises = concurrentQueries.map(options => 
        speciesService.searchSpecies(options)
      );

      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;

      performanceResults['100并发查询总时间'] = `${totalTime}ms`;
      performanceResults['并发查询平均时间'] = `${(totalTime / 100).toFixed(2)}ms`;

      console.log(`⚡ 100个并发查询总时间: ${totalTime}ms`);
      console.log(`⚡ 平均每个查询: ${(totalTime / 100).toFixed(2)}ms`);

      // 验证所有查询都成功
      expect(results).toHaveLength(100);
      results.forEach(result => {
        expect(result.species).toBeDefined();
        expect(result.searchTime).toBeLessThan(100);
      });

      // 总时间应该远小于串行执行时间
      expect(totalTime).toBeLessThan(5000); // 5秒以内
    });

    it('混合类型并发查询性能', async () => {
      const mixedQueries = [
        // 搜索查询
        ...Array(30).fill(0).map((_, i) => ({ type: 'search', query: `测试${i}`, limit: 10 })),
        // 详情查询
        ...Array(20).fill(0).map((_, i) => ({ type: 'info', speciesName: `测试物种${i + 1}` })),
        // 科属查询
        ...Array(10).fill(0).map((_, i) => ({ type: 'family', familyName: '鸦科', limit: 20 })),
      ];

      const startTime = Date.now();
      const promises = mixedQueries.map(async (query) => {
        switch (query.type) {
          case 'search':
            return speciesService.searchSpecies({ query: query.query!, limit: query.limit! });
          case 'info':
            return speciesService.getSpeciesInfo(query.speciesName!);
          case 'family':
            return speciesService.getSpeciesByFamily(query.familyName!, { limit: query.limit! });
          default:
            throw new Error('Unknown query type');
        }
      });

      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;

      performanceResults['混合并发查询时间'] = `${totalTime}ms`;

      console.log(`🔀 60个混合并发查询总时间: ${totalTime}ms`);

      expect(results).toHaveLength(60);
      expect(totalTime).toBeLessThan(3000); // 3秒以内
    });
  });

  describe('内存使用', () => {
    it('内存占用应该在合理范围内', async () => {
      if (!speciesService) {
        speciesService = new SpeciesService(PERFORMANCE_DATA_PATH);
        await speciesService.ensureLoaded();
      }

      const stats = speciesService.getStats();
      
      // 估算内存使用（简单估算）
      const estimatedMemory = (
        stats.memoryUsage.exactIndex * 50 +      // 精确索引
        stats.memoryUsage.prefixIndex * 30 +     // 前缀索引
        stats.memoryUsage.pinyinIndex * 20 +     // 拼音索引
        stats.memoryUsage.ngramIndex * 25 +      // N-gram索引
        stats.totalSpecies * 100                 // 原始数据
      ) / 1024; // 转换为KB

      performanceResults['估算内存使用'] = `${estimatedMemory.toFixed(2)}KB`;

      console.log(`💾 估算内存使用: ${estimatedMemory.toFixed(2)}KB`);
      console.log(`📊 索引统计:`, stats.memoryUsage);

      // 对于1000+条记录，内存使用应该在10MB以内
      expect(estimatedMemory).toBeLessThan(10 * 1024); // 10MB
    });
  });

  describe('缓存性能', () => {
    it('重复查询应该有缓存效果', async () => {
      if (!speciesService) {
        speciesService = new SpeciesService(PERFORMANCE_DATA_PATH);
        await speciesService.ensureLoaded();
      }

      const query = { query: '白头鹎', limit: 10 };

      // 第一次查询（冷启动）
      const startTime1 = Date.now();
      const result1 = await speciesService.searchSpecies(query);
      const time1 = Date.now() - startTime1;

      // 第二次查询（应该更快，因为索引已建立）
      const startTime2 = Date.now();
      const result2 = await speciesService.searchSpecies(query);
      const time2 = Date.now() - startTime2;

      performanceResults['首次查询时间'] = `${time1}ms`;
      performanceResults['重复查询时间'] = `${time2}ms`;

      console.log(`🔄 首次查询: ${time1}ms, 重复查询: ${time2}ms`);

      // 验证结果一致
      expect(result1.species).toEqual(result2.species);
      
      // 重复查询应该更快（或至少不慢太多）
      expect(time2).toBeLessThanOrEqual(time1 + 5); // 允许5ms误差
    });
  });
});
