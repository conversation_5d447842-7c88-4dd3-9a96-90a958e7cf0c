import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { ipcMain } from 'electron';
import path from 'path';
import fs from 'fs/promises';
import { SpeciesService } from '../../../electron/services/SpeciesService';
import type { SpeciesDictionarySearchOptions } from '../../../schemas/speciesDictionary';

// 模拟Electron的ipcMain
vi.mock('electron', () => ({
  ipcMain: {
    handle: vi.fn(),
    removeHandler: vi.fn(),
  },
}));

// 测试数据路径
const TEST_DATA_PATH = path.join(__dirname, '../../fixtures/test-species-ipc.json');

// 测试用物种数据
const mockSpeciesData = [
  { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "白头鹎" },
  { "目": "雀形目", "科": "鸦科", "属": "鸦属", "种": "黑头鹎" },
  { "目": "雀形目", "科": "麻雀科", "属": "麻雀属", "种": "家麻雀" },
  { "目": "鹰形目", "科": "鹰科", "属": "鹰属", "种": "苍鹰" },
];

describe('Species IPC Handlers', () => {
  let speciesService: SpeciesService;
  let ipcHandlers: Map<string, Function>;

  beforeEach(async () => {
    // 重置模拟
    vi.clearAllMocks();
    
    // 创建测试数据文件
    await fs.writeFile(TEST_DATA_PATH, JSON.stringify(mockSpeciesData, null, 2));
    
    // 创建服务实例
    speciesService = new SpeciesService(TEST_DATA_PATH);
    await speciesService.ensureLoaded();
    
    // 存储IPC处理器
    ipcHandlers = new Map();
    
    // 模拟ipcMain.handle的实现
    (ipcMain.handle as any).mockImplementation((channel: string, handler: Function) => {
      ipcHandlers.set(channel, handler);
    });
    
    // 注册物种查询IPC处理器（模拟main.ts中的注册逻辑）
    registerSpeciesIpcHandlers(speciesService);
  });

  afterEach(async () => {
    // 清理测试数据
    try {
      await fs.unlink(TEST_DATA_PATH);
    } catch {
      // 忽略文件不存在的错误
    }
    
    // 清理IPC处理器
    ipcHandlers.clear();
  });

  describe('search-species IPC处理器', () => {
    it('应该能够处理有效的搜索请求', async () => {
      const handler = ipcHandlers.get('search-species');
      expect(handler).toBeDefined();

      const mockEvent = {};
      const searchOptions: SpeciesDictionarySearchOptions = {
        query: '白头鹎',
        limit: 10
      };

      const result = await handler(mockEvent, searchOptions);

      expect(result.success).toBe(true);
      expect(result.data.species).toHaveLength(1);
      expect(result.data.species[0].种).toBe('白头鹎');
      expect(result.data.searchTime).toBeLessThan(100);
    });

    it('应该能够处理模糊搜索', async () => {
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      const searchOptions: SpeciesDictionarySearchOptions = {
        query: '鹎',
        limit: 10
      };

      const result = await handler(mockEvent, searchOptions);

      expect(result.success).toBe(true);
      expect(result.data.species.length).toBeGreaterThan(1);
      expect(result.data.species.some((s: any) => s.种.includes('鹎'))).toBe(true);
    });

    it('应该能够处理拼音搜索', async () => {
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      const searchOptions: SpeciesDictionarySearchOptions = {
        query: 'baitou',
        limit: 10
      };

      const result = await handler(mockEvent, searchOptions);

      expect(result.success).toBe(true);
      expect(result.data.species.length).toBeGreaterThan(0);
    });

    it('应该处理无效参数并返回错误', async () => {
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      const invalidOptions = {
        query: '', // 空查询字符串
        limit: 10
      };

      const result = await handler(mockEvent, invalidOptions);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(typeof result.error).toBe('string');
    });

    it('应该限制查询结果数量', async () => {
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      const searchOptions: SpeciesDictionarySearchOptions = {
        query: '目', // 应该匹配多个结果
        limit: 2
      };

      const result = await handler(mockEvent, searchOptions);

      expect(result.success).toBe(true);
      expect(result.data.species.length).toBeLessThanOrEqual(2);
    });
  });

  describe('get-species-info IPC处理器', () => {
    it('应该能够获取存在的物种信息', async () => {
      const handler = ipcHandlers.get('get-species-info');
      expect(handler).toBeDefined();

      const mockEvent = {};
      const speciesName = '白头鹎';

      const result = await handler(mockEvent, speciesName);

      expect(result.success).toBe(true);
      expect(result.data).not.toBeNull();
      expect(result.data.种).toBe('白头鹎');
      expect(result.data.科).toBe('鸦科');
    });

    it('应该处理不存在的物种', async () => {
      const handler = ipcHandlers.get('get-species-info');
      const mockEvent = {};
      const speciesName = '不存在的鸟';

      const result = await handler(mockEvent, speciesName);

      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
    });

    it('应该处理无效参数', async () => {
      const handler = ipcHandlers.get('get-species-info');
      const mockEvent = {};
      const invalidName = '';

      const result = await handler(mockEvent, invalidName);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('get-species-by-family IPC处理器', () => {
    it('应该能够按科查询物种', async () => {
      const handler = ipcHandlers.get('get-species-by-family');
      expect(handler).toBeDefined();

      const mockEvent = {};
      const familyName = '鸦科';
      const options = { limit: 10 };

      const result = await handler(mockEvent, familyName, options);

      expect(result.success).toBe(true);
      expect(result.data.length).toBe(2); // 白头鹎、黑头鹎
      expect(result.data.every((s: any) => s.科 === '鸦科')).toBe(true);
    });

    it('应该处理不存在的科', async () => {
      const handler = ipcHandlers.get('get-species-by-family');
      const mockEvent = {};
      const familyName = '不存在科';
      const options = { limit: 10 };

      const result = await handler(mockEvent, familyName, options);

      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
    });
  });

  describe('get-species-by-genus IPC处理器', () => {
    it('应该能够按属查询物种', async () => {
      const handler = ipcHandlers.get('get-species-by-genus');
      expect(handler).toBeDefined();

      const mockEvent = {};
      const genusName = '鸦属';
      const options = { limit: 10 };

      const result = await handler(mockEvent, genusName, options);

      expect(result.success).toBe(true);
      expect(result.data.length).toBe(2); // 白头鹎、黑头鹎
      expect(result.data.every((s: any) => s.属 === '鸦属')).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该处理服务异常', async () => {
      // 创建一个会抛出异常的服务实例
      const faultyService = new SpeciesService('/invalid/path.json');
      
      // 重新注册处理器
      registerSpeciesIpcHandlers(faultyService);
      
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      const searchOptions: SpeciesDictionarySearchOptions = {
        query: '测试',
        limit: 10
      };

      const result = await handler(mockEvent, searchOptions);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(typeof result.error).toBe('string');
    });
  });

  describe('性能测试', () => {
    it('IPC调用响应时间应该在合理范围内', async () => {
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      const searchOptions: SpeciesDictionarySearchOptions = {
        query: '白头',
        limit: 10
      };

      const startTime = Date.now();
      const result = await handler(mockEvent, searchOptions);
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(100); // 100ms以内
    });

    it('应该支持并发IPC调用', async () => {
      const handler = ipcHandlers.get('search-species');
      const mockEvent = {};
      
      const promises = Array(5).fill(0).map((_, index) => 
        handler(mockEvent, { query: `测试${index}`, limit: 5 })
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });
});

/**
 * 注册物种查询IPC处理器的函数
 * 这个函数模拟了main.ts中的实际注册逻辑
 */
function registerSpeciesIpcHandlers(speciesService: SpeciesService) {
  // search-species IPC处理器
  ipcMain.handle('search-species', async (event, options: SpeciesDictionarySearchOptions) => {
    try {
      console.log('🔍 IPC: 收到物种搜索请求', options);
      
      const result = await speciesService.searchSpecies(options);
      
      console.log(`✅ IPC: 物种搜索完成，找到${result.species.length}条结果`);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ IPC: 物种搜索失败', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // get-species-info IPC处理器
  ipcMain.handle('get-species-info', async (event, speciesName: string) => {
    try {
      if (!speciesName || typeof speciesName !== 'string' || speciesName.trim() === '') {
        throw new Error('物种名称不能为空');
      }

      console.log('📋 IPC: 收到获取物种信息请求', speciesName);
      
      const result = await speciesService.getSpeciesInfo(speciesName.trim());
      
      console.log(`✅ IPC: 获取物种信息完成`, result ? '找到' : '未找到');
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ IPC: 获取物种信息失败', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // get-species-by-family IPC处理器
  ipcMain.handle('get-species-by-family', async (event, familyName: string, options?: any) => {
    try {
      if (!familyName || typeof familyName !== 'string' || familyName.trim() === '') {
        throw new Error('科名不能为空');
      }

      console.log('🏷️ IPC: 收到按科查询物种请求', familyName);
      
      const result = await speciesService.getSpeciesByFamily(familyName.trim(), options);
      
      console.log(`✅ IPC: 按科查询完成，找到${result.length}条结果`);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ IPC: 按科查询失败', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // get-species-by-genus IPC处理器
  ipcMain.handle('get-species-by-genus', async (event, genusName: string, options?: any) => {
    try {
      if (!genusName || typeof genusName !== 'string' || genusName.trim() === '') {
        throw new Error('属名不能为空');
      }

      console.log('🏷️ IPC: 收到按属查询物种请求', genusName);
      
      const result = await speciesService.getSpeciesByGenus(genusName.trim(), options);
      
      console.log(`✅ IPC: 按属查询完成，找到${result.length}条结果`);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ IPC: 按属查询失败', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });
}
