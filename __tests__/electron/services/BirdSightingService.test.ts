import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';
import fs from 'fs';
import path from 'path';
import * as XLSX from 'xlsx';

// Mock electron模块 - 必须在导入前
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

// Mock fs模块
vi.mock('fs');
const mockFs = vi.mocked(fs);

// Mock xlsx模块
vi.mock('xlsx', () => ({
  readFile: vi.fn(),
  utils: {
    sheet_to_json: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { BirdSightingService } from '../../../electron/services/BirdSightingService';
import * as XLSX from 'xlsx';

const mockXLSX = vi.mocked(XLSX);

// 测试数据类型定义
interface ExcelBirdData {
  鸟种编号: string;
  中文名: string;
  拉丁名: string;
  英文名: string;
  目: string;
  科: string;
  定点记报告: string;
  随手记报告: string;
  记录次数: string;
  记录时间: string;
}

interface BirdSightingRecord {
  id: string;
  chineseName: string;
  latinName: string;
  englishName: string;
  order: string;
  family: string;
  count: number;
  recordDate: string;
}

describe('BirdSightingService', () => {
  let dbManager: DatabaseManager;
  let birdSightingService: BirdSightingService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 使用测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建模拟的DatabaseManager
    dbManager = {
      getDatabase: () => testDb,
      getDatabasePath: vi.fn(() => '/mock/test/database.db'),
      testConnection: vi.fn(),
      getStats: vi.fn(),
      close: vi.fn()
    } as any;

    birdSightingService = new BirdSightingService(dbManager);

    // 静默控制台输出
    console.log = vi.fn();
    console.error = vi.fn();
    console.warn = vi.fn();

    // 重置所有mock
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  describe('Excel文件解析', () => {
    it('应该成功解析有效的Excel文件', async () => {
      const mockExcelData: ExcelBirdData[] = [
        {
          鸟种编号: '4004',
          中文名: '白眉山鹧鸪',
          拉丁名: 'Arborophila gingica',
          英文名: "Rickett's Hill Partridge",
          目: '鸡形目',
          科: '雉科',
          定点记报告: '3',
          随手记报告: '2',
          记录次数: '5',
          记录时间: '2025-03-22 10:00'
        },
        {
          鸟种编号: '4040',
          中文名: '环颈雉',
          拉丁名: 'Phasianus colchicus',
          英文名: 'Common Pheasant',
          目: '鸡形目',
          科: '雉科',
          定点记报告: '2',
          随手记报告: '3',
          记录次数: '5',
          记录时间: '2025-02-09 12:30'
        }
      ];

      const mockWorkbook = {
        SheetNames: ['鸟种数据导出'],
        Sheets: {
          '鸟种数据导出': {}
        }
      };

      mockFs.existsSync.mockReturnValue(true);
      mockXLSX.readFile.mockReturnValue(mockWorkbook);
      mockXLSX.utils.sheet_to_json.mockReturnValue(mockExcelData);

      const result = await birdSightingService.parseExcelFile('/test/path/data.xlsx');

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0]).toEqual({
        id: '4004',
        chineseName: '白眉山鹧鸪',
        latinName: 'Arborophila gingica',
        englishName: "Rickett's Hill Partridge",
        order: '鸡形目',
        family: '雉科',
        count: 5,
        recordDate: '2025/3/22'
      });
    });

    it('应该处理文件不存在的情况', async () => {
      mockFs.existsSync.mockReturnValue(false);

      const result = await birdSightingService.parseExcelFile('/nonexistent/file.xlsx');

      expect(result.success).toBe(false);
      expect(result.error).toContain('文件不存在');
    });

    it('应该处理无效的Excel文件格式', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockXLSX.readFile.mockImplementation(() => {
        throw new Error('无效的Excel文件格式');
      });

      const result = await birdSightingService.parseExcelFile('/test/invalid.xlsx');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Excel文件解析失败');
    });

    it('应该处理缺少必要工作表的情况', async () => {
      const mockWorkbook = {
        SheetNames: ['其他工作表'],
        Sheets: {
          '其他工作表': {}
        }
      };

      mockFs.existsSync.mockReturnValue(true);
      mockXLSX.readFile.mockReturnValue(mockWorkbook);

      const result = await birdSightingService.parseExcelFile('/test/invalid-sheet.xlsx');

      expect(result.success).toBe(false);
      expect(result.error).toContain('未找到鸟种数据导出工作表');
    });

    it('应该处理空数据的情况', async () => {
      const mockWorkbook = {
        SheetNames: ['鸟种数据导出'],
        Sheets: {
          '鸟种数据导出': {}
        }
      };

      mockFs.existsSync.mockReturnValue(true);
      mockXLSX.readFile.mockReturnValue(mockWorkbook);
      mockXLSX.utils.sheet_to_json.mockReturnValue([]);

      const result = await birdSightingService.parseExcelFile('/test/empty.xlsx');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Excel文件中没有数据');
    });
  });

  describe('数据格式转换', () => {
    it('应该正确转换Excel数据为时间线格式', () => {
      const excelData: ExcelBirdData = {
        鸟种编号: '4004',
        中文名: '白眉山鹧鸪',
        拉丁名: 'Arborophila gingica',
        英文名: "Rickett's Hill Partridge",
        目: '鸡形目',
        科: '雉科',
        定点记报告: '3',
        随手记报告: '2',
        记录次数: '5',
        记录时间: '2025-03-22 10:00'
      };

      const result = birdSightingService.convertExcelDataToTimelineFormat(excelData);

      expect(result).toEqual({
        id: '4004',
        chineseName: '白眉山鹧鸪',
        latinName: 'Arborophila gingica',
        englishName: "Rickett's Hill Partridge",
        order: '鸡形目',
        family: '雉科',
        count: 5,
        recordDate: '2025/3/22'
      });
    });

    it('应该处理时间格式转换', () => {
      const testCases = [
        { input: '2025-03-22 10:00', expected: '2025/3/22' },
        { input: '2025-01-01', expected: '2025/1/1' },
        { input: '2025/12/31', expected: '2025/12/31' },
        { input: '2025-12-31 23:59:59', expected: '2025/12/31' }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = birdSightingService.formatRecordDate(input);
        expect(result).toBe(expected);
      });
    });

    it('应该处理无效的数字格式', () => {
      const excelData: ExcelBirdData = {
        鸟种编号: '4004',
        中文名: '白眉山鹧鸪',
        拉丁名: 'Arborophila gingica',
        英文名: "Rickett's Hill Partridge",
        目: '鸡形目',
        科: '雉科',
        定点记报告: 'invalid',
        随手记报告: 'invalid',
        记录次数: 'invalid',
        记录时间: '2025-03-22 10:00'
      };

      const result = birdSightingService.convertExcelDataToTimelineFormat(excelData);

      expect(result.count).toBe(0); // 默认值
    });
  });

  describe('数据验证', () => {
    it('应该验证必要字段的存在', () => {
      const validData: BirdSightingRecord = {
        id: '4004',
        chineseName: '白眉山鹧鸪',
        latinName: 'Arborophila gingica',
        englishName: "Rickett's Hill Partridge",
        order: '鸡形目',
        family: '雉科',
        count: 5,
        recordDate: '2025/3/22'
      };

      const result = birdSightingService.validateBirdSightingRecord(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测缺少的必要字段', () => {
      const invalidData = {
        id: '4004',
        // 缺少其他必要字段
      } as BirdSightingRecord;

      const result = birdSightingService.validateBirdSightingRecord(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('应该验证数据类型', () => {
      const invalidData: BirdSightingRecord = {
        id: '4004',
        chineseName: '白眉山鹧鸪',
        latinName: 'Arborophila gingica',
        englishName: "Rickett's Hill Partridge",
        order: '鸡形目',
        family: '雉科',
        count: -1, // 无效的计数
        recordDate: '2025/3/22'
      };

      const result = birdSightingService.validateBirdSightingRecord(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('记录次数'))).toBe(true);
    });
  });

  describe('数据库操作', () => {
    it('应该成功插入观鸟数据到数据库', async () => {
      const sightingData: BirdSightingRecord = {
        id: '4004',
        chineseName: '白眉山鹧鸪',
        latinName: 'Arborophila gingica',
        englishName: "Rickett's Hill Partridge",
        order: '鸡形目',
        family: '雉科',
        count: 5,
        recordDate: '2025/3/22'
      };

      const result = await birdSightingService.insertBirdSightingRecord(sightingData);

      expect(result.success).toBe(true);

      // 验证数据已插入
      const insertedRecord = testDb.prepare('SELECT * FROM bird_sightings WHERE id = ?').get('4004');
      expect(insertedRecord).toBeDefined();
      expect(insertedRecord.chinese_name).toBe('白眉山鹧鸪');
    });

    it('应该成功获取所有观鸟数据', async () => {
      // 先插入测试数据
      const insertStmt = testDb.prepare(`
        INSERT INTO bird_sightings (id, chinese_name, latin_name, english_name, bird_order, family, count, record_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run('4004', '白眉山鹧鸪', 'Arborophila gingica', "Rickett's Hill Partridge", '鸡形目', '雉科', 5, '2025/3/22');
      insertStmt.run('4040', '环颈雉', 'Phasianus colchicus', 'Common Pheasant', '鸡形目', '雉科', 5, '2025/2/9');

      const result = await birdSightingService.getAllBirdSightingRecords();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0]).toHaveProperty('chineseName');
    });

    it('应该成功清空并重新插入数据（覆盖模式）', async () => {
      // 先插入一些旧数据
      const insertStmt = testDb.prepare(`
        INSERT INTO bird_sightings (id, chinese_name, latin_name, english_name, bird_order, family, count, record_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertStmt.run('old1', '旧数据1', 'Old data 1', 'Old Data 1', '目1', '科1', 1, '2024/1/1');

      // 新数据
      const newData: BirdSightingRecord[] = [
        {
          id: '4004',
          chineseName: '白眉山鹧鸪',
          latinName: 'Arborophila gingica',
          englishName: "Rickett's Hill Partridge",
          order: '鸡形目',
          family: '雉科',
          count: 5,
          recordDate: '2025/3/22'
        }
      ];

      const result = await birdSightingService.replaceAllBirdSightingRecords(newData);

      expect(result.success).toBe(true);

      // 验证旧数据已删除，新数据已插入
      const allRecords = testDb.prepare('SELECT * FROM bird_sightings').all();
      expect(allRecords).toHaveLength(1);
      expect(allRecords[0].id).toBe('4004');
    });

    it('应该获取观鸟数据统计信息', async () => {
      // 插入测试数据
      const insertStmt = testDb.prepare(`
        INSERT INTO bird_sightings (id, chinese_name, latin_name, english_name, bird_order, family, count, record_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertStmt.run('4004', '白眉山鹧鸪', 'Arborophila gingica', "Rickett's Hill Partridge", '鸡形目', '雉科', 5, '2025/3/22');
      insertStmt.run('4040', '环颈雉', 'Phasianus colchicus', 'Common Pheasant', '鸡形目', '雉科', 3, '2025/2/9');

      const result = await birdSightingService.getBirdSightingStats();

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        totalRecords: 2,
        totalSpecies: 2,
        totalObservations: 8,
        latestRecord: '2025/3/22'
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理数据库连接错误', async () => {
      // 模拟数据库错误
      const errorDb = {
        prepare: vi.fn(() => {
          throw new Error('Database connection failed');
        })
      };

      const errorDbManager = {
        getDatabase: () => errorDb
      } as any;

      const errorService = new BirdSightingService(errorDbManager);

      const result = await errorService.getAllBirdSightingRecords();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection failed');
    });

    it('应该处理批量插入时的部分失败', async () => {
      const mixedData: BirdSightingRecord[] = [
        {
          id: '4004',
          chineseName: '白眉山鹧鸪',
          latinName: 'Arborophila gingica',
          englishName: "Rickett's Hill Partridge",
          order: '鸡形目',
          family: '雉科',
          count: 5,
          recordDate: '2025/3/22'
        },
        {
          id: '4004', // 重复的ID，会导致冲突
          chineseName: '重复数据',
          latinName: 'Duplicate data',
          englishName: 'Duplicate Data',
          order: '目',
          family: '科',
          count: 1,
          recordDate: '2025/1/1'
        }
      ];

      const result = await birdSightingService.replaceAllBirdSightingRecords(mixedData);

      expect(result.success).toBe(false);
      expect(result.error).toContain('数据插入失败');
    });
  });

  describe('端到端流程', () => {
    it('应该完成完整的Excel上传到数据库流程', async () => {
      const mockExcelData: ExcelBirdData[] = [
        {
          鸟种编号: '4004',
          中文名: '白眉山鹧鸪',
          拉丁名: 'Arborophila gingica',
          英文名: "Rickett's Hill Partridge",
          目: '鸡形目',
          科: '雉科',
          定点记报告: '3',
          随手记报告: '2',
          记录次数: '5',
          记录时间: '2025-03-22 10:00'
        }
      ];

      const mockWorkbook = {
        SheetNames: ['鸟种数据导出'],
        Sheets: {
          '鸟种数据导出': {}
        }
      };

      mockFs.existsSync.mockReturnValue(true);
      mockXLSX.readFile.mockReturnValue(mockWorkbook);
      mockXLSX.utils.sheet_to_json.mockReturnValue(mockExcelData);

      const result = await birdSightingService.uploadExcelFile('/test/data.xlsx');

      expect(result.success).toBe(true);
      expect(result.data?.importedCount).toBe(1);

      // 验证数据已正确插入数据库
      const dbRecord = testDb.prepare('SELECT * FROM bird_sightings WHERE id = ?').get('4004');
      expect(dbRecord).toBeDefined();
      expect(dbRecord.chinese_name).toBe('白眉山鹧鸪');
    });
  });
});