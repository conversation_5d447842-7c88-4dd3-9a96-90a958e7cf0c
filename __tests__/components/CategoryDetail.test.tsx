import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import CategoryDetail from '../../components/CategoryDetail';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock the API functions
vi.mock('../../services/api', () => ({
  getCategoryWithImages: vi.fn(),
  updateCategory: vi.fn(),
  deleteCategory: vi.fn(),
  uploadImage: vi.fn(),
}));

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ categoryId: '1' }),
    useNavigate: () => vi.fn(),
    useLocation: () => ({ search: '' }),
  };
});

const mockCategory = {
  id: '1',
  name: '测试分类',
  description: '这是一个测试分类的描述',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  thumbnail_url: null,
  thumbnail_path: null,
  images: [
    {
      id: '1',
      title: '测试图片1',
      description: '测试图片描述',
      image_url: '/test1.jpg',
      thumbnail_url: '/test1_thumb.jpg',
      tags: ['测试', '图片'],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      relative_file_path: '/test1.jpg',
      relative_thumbnail_path: '/test1_thumb.jpg',
    },
  ],
};

const renderWithProviders = (component: React.ReactElement, isAuthenticated = false) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        <AuthProvider>
          {component}
        </AuthProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('CategoryDetail', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('中文文本显示', () => {
    it('显示正确的中文分类信息', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      vi.mocked(getCategoryWithImages).mockResolvedValue(mockCategory);

      renderWithProviders(<CategoryDetail />);

      // 等待数据加载
      await waitFor(() => {
        expect(screen.getByText('测试分类')).toBeInTheDocument();
      });

      // 检查中文文本 - 使用更灵活的查找方式
      expect(screen.getByText((content, element) => {
        return content.includes('创建时间:') || element?.textContent?.includes('创建时间:');
      })).toBeInTheDocument();
      expect(screen.getByText((content, element) => {
        return content.includes('此分类中的图片') || element?.textContent?.includes('此分类中的图片');
      })).toBeInTheDocument();
    });

    it('显示正确的中文描述或默认提示', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      
      // 测试有描述的情况
      vi.mocked(getCategoryWithImages).mockResolvedValue(mockCategory);
      const { rerender } = renderWithProviders(<CategoryDetail />);

      await waitFor(() => {
        expect(screen.getByText('这是一个测试分类的描述')).toBeInTheDocument();
      });

      // 测试无描述的情况
      const categoryWithoutDescription = { ...mockCategory, description: '' };
      vi.mocked(getCategoryWithImages).mockResolvedValue(categoryWithoutDescription);
      
      rerender(
        <MemoryRouter>
          <ThemeProvider>
            <AuthProvider>
              <CategoryDetail />
            </AuthProvider>
          </ThemeProvider>
        </MemoryRouter>
      );

      await waitFor(() => {
        expect(screen.getByText((content, element) => {
          return content.includes('此分类暂无描述') || element?.textContent?.includes('此分类暂无描述');
        })).toBeInTheDocument();
      });
    });

    it('显示正确的无图片提示信息', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      const categoryWithoutImages = { ...mockCategory, images: [] };
      vi.mocked(getCategoryWithImages).mockResolvedValue(categoryWithoutImages);

      renderWithProviders(<CategoryDetail />);

      await waitFor(() => {
        expect(screen.getByText('此分类中暂无图片。 登录后可上传图片。')).toBeInTheDocument();
      });
    });

    it('在未找到分类时显示正确的中文提示', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      vi.mocked(getCategoryWithImages).mockRejectedValue(new Error('Not found'));

      renderWithProviders(<CategoryDetail />);

      await waitFor(() => {
        expect(screen.getByText('未找到分类。')).toBeInTheDocument();
      });
    });
  });

  describe('认证用户界面', () => {
    it('为认证用户显示正确的中文按钮和提示', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      const categoryWithoutImages = { ...mockCategory, images: [] };
      vi.mocked(getCategoryWithImages).mockResolvedValue(categoryWithoutImages);

      // Mock authenticated state
      vi.doMock('../../contexts/AuthContext', () => ({
        useAuth: () => ({ isAuthenticated: true }),
        AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
      }));

      renderWithProviders(<CategoryDetail />, true);

      await waitFor(() => {
        expect(screen.getByText('上传图片')).toBeInTheDocument();
        expect(screen.getByText('此分类中暂无图片。 快来上传一些吧！')).toBeInTheDocument();
      });
    });

    it('显示正确的中文工具提示', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      vi.mocked(getCategoryWithImages).mockResolvedValue(mockCategory);

      // Mock authenticated state
      vi.doMock('../../contexts/AuthContext', () => ({
        useAuth: () => ({ isAuthenticated: true }),
        AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
      }));

      renderWithProviders(<CategoryDetail />, true);

      await waitFor(() => {
        expect(screen.getByTitle('编辑分类')).toBeInTheDocument();
        expect(screen.getByTitle('删除分类')).toBeInTheDocument();
      });
    });
  });

  describe('模态框标题', () => {
    it('显示正确的中文模态框标题', async () => {
      const { getCategoryWithImages } = await import('../../services/api');
      vi.mocked(getCategoryWithImages).mockResolvedValue(mockCategory);

      // Mock authenticated state
      vi.doMock('../../contexts/AuthContext', () => ({
        useAuth: () => ({ isAuthenticated: true }),
        AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
      }));

      const user = userEvent.setup();
      renderWithProviders(<CategoryDetail />, true);

      await waitFor(() => {
        expect(screen.getByText('测试分类')).toBeInTheDocument();
      });

      // 点击编辑按钮
      const editButton = screen.getByTitle('编辑分类');
      await user.click(editButton);

      // 检查编辑模态框标题
      expect(screen.getByText('编辑分类')).toBeInTheDocument();
    });
  });
});
