import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import AnalyticsPage from '../../components/AnalyticsPage';
import { ThemeProvider } from '../../contexts/ThemeContext';
import type { DatabaseStats } from '../../services/database-stats';

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock database stats API
vi.mock('../../services/database-stats', () => ({
  getDatabaseStats: vi.fn(),
  formatStatsNumber: vi.fn((num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  })
}));

// Mock TagsDisplay component
vi.mock('../../components/TagsDisplay', () => ({
  default: ({ className }: { className?: string }) => (
    <div data-testid="tags-display" className={className}>
      Mocked TagsDisplay Component
    </div>
  )
}));

// Mock PieChart component
vi.mock('../../components/PieChart', () => ({
  default: ({ data, colors }: { data: any; colors: any }) => (
    <div data-testid="pie-chart">
      Mocked PieChart Component
    </div>
  )
}));

// Mock CategoryContext
vi.mock('../../contexts/CategoryContext', () => ({
  useCategories: () => ({
    categories: [],
    isLoading: false,
    error: null
  }),
  CategoryProvider: ({ children }: { children: React.ReactNode }) => children
}));

// Mock BirdSightingTimeline component
vi.mock('../../components/BirdSightingTimeline', () => ({
  default: ({ records }: { records: any }) => (
    <div data-testid="bird-sighting-timeline">
      Mocked BirdSightingTimeline Component
    </div>
  )
}));

// Mock Timeline component
vi.mock('../../components/Timeline', () => ({
  default: ({ items, isLoading, hasError, testId }: {
    items?: any[];
    isLoading?: boolean;
    hasError?: boolean;
    testId?: string;
  }) => (
    <div data-testid={testId}>
      <span data-testid="timeline-title">活动时间线</span>
      {isLoading && <span data-testid="timeline-loading">Loading...</span>}
      {hasError && <span data-testid="timeline-error">Error</span>}
      {items && <span data-testid="timeline-items">{items.length} items</span>}
    </div>
  )
}));

// Mock ErrorDisplay component
vi.mock('../../components/ErrorDisplay', () => ({
  default: ({ error }: { error: string }) => (
    <div data-testid="error-display">
      Error: {error}
    </div>
  )
}));

// Mock StatCard component
vi.mock('../../components/StatCard', () => ({
  default: ({ icon, title, value, isLoading, hasError, testId }: {
    icon: any;
    title: string;
    value: number;
    isLoading?: boolean;
    hasError?: boolean;
    testId?: string;
  }) => {
    const formatValue = (num: number): string => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    };

    return (
      <div data-testid={testId}>
        <span data-testid="stat-title">{title}</span>
        <span data-testid="stat-value">
          {isLoading ? 'Loading...' : hasError ? 'Error' : formatValue(value)}
        </span>
        {isLoading && <div data-testid="loading-spinner">Loading...</div>}
      </div>
    );
  }
}));

// Mock LoadingSpinner component
vi.mock('../../components/LoadingSpinner', () => ({
  default: ({ size }: { size?: string }) => (
    <div data-testid="loading-spinner" className={`spinner ${size || ''}`}>
      Loading...
    </div>
  )
}));

// Mock icons
vi.mock('../../components/icons', () => ({
  DocumentTextIcon: ({ className }: { className?: string }) => (
    <div data-testid="document-text-icon" className={className}>📄</div>
  ),
  TagIcon: ({ className }: { className?: string }) => (
    <div data-testid="tag-icon" className={className}>🏷️</div>
  ),
  PhotoIcon: ({ className }: { className?: string }) => (
    <div data-testid="photo-icon" className={className}>📷</div>
  )
}));

// Mock utils
vi.mock('../../utils/dataLoader', () => ({
  loadDataFile: vi.fn().mockResolvedValue({
    success: true,
    data: []
  }),
  DATA_FILES: {
    BIRD_SIGHTINGS: 'bird-sightings.json',
    TOP_BIRDS: 'top-birds.json'
  }
}));

vi.mock('../../utils/animations', () => ({
  fadeInVariants: {},
  staggerContainerVariants: {},
  staggerItemVariants: {},
  getAnimationConfig: (variants: any) => variants
}));



import { getDatabaseStats } from '../../services/database-stats';

const mockGetDatabaseStats = getDatabaseStats as any;

// Test data
const mockDatabaseStats: DatabaseStats = {
  categoriesCount: 15,
  imagesCount: 250,
  tagsCount: 42
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('AnalyticsPage Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('持续测试覆盖范围', () => {
    describe('数据库统计数据正确加载和显示', () => {
      it('应该成功加载并显示数据库统计数据', async () => {
        mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        // 等待数据加载完成
        await waitFor(() => {
          expect(screen.getByText('15')).toBeInTheDocument();
        });

        // 验证所有统计数据都显示
        expect(screen.getByText('15')).toBeInTheDocument(); // 种类数
        expect(screen.getByText('250')).toBeInTheDocument(); // 图片数
        expect(screen.getByText('42')).toBeInTheDocument(); // 标签数

        // 验证标签文本
        expect(screen.getByText('种类数')).toBeInTheDocument();
        expect(screen.getByText('图片数')).toBeInTheDocument();
        expect(screen.getByText('标签数量')).toBeInTheDocument();

        // 验证API调用
        expect(mockGetDatabaseStats).toHaveBeenCalledTimes(1);
      });

      it('应该在数据加载时显示加载状态', () => {
        mockGetDatabaseStats.mockImplementation(() => new Promise(() => {})); // 永不resolve

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        // 验证加载状态 - 3个StatCard + 2个其他组件的loading spinner
        const loadingSpinners = screen.getAllByTestId('loading-spinner');
        expect(loadingSpinners.length).toBeGreaterThanOrEqual(3);
      });

      it('应该正确格式化大数值', async () => {
        const largeMockStats: DatabaseStats = {
          categoriesCount: 1500,
          imagesCount: 25000,
          tagsCount: 4200
        };

        mockGetDatabaseStats.mockResolvedValue(largeMockStats);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('1.5K')).toBeInTheDocument();
        });

        expect(screen.getByText('1.5K')).toBeInTheDocument(); // 1500 -> 1.5K
        expect(screen.getByText('25.0K')).toBeInTheDocument(); // 25000 -> 25.0K
        expect(screen.getByText('4.2K')).toBeInTheDocument(); // 4200 -> 4.2K
      });
    });

    describe('标签展示组件功能正常', () => {
      it('应该正确渲染TagsDisplay组件', async () => {
        mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        // 验证TagsDisplay组件存在
        expect(screen.getByTestId('tags-display')).toBeInTheDocument();
        expect(screen.getByText('Mocked TagsDisplay Component')).toBeInTheDocument();
      });

      it('应该为TagsDisplay组件传递正确的高度绑定', async () => {
        mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByTestId('tags-display')).toBeInTheDocument();
        });

        const tagsDisplay = screen.getByTestId('tags-display');
        const tagsSection = screen.getByTestId('tags-section');

        // 验证TagsDisplay组件存在并且在正确的容器中
        expect(tagsDisplay).toBeInTheDocument();
        expect(tagsSection).toContainElement(tagsDisplay);
        expect(tagsDisplay).toHaveClass('w-full');
      });
    });

    describe('数据加载错误处理', () => {
      it('应该正确处理数据库统计加载错误', async () => {
        const errorMessage = 'Database connection failed';
        mockGetDatabaseStats.mockRejectedValue(new Error(errorMessage));

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getAllByText('Error')).toHaveLength(3);
        });

        // 验证错误状态显示
        expect(screen.getAllByText('Error')).toHaveLength(3);
      });

      it('应该在错误状态下仍然显示TagsDisplay组件', async () => {
        mockGetDatabaseStats.mockRejectedValue(new Error('Network error'));

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getAllByText('Error')).toHaveLength(3);
        });

        // TagsDisplay组件应该仍然显示
        expect(screen.getByTestId('tags-display')).toBeInTheDocument();
      });
    });

    describe('统计数据更新机制', () => {
      it('应该支持数据刷新', async () => {
        // 设置初始数据
        mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

        render(
          <TestWrapper>
            <AnalyticsPage />
          </TestWrapper>
        );

        // 验证初始数据加载
        await waitFor(() => {
          expect(screen.getByText('15')).toBeInTheDocument();
        });

        expect(screen.getByText('15')).toBeInTheDocument();
        expect(screen.getByText('250')).toBeInTheDocument();
        expect(screen.getByText('42')).toBeInTheDocument();

        // 验证组件能够正确显示数据库统计数据
        expect(screen.getByTestId('stats-card-categories')).toBeInTheDocument();
        expect(screen.getByTestId('stats-card-images')).toBeInTheDocument();
        expect(screen.getByTestId('stats-card-tags')).toBeInTheDocument();
      });
    });
  });

  describe('一次性验证项目（不需要持续测试）', () => {
    it('应该正确渲染页面标题', async () => {
      mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      expect(screen.getByText('数据分析')).toBeInTheDocument();
    });

    it('应该正确渲染统计卡片布局', async () => {
      mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证统计卡片容器
      expect(screen.getByTestId('stats-cards-container')).toBeInTheDocument();
      
      // 验证三个统计卡片
      expect(screen.getByTestId('stats-card-categories')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-images')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-tags')).toBeInTheDocument();
    });

    it('应该不包含已删除的社交媒体卡片', () => {
      mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证不存在社交媒体相关内容
      expect(screen.queryByText('中国观鸟记录中心')).not.toBeInTheDocument();
      expect(screen.queryByText('小红书')).not.toBeInTheDocument();
      expect(screen.queryByText('社交媒体')).not.toBeInTheDocument();
    });

    it('应该不包含已删除的Bird Sighting Regions组件', () => {
      mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证不存在地图相关内容
      expect(screen.queryByText('Bird Sighting Regions')).not.toBeInTheDocument();
      expect(screen.queryByText('观鸟区域')).not.toBeInTheDocument();
      expect(screen.queryByTestId('bird-sighting-regions')).not.toBeInTheDocument();
    });

    it('应该正确布局Timeline和TagsDisplay组件', async () => {
      mockGetDatabaseStats.mockResolvedValue(mockDatabaseStats);

      render(
        <TestWrapper>
          <AnalyticsPage />
        </TestWrapper>
      );

      // 验证Timeline和TagsDisplay在同一行
      const timelineSection = screen.getByTestId('timeline-section');
      const tagsSection = screen.getByTestId('tags-section');
      
      expect(timelineSection).toBeInTheDocument();
      expect(tagsSection).toBeInTheDocument();
    });
  });
});
