import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import TagsDisplay from '../../components/TagsDisplay';
import { ThemeProvider } from '../../contexts/ThemeContext';
import type { TagRead } from '../../types';

// Mock getAllTags API
vi.mock('../../services/api', () => ({
  getAllTags: vi.fn()
}));

// Mock react-router-dom navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate
  };
});

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  }
}));

import { getAllTags } from '../../services/api';

const mockGetAllTags = getAllTags as any;

// Test data
const mockTags: TagRead[] = [
  {
    id: '1',
    name: '鸟类摄影',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2', 
    name: '野生动物',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    name: '自然风光',
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z'
  }
];

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; theme?: string }> = ({ 
  children, 
  theme = 'modern' 
}) => (
  <BrowserRouter>
    <ThemeProvider>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('TagsDisplay Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('持续测试覆盖范围', () => {
    describe('标签数据加载和显示逻辑', () => {
      it('应该成功加载并显示标签列表', async () => {
        mockGetAllTags.mockResolvedValue(mockTags);

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        // 验证加载状态
        expect(screen.getByTestId('tags-loading')).toBeInTheDocument();

        // 等待数据加载完成
        await waitFor(() => {
          expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        });

        // 验证所有标签都显示
        expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        expect(screen.getByText('野生动物')).toBeInTheDocument();
        expect(screen.getByText('自然风光')).toBeInTheDocument();

        // 验证API调用
        expect(mockGetAllTags).toHaveBeenCalledTimes(1);
      });

      it('应该在数据加载时显示加载状态', () => {
        mockGetAllTags.mockImplementation(() => new Promise(() => {})); // 永不resolve

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        expect(screen.getByTestId('tags-loading')).toBeInTheDocument();
        expect(screen.queryByText('鸟类摄影')).not.toBeInTheDocument();
      });

      it('应该正确处理标签数据更新', async () => {
        const { rerender } = render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        // 初始数据
        mockGetAllTags.mockResolvedValue(mockTags);
        
        await waitFor(() => {
          expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        });

        // 更新数据
        const updatedTags = [
          ...mockTags,
          {
            id: '4',
            name: '新标签',
            created_at: '2024-01-04T00:00:00Z',
            updated_at: '2024-01-04T00:00:00Z'
          }
        ];

        mockGetAllTags.mockResolvedValue(updatedTags);

        // 重新渲染组件
        rerender(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('新标签')).toBeInTheDocument();
        });
      });
    });

    describe('点击跳转功能', () => {
      it('应该在点击标签时正确跳转', async () => {
        mockGetAllTags.mockResolvedValue(mockTags);

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        });

        // 点击标签
        fireEvent.click(screen.getByText('鸟类摄影'));

        // 验证导航调用（应该是编码后的URL）
        expect(mockNavigate).toHaveBeenCalledWith('/tags/%E9%B8%9F%E7%B1%BB%E6%91%84%E5%BD%B1');
      });

      it('应该正确编码标签名称进行跳转', async () => {
        const tagsWithSpecialChars: TagRead[] = [
          {
            id: '1',
            name: '鸟类/摄影 & 观察',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          }
        ];

        mockGetAllTags.mockResolvedValue(tagsWithSpecialChars);

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('鸟类/摄影 & 观察')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('鸟类/摄影 & 观察'));

        expect(mockNavigate).toHaveBeenCalledWith('/tags/%E9%B8%9F%E7%B1%BB%2F%E6%91%84%E5%BD%B1%20%26%20%E8%A7%82%E5%AF%9F');
      });
    });

    describe('空状态和错误状态处理', () => {
      it('应该正确显示空状态', async () => {
        mockGetAllTags.mockResolvedValue([]);

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByTestId('tags-empty-state')).toBeInTheDocument();
        });

        expect(screen.getByText('暂无标签')).toBeInTheDocument();
        expect(screen.getByText('还没有创建任何标签')).toBeInTheDocument();
      });

      it('应该正确处理加载错误', async () => {
        const errorMessage = 'Failed to load tags';
        mockGetAllTags.mockRejectedValue(new Error(errorMessage));

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByTestId('tags-error-state')).toBeInTheDocument();
        });

        expect(screen.getByText('加载标签失败')).toBeInTheDocument();
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      it('应该提供重试功能', async () => {
        mockGetAllTags.mockRejectedValueOnce(new Error('Network error'));

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByTestId('tags-error-state')).toBeInTheDocument();
        });

        // 点击重试按钮
        const retryButton = screen.getByText('重试');
        expect(retryButton).toBeInTheDocument();

        // 模拟重试成功
        mockGetAllTags.mockResolvedValue(mockTags);
        fireEvent.click(retryButton);

        await waitFor(() => {
          expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        });

        expect(mockGetAllTags).toHaveBeenCalledTimes(2);
      });
    });

    describe('数据更新响应', () => {
      it('应该支持手动刷新标签数据', async () => {
        mockGetAllTags.mockResolvedValue(mockTags);

        render(
          <TestWrapper>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        });

        // 验证初始调用
        expect(mockGetAllTags).toHaveBeenCalledTimes(1);

        // 模拟外部数据更新，组件应该能够响应
        const refreshButton = screen.getByTestId('tags-refresh-button');
        fireEvent.click(refreshButton);

        expect(mockGetAllTags).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('一次性验证项目（不需要持续测试）', () => {
    it('应该正确渲染基本结构', async () => {
      mockGetAllTags.mockResolvedValue(mockTags);

      render(
        <TestWrapper>
          <TagsDisplay />
        </TestWrapper>
      );

      // 验证容器存在
      expect(screen.getByTestId('tags-display-container')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('tags-grid')).toBeInTheDocument();
      });
    });

    it('应该具有可滚动的容器', async () => {
      mockGetAllTags.mockResolvedValue(mockTags);

      render(
        <TestWrapper>
          <TagsDisplay />
        </TestWrapper>
      );

      await waitFor(() => {
        const container = screen.getByTestId('tags-display-container');
        expect(container).toHaveClass('overflow-y-auto');
      });
    });

    it('应该正确应用主题样式类', async () => {
      mockGetAllTags.mockResolvedValue(mockTags);

      render(
        <TestWrapper>
          <TagsDisplay />
        </TestWrapper>
      );

      await waitFor(() => {
        const tagElements = screen.getAllByTestId(/^tag-item-/);
        expect(tagElements.length).toBe(3);

        // 验证标签元素包含主题相关的CSS类
        tagElements.forEach(tag => {
          expect(tag).toHaveClass('cursor-pointer');
          expect(tag).toHaveClass('transition-all');
        });
      });
    });

    it('应该支持键盘导航', async () => {
      mockGetAllTags.mockResolvedValue(mockTags);

      render(
        <TestWrapper>
          <TagsDisplay />
        </TestWrapper>
      );

      await waitFor(() => {
        const firstTag = screen.getByText('鸟类摄影');
        expect(firstTag).toBeInTheDocument();
      });

      const firstTagContainer = screen.getByTestId('tag-item-1');

      // 验证可以获得焦点
      firstTagContainer.focus();
      expect(firstTagContainer).toHaveFocus();

      // 验证回车键触发点击
      fireEvent.keyDown(firstTagContainer, { key: 'Enter', code: 'Enter' });
      expect(mockNavigate).toHaveBeenCalledWith('/tags/%E9%B8%9F%E7%B1%BB%E6%91%84%E5%BD%B1');
    });
  });

  describe('主题适配验证', () => {
    const themes = ['modern', 'nature', 'neonGalaxy', 'arcadeFlash', 'retroTechDark'];

    themes.forEach(theme => {
      it(`应该在${theme}主题下正确渲染`, async () => {
        mockGetAllTags.mockResolvedValue(mockTags);

        render(
          <TestWrapper theme={theme}>
            <TagsDisplay />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('鸟类摄影')).toBeInTheDocument();
        });

        // 验证主题相关的样式类存在
        const container = screen.getByTestId('tags-display-container');
        expect(container).toBeInTheDocument();

        const tagElements = screen.getAllByTestId(/^tag-item-/);
        expect(tagElements.length).toBe(3);
      });
    });
  });
});
