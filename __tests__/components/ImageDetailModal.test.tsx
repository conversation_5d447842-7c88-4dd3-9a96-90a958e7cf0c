import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import ImageDetailModal from '../../components/ImageDetailModal';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock the electron API
const mockElectronAPI = {
  updateImage: vi.fn(),
  deleteImage: vi.fn(),
  setCategoryThumbnail: vi.fn(),
  removeCategoryThumbnail: vi.fn(),
};

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

const mockImage = {
  id: 1,
  title: '测试图片',
  description: '这是一个测试图片',
  tags: ['标签1', '标签2'],
  file_path: '/test/image.jpg',
  original_filename: 'test.jpg',
  category_id: 1,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const defaultProps = {
  isOpen: true,
  onClose: vi.fn(),
  image: mockImage,
  onImageUpdate: vi.fn(),
  onImageDelete: vi.fn(),
  isAuthenticated: true,
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        <AuthProvider>
          {component}
        </AuthProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('ImageDetailModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('中文文本显示', () => {
    it('显示正确的中文标签', () => {
      renderWithProviders(<ImageDetailModal {...defaultProps} />);

      // 检查是否显示图片标题
      expect(screen.getByText('测试图片')).toBeInTheDocument();
    });

    it('在编辑模式下显示正确的中文表单标签', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ImageDetailModal {...defaultProps} />);

      // 进入编辑模式
      const editButton = screen.getByRole('button', { name: /编辑/i });
      await user.click(editButton);

      // 检查表单标签
      expect(screen.getByText('标题')).toBeInTheDocument();
      expect(screen.getByText('描述')).toBeInTheDocument();
      expect(screen.getByText('标签（逗号分隔）')).toBeInTheDocument();
    });

    it('显示正确的中文按钮文本', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ImageDetailModal {...defaultProps} />);
      
      // 进入编辑模式
      const editButton = screen.getByRole('button', { name: /编辑/i });
      await user.click(editButton);
      
      // 检查按钮文本
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '保存更改' })).toBeInTheDocument();
    });

    it('显示正确的删除按钮文本', () => {
      renderWithProviders(<ImageDetailModal {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /删除/i })).toBeInTheDocument();
    });

    it('显示正确的缩略图按钮文本', () => {
      renderWithProviders(<ImageDetailModal {...defaultProps} />);
      
      expect(screen.getByRole('button', { name: /设为缩略图/i })).toBeInTheDocument();
    });

    it('显示无标题图片的中文提示', () => {
      const imageWithoutTitle = { ...mockImage, title: '' };
      renderWithProviders(<ImageDetailModal {...defaultProps} image={imageWithoutTitle} />);
      
      expect(screen.getByText('无标题图片')).toBeInTheDocument();
    });

    it('显示无描述的中文提示', () => {
      const imageWithoutDescription = { ...mockImage, description: '' };
      renderWithProviders(<ImageDetailModal {...defaultProps} image={imageWithoutDescription} />);
      
      expect(screen.getByText('暂无描述。')).toBeInTheDocument();
    });
  });

  describe('删除确认对话框', () => {
    it('显示正确的中文删除确认信息', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ImageDetailModal {...defaultProps} />);
      
      // 点击删除按钮
      const deleteButton = screen.getByRole('button', { name: /删除/i });
      await user.click(deleteButton);
      
      // 检查删除确认对话框
      expect(screen.getByText('删除图片')).toBeInTheDocument();
      expect(screen.getByText(/确定要删除图片/)).toBeInTheDocument();
      expect(screen.getByText(/此操作无法撤销/)).toBeInTheDocument();
    });
  });
});
