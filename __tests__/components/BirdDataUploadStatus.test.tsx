import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import BirdDataUploadStatus from '../../components/BirdDataUploadStatus';

// Mock dependencies
vi.mock('../../services/bird-sighting-api', () => ({
  getBirdSightingDataStatus: vi.fn(),
  hasBirdSightingData: vi.fn(),
}));

vi.mock('../../components/BirdDataUploadButton', () => ({
  default: ({ onUploadComplete }: { onUploadComplete?: () => void }) => (
    <button onClick={() => onUploadComplete?.()}>
      Mock Upload Button
    </button>
  ),
}));

// Mock theme context
vi.mock('../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      brandColor: 'text-blue-600 dark:text-blue-400', // 添加这个以修复LoadingSpinner错误
      card: {
        bg: 'bg-white',
        text: 'text-gray-900',
        secondaryText: 'text-gray-500',
        rounded: 'rounded-lg',
        border: 'border border-gray-200',
        shadow: 'shadow-md',
      },
      text: 'text-gray-900',
      button: {
        primary: 'bg-blue-600',
        primaryText: 'text-white',
        secondary: 'bg-gray-200',
        secondaryText: 'text-gray-700',
      },
    },
  }),
}));

import * as birdSightingApi from '../../services/bird-sighting-api';

describe('BirdDataUploadStatus', () => {
  const user = userEvent.setup();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock console methods
    console.log = vi.fn();
    console.error = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('无数据状态', () => {
    beforeEach(() => {
      (birdSightingApi.getBirdSightingDataStatus as any).mockResolvedValue({
        hasData: false,
        suggestions: [
          '当前没有观鸟数据，点击"上传数据"按钮导入Excel文件',
          'Excel文件应包含鸟种编号、中文名、拉丁名、英文名、目、科、记录次数、记录时间等字段'
        ]
      });
    });

    it('应该显示无数据提示', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/当前没有观鸟数据/)).toBeInTheDocument();
      });
    });

    it('应该显示上传建议信息', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/点击.*上传数据.*按钮导入Excel文件/)).toBeInTheDocument();
        expect(screen.getByText(/Excel文件应包含.*字段/)).toBeInTheDocument();
      });
    });

    it('应该显示上传按钮', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText('Mock Upload Button')).toBeInTheDocument();
      });
    });

    it('应该显示数据格式要求', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/支持的格式/i)).toBeInTheDocument();
        expect(screen.getByText(/\.xlsx/)).toBeInTheDocument();
        expect(screen.getByText(/\.xls/)).toBeInTheDocument();
      });
    });
  });

  describe('有数据状态', () => {
    beforeEach(() => {
      (birdSightingApi.getBirdSightingDataStatus as any).mockResolvedValue({
        hasData: true,
        stats: {
          totalRecords: 15,
          totalSpecies: 12,
          totalObservations: 48,
          latestRecord: '2025/3/22'
        },
        suggestions: [
          '数据已加载，可以查看统计信息和时间线图表',
          '当前共有 15 条观测记录，涵盖 12 个物种',
          '可以上传新的Excel文件来更新数据（将覆盖现有数据）'
        ]
      });
    });

    it('应该显示数据统计信息', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/15.*条记录/)).toBeInTheDocument();
        expect(screen.getByText(/12.*个物种/)).toBeInTheDocument();
        expect(screen.getByText(/48.*次观测/)).toBeInTheDocument();
      });
    });

    it('应该显示最新记录时间', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/2025\/3\/22/)).toBeInTheDocument();
      });
    });

    it('应该显示更新提示', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/可以上传新的Excel文件来更新数据/)).toBeInTheDocument();
      });
    });

    it('应该仍然显示上传按钮用于更新数据', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText('Mock Upload Button')).toBeInTheDocument();
      });
    });
  });

  describe('加载状态', () => {
    beforeEach(() => {
      // Mock a delayed response
      (birdSightingApi.getBirdSightingDataStatus as any).mockImplementation(
        () => new Promise(resolve => 
          setTimeout(() => resolve({
            hasData: false,
            suggestions: ['无数据']
          }), 100)
        )
      );
    });

    it('应该显示加载指示器', () => {
      render(<BirdDataUploadStatus />);
      
      expect(screen.getByText(/加载中/i)).toBeInTheDocument();
    });

    it('应该在加载完成后显示内容', async () => {
      render(<BirdDataUploadStatus />);
      
      expect(screen.getByText(/加载中/i)).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText(/加载中/i)).not.toBeInTheDocument();
        expect(screen.getByText(/无数据/)).toBeInTheDocument();
      }, { timeout: 200 });
    });
  });

  describe('错误状态', () => {
    beforeEach(() => {
      (birdSightingApi.getBirdSightingDataStatus as any).mockRejectedValue(
        new Error('获取数据状态失败')
      );
    });

    it('应该显示错误信息', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByText(/加载失败/i)).toBeInTheDocument();
        expect(screen.getByText(/获取数据状态失败/)).toBeInTheDocument();
      });
    });

    it('应该提供重试按钮', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /重试/i })).toBeInTheDocument();
      });
    });

    it('应该在点击重试时重新加载', async () => {
      // First call fails, second call succeeds
      (birdSightingApi.getBirdSightingDataStatus as any)
        .mockRejectedValueOnce(new Error('获取数据状态失败'))
        .mockResolvedValueOnce({
          hasData: false,
          suggestions: ['重试后的数据']
        });

      render(<BirdDataUploadStatus />);
      
      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText(/加载失败/i)).toBeInTheDocument();
      });
      
      // Click retry button
      const retryButton = screen.getByRole('button', { name: /重试/i });
      await user.click(retryButton);
      
      // Wait for success state
      await waitFor(() => {
        expect(screen.getByText(/重试后的数据/)).toBeInTheDocument();
      });
    });
  });

  describe('数据刷新', () => {
    it('应该在上传完成后刷新数据', async () => {
      // Mock initial state
      (birdSightingApi.getBirdSightingDataStatus as any).mockResolvedValueOnce({
        hasData: false,
        suggestions: ['初始无数据']
      });

      render(<BirdDataUploadStatus />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText(/初始无数据/)).toBeInTheDocument();
      });
      
      // Mock updated state after upload
      (birdSightingApi.getBirdSightingDataStatus as any).mockResolvedValueOnce({
        hasData: true,
        stats: {
          totalRecords: 5,
          totalSpecies: 4,
          totalObservations: 12,
          latestRecord: '2025/3/22'
        },
        suggestions: ['上传后有数据了']
      });
      
      // Simulate upload completion
      const uploadButton = screen.getByText('Mock Upload Button');
      await user.click(uploadButton);
      
      // Wait for data refresh
      await waitFor(() => {
        expect(screen.getByText(/5.*条记录/)).toBeInTheDocument();
      });
    });

    it('应该支持手动刷新', async () => {
      render(<BirdDataUploadStatus showRefreshButton={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /刷新/i })).toBeInTheDocument();
      });
    });
  });

  describe('主题适配', () => {
    it('应该应用正确的主题样式', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        const container = screen.getByTestId('bird-data-upload-status');
        expect(container).toHaveClass('bg-white');
        expect(container).toHaveClass('text-gray-900');
        expect(container).toHaveClass('rounded-lg');
      });
    });
  });

  describe('可访问性', () => {
    it('应该有正确的ARIA标签', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        const statusContainer = screen.getByRole('region');
        expect(statusContainer).toHaveAttribute('aria-label', '观鸟数据状态');
      });
    });

    it('应该支持屏幕阅读器', async () => {
      render(<BirdDataUploadStatus />);
      
      await waitFor(() => {
        const statusText = screen.getByRole('status');
        expect(statusText).toBeInTheDocument();
      });
    });
  });

  describe('组件属性', () => {
    it('应该接受自定义类名', async () => {
      render(<BirdDataUploadStatus className="custom-class" />);
      
      await waitFor(() => {
        const container = screen.getByTestId('bird-data-upload-status');
        expect(container).toHaveClass('custom-class');
      });
    });

    it('应该接受hideUploadButton属性', async () => {
      render(<BirdDataUploadStatus hideUploadButton={true} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Mock Upload Button')).not.toBeInTheDocument();
      });
    });

    it('应该接受compact属性', async () => {
      render(<BirdDataUploadStatus compact={true} />);
      
      await waitFor(() => {
        const container = screen.getByTestId('bird-data-upload-status');
        expect(container).toHaveClass('p-4'); // 较小的padding
      });
    });
  });

  describe('数据格式显示', () => {
    it('应该显示Excel文件要求的字段信息', async () => {
      render(<BirdDataUploadStatus showFieldRequirements={true} />);
      
      await waitFor(() => {
        expect(screen.getByText(/必需字段/i)).toBeInTheDocument();
        expect(screen.getByText(/鸟种编号/)).toBeInTheDocument();
        expect(screen.getByText(/中文名/)).toBeInTheDocument();
        expect(screen.getByText(/拉丁名/)).toBeInTheDocument();
        expect(screen.getByText(/记录次数/)).toBeInTheDocument();
      });
    });
  });
});