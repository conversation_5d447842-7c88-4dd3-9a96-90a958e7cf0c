import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import ImageUploadForm from '../../components/ImageUploadForm';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { CategoryProvider } from '../../contexts/CategoryContext';

// Mock the electron API
const mockElectronAPI = {
  getCategories: vi.fn(),
};

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

const defaultProps = {
  categoryId: '1',
  onSubmit: vi.fn(),
  onCancel: vi.fn(),
  isLoading: false,
  error: null,
};

const mockCategories = [
  { id: '1', name: '测试分类', description: '测试描述', created_at: '2024-01-01', updated_at: '2024-01-01' }
];

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        <CategoryProvider>
          {component}
        </CategoryProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('ImageUploadForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockElectronAPI.getCategories.mockResolvedValue(mockCategories);
  });

  describe('中文文本显示', () => {
    it('显示正确的中文表单标签', () => {
      renderWithProviders(<ImageUploadForm {...defaultProps} />);
      
      // 检查表单标签
      expect(screen.getByText('图片文件')).toBeInTheDocument();
      expect(screen.getByText('标题（可选）')).toBeInTheDocument();
      expect(screen.getByText('描述（可选）')).toBeInTheDocument();
      expect(screen.getByText('标签（可选，逗号分隔）')).toBeInTheDocument();
      expect(screen.getByText('设为分类缩略图')).toBeInTheDocument();
    });

    it('显示正确的中文按钮文本', () => {
      renderWithProviders(<ImageUploadForm {...defaultProps} />);
      
      // 检查按钮文本
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '上传图片' })).toBeInTheDocument();
    });

    it('显示正确的中文上传提示', () => {
      renderWithProviders(<ImageUploadForm {...defaultProps} />);
      
      // 检查上传提示文本
      expect(screen.getByText('上传文件')).toBeInTheDocument();
      expect(screen.getByText('或拖拽上传')).toBeInTheDocument();
      expect(screen.getByText('支持PNG、JPG、GIF格式，最大10MB')).toBeInTheDocument();
    });

    it('在加载状态下显示正确的中文文本', () => {
      renderWithProviders(<ImageUploadForm {...defaultProps} isLoading={true} />);
      
      // 检查加载状态文本
      expect(screen.getByRole('button', { name: '上传中...' })).toBeInTheDocument();
    });

    it('显示文件选择后的中文提示', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ImageUploadForm {...defaultProps} />);
      
      // 创建一个模拟文件
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const fileInput = screen.getByLabelText(/图片文件/i);
      
      // 上传文件
      await user.upload(fileInput, file);
      
      // 检查文件选择提示
      expect(screen.getByText('已选择: test.jpg')).toBeInTheDocument();
    });
  });

  describe('表单验证', () => {
    it('在未选择文件时显示中文错误信息', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ImageUploadForm {...defaultProps} />);
      
      // 尝试提交表单而不选择文件
      const submitButton = screen.getByRole('button', { name: '上传图片' });
      await user.click(submitButton);
      
      // 检查错误信息
      expect(screen.getByText('请选择一个图片文件。')).toBeInTheDocument();
    });
  });
});
