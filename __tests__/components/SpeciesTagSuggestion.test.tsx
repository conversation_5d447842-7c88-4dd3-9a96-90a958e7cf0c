import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import SpeciesTagSuggestion from '../../components/SpeciesTagSuggestion';
import type { SpeciesDictionaryEntry } from '../../schemas/speciesDictionary';
import * as speciesService from '../../services/species';

// Mock dependencies
vi.mock('../../services/species', () => ({
  getSpeciesInfo: vi.fn(),
}));

vi.mock('../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      card: {
        text: 'text-gray-900',
        secondaryText: 'text-gray-600',
        bg: 'bg-white',
        border: 'border-gray-300',
        rounded: 'rounded-lg',
      },
      button: {
        primary: 'bg-blue-600',
        primaryText: 'text-white',
        secondary: 'bg-gray-200',
        secondaryText: 'text-gray-700',
        transition: 'transition duration-200',
      },
      brandColor: 'text-blue-600',
    },
  }),
}));

describe('SpeciesTagSuggestion', () => {
  const mockOnAddTags = vi.fn();
  
  const mockSpeciesData: SpeciesDictionaryEntry = {
    目: '雀形目',
    科: '鸦科',
    属: '鹊属',
    种: '喜鹊',
  };

  const defaultProps = {
    categoryName: '喜鹊',
    onAddTags: mockOnAddTags,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('组件渲染', () => {
    it('应该渲染基本组件结构', () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(null);
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      // 应该不显示任何内容，因为没有匹配到物种
      const container = screen.queryByTestId('species-tag-suggestion');
      expect(container).not.toBeInTheDocument();
    });

    it('当匹配到物种时应该显示建议内容', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      expect(screen.getByText(/添加喜鹊对应目科属/)).toBeInTheDocument();
      expect(screen.getByText(/雀形目·鸦科·鹊属/)).toBeInTheDocument();
      expect(screen.getByText('添加到标签')).toBeInTheDocument();
    });

    it('应该使用自定义className', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);
      const customClassName = 'custom-class';
      
      render(<SpeciesTagSuggestion {...defaultProps} className={customClassName} />);
      
      await waitFor(() => {
        const container = screen.getByTestId('species-tag-suggestion');
        expect(container).toHaveClass(customClassName);
      });
    });
  });

  describe('物种匹配功能', () => {
    it('应该在组件加载时调用getSpeciesInfo', () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(null);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('喜鹊');
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledTimes(1);
    });

    it('当categoryName变化时应该重新匹配', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(null);
      
      const { rerender } = render(<SpeciesTagSuggestion {...defaultProps} />);
      
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('喜鹊');
      
      vi.mocked(speciesService.getSpeciesInfo).mockClear();
      rerender(<SpeciesTagSuggestion {...defaultProps} categoryName="白头鹎" />);
      
      expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalledWith('白头鹎');
    });

    it('应该处理API错误', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      vi.mocked(speciesService.getSpeciesInfo).mockRejectedValue(new Error('API错误'));
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('获取物种信息失败:', expect.any(Error));
      });

      const container = screen.queryByTestId('species-tag-suggestion');
      expect(container).not.toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });

  describe('标签添加功能', () => {
    it('点击按钮时应该调用onAddTags', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);
      
      expect(mockOnAddTags).toHaveBeenCalledWith('雀形目,鸦科,鹊属');
      expect(mockOnAddTags).toHaveBeenCalledTimes(1);
    });

    it('应该正确格式化目科属标签', async () => {
      const speciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(speciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);
      
      expect(mockOnAddTags).toHaveBeenCalledWith('雀形目,鸦科,鹊属');
    });

    it('应该处理属字段为空的情况', async () => {
      const speciesData: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '',
        种: '喜鹊',
      };
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(speciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);
      
      expect(mockOnAddTags).toHaveBeenCalledWith('雀形目,鸦科');
    });
  });

  describe('主题样式适配', () => {
    it('应该应用正确的主题样式', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const container = screen.getByTestId('species-tag-suggestion');
      expect(container).toHaveClass('bg-white', 'border-gray-300', 'rounded-lg');
      
      const addButton = screen.getByText('添加到标签');
      expect(addButton).toHaveClass('bg-blue-600', 'text-white', 'transition', 'duration-200');
    });
  });

  describe('可访问性', () => {
    it('应该有正确的ARIA标签', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const addButton = screen.getByText('添加到标签');
      expect(addButton).toHaveAttribute('aria-label', '添加物种目科属标签到标签字段');
    });

    it('应该支持键盘导航', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(mockSpeciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const addButton = screen.getByText('添加到标签');
      
      // 模拟键盘Enter按键
      fireEvent.keyDown(addButton, { key: 'Enter', code: 'Enter' });
      expect(mockOnAddTags).toHaveBeenCalledWith('雀形目,鸦科,鹊属');
      
      mockOnAddTags.mockClear();
      
      // 模拟键盘Space按键
      fireEvent.keyDown(addButton, { key: ' ', code: 'Space' });
      expect(mockOnAddTags).toHaveBeenCalledWith('雀形目,鸦科,鹊属');
    });
  });

  describe('边界情况', () => {
    it('当categoryName为空时不应该调用API', () => {
      render(<SpeciesTagSuggestion {...defaultProps} categoryName="" />);
      
      expect(vi.mocked(speciesService.getSpeciesInfo)).not.toHaveBeenCalled();
      
      const container = screen.queryByTestId('species-tag-suggestion');
      expect(container).not.toBeInTheDocument();
    });

    it('当API返回null时不应该显示建议', async () => {
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(null);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(vi.mocked(speciesService.getSpeciesInfo)).toHaveBeenCalled();
      });

      const container = screen.queryByTestId('species-tag-suggestion');
      expect(container).not.toBeInTheDocument();
    });

    it('应该正确处理部分缺失的分类信息', async () => {
      const incompleteSpeciesData: SpeciesDictionaryEntry = {
        目: '',
        科: '鸦科',
        属: '鹊属',
        种: '喜鹊',
      };
      vi.mocked(speciesService.getSpeciesInfo).mockResolvedValue(incompleteSpeciesData);
      
      render(<SpeciesTagSuggestion {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('species-tag-suggestion')).toBeInTheDocument();
      });

      const addButton = screen.getByText('添加到标签');
      fireEvent.click(addButton);
      
      expect(mockOnAddTags).toHaveBeenCalledWith('鸦科,鹊属');
    });
  });
});