import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import AlertDialog from '../../components/AlertDialog';
import { ThemeProvider } from '../../contexts/ThemeContext';

const defaultProps = {
  isOpen: true,
  onClose: vi.fn(),
  onConfirm: vi.fn(),
  title: '测试对话框',
  message: '这是一个测试消息',
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        {component}
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('AlertDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('中文文本显示', () => {
    it('显示正确的默认中文按钮文本', () => {
      renderWithProviders(<AlertDialog {...defaultProps} />);
      
      // 检查默认按钮文本
      expect(screen.getByRole('button', { name: '确认' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument();
    });

    it('显示自定义的中文按钮文本', () => {
      renderWithProviders(
        <AlertDialog 
          {...defaultProps} 
          confirmText='删除' 
          cancelText='返回' 
        />
      );
      
      // 检查自定义按钮文本
      expect(screen.getByRole('button', { name: '删除' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '返回' })).toBeInTheDocument();
    });

    it('显示正确的标题和消息', () => {
      renderWithProviders(<AlertDialog {...defaultProps} />);
      
      // 检查标题和消息
      expect(screen.getByText('测试对话框')).toBeInTheDocument();
      expect(screen.getByText('这是一个测试消息')).toBeInTheDocument();
    });

    it('支持中文标题和消息', () => {
      const chineseProps = {
        ...defaultProps,
        title: '删除确认',
        message: '确定要删除这个项目吗？此操作无法撤销。',
      };
      
      renderWithProviders(<AlertDialog {...chineseProps} />);
      
      // 检查中文标题和消息
      expect(screen.getByText('删除确认')).toBeInTheDocument();
      expect(screen.getByText('确定要删除这个项目吗？此操作无法撤销。')).toBeInTheDocument();
    });
  });

  describe('交互功能', () => {
    it('点击取消按钮调用onClose', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AlertDialog {...defaultProps} />);
      
      const cancelButton = screen.getByRole('button', { name: '取消' });
      await user.click(cancelButton);
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it('点击确认按钮调用onConfirm和onClose', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AlertDialog {...defaultProps} />);
      
      const confirmButton = screen.getByRole('button', { name: '确认' });
      await user.click(confirmButton);
      
      expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it('当isOpen为false时不显示对话框', () => {
      renderWithProviders(<AlertDialog {...defaultProps} isOpen={false} />);
      
      // 对话框不应该显示
      expect(screen.queryByText('测试对话框')).not.toBeInTheDocument();
      expect(screen.queryByText('这是一个测试消息')).not.toBeInTheDocument();
    });
  });

  describe('删除确认对话框场景', () => {
    it('显示正确的删除确认对话框', () => {
      const deleteProps = {
        ...defaultProps,
        title: '删除图片',
        message: '确定要删除图片"测试图片.jpg"吗？此操作无法撤销。',
        confirmText: '删除',
        cancelText: '取消',
      };
      
      renderWithProviders(<AlertDialog {...deleteProps} />);
      
      // 检查删除确认对话框的内容
      expect(screen.getByText('删除图片')).toBeInTheDocument();
      expect(screen.getByText(/确定要删除图片.*此操作无法撤销/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '删除' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument();
    });
  });
});
