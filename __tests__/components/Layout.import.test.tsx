/**
 * @vitest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Layout from '../../components/Layout';
import { AuthProvider } from '../../contexts/AuthContext';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { CategoryProvider } from '../../contexts/CategoryContext';

// Mock the constants
vi.mock('../../constants', () => ({
  API_BASE_URL: '/',
  IMAGE_BASE_URL: 'http://test.com',
  MAX_CATEGORIES_TO_LOAD_IMAGES_FROM: 2000,
  IS_ELECTRON: true,
  IS_PWA_ENVIRONMENT: false,
  PLATFORM: 'test',
  isElectronEnvironment: () => true,
  isPWAEnvironment: () => false,
  getPlatform: () => 'test',
}));

// Mock the API services
vi.mock('../../services/api', () => ({
  getCategories: vi.fn().mockResolvedValue([]),
}));

// Mock the electron API
const mockElectronAPI = {
  selectImportFolder: vi.fn(),
  validateFolderStructure: vi.fn(),
  importFromFolder: vi.fn(),
  onImportProgress: vi.fn(),
  resetDatabase: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock constants module
vi.mock('../../constants', () => ({
  IS_ELECTRON: true,
  isPWAEnvironment: () => false,
  isElectronEnvironment: () => true,
  getPlatform: () => 'electron',
  PLATFORM: 'electron',
  IS_PWA_ENVIRONMENT: false,
  API_BASE_URL: '/',
  IMAGE_BASE_URL: 'http://39.107.88.124:8000',
  MAX_CATEGORIES_TO_LOAD_IMAGES_FROM: 2000
}));

// Mock window.confirm
Object.defineProperty(window, 'confirm', {
  value: vi.fn(),
  writable: true,
});

// Mock window.alert
Object.defineProperty(window, 'alert', {
  value: vi.fn(),
  writable: true,
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
  writable: true,
});

// Mock window.location.reload
const mockReload = vi.fn();
Object.defineProperty(window, 'location', {
  value: {
    ...window.location,
    reload: mockReload,
  },
  writable: true,
});

// Mock console.log and console.error
const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      <ThemeProvider>
        <CategoryProvider>
          {children}
        </CategoryProvider>
      </ThemeProvider>
    </AuthProvider>
  </BrowserRouter>
);

describe('Layout Import Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockClear();
    consoleErrorSpy.mockClear();
  });

  afterEach(() => {
    cleanup();
  });

  it('should NOT render import button in toolbar after refactor', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    // 验证导入按钮不再存在于顶栏中（已移动到文件菜单）
    const importButton = screen.queryByTitle('从文件夹导入');
    expect(importButton).not.toBeInTheDocument();
  });

  it('should NOT render reset database button in toolbar after refactor', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    // 验证重置数据库按钮不再存在于顶栏中（已移动到文件菜单）
    const resetButton = screen.queryByTitle('重置数据库');
    expect(resetButton).not.toBeInTheDocument();
  });

  it('should render basic layout elements', () => {
    render(
      <TestWrapper>
        <Layout>
          <div>Test Content</div>
        </Layout>
      </TestWrapper>
    );

    // 验证基本的布局元素仍然存在
    expect(screen.getByText('Pokedex')).toBeInTheDocument();
    expect(screen.getByText('Categories')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
});