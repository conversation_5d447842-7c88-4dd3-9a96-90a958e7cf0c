import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import TagPage from '../../components/TagPage';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock deleteTag and searchImagesByTag API
vi.mock('../../services/api', () => ({
  deleteTag: vi.fn(),
  searchImagesByTag: vi.fn()
}));

// Mock AuthContext
vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: '1', username: 'testuser' }
  })
}));

// Mock react-router-dom navigate and params
const mockNavigate = vi.fn();
const mockParams = { tagName: '鸟类摄影' };

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => mockParams
  };
});

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>
  }
}));

import { deleteTag, searchImagesByTag } from '../../services/api';

const mockDeleteTag = deleteTag as any;
const mockSearchImagesByTag = searchImagesByTag as any;

// Mock window.electronAPI
const mockElectronAPI = {
  getTagByName: vi.fn(),
  searchTags: vi.fn()
};

// Set up global mock
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      <ThemeProvider>
        {children}
      </ThemeProvider>
    </AuthProvider>
  </BrowserRouter>
);

describe('TagPage Component - 标签删除功能', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();

    // 设置默认的mock返回值
    mockSearchImagesByTag.mockResolvedValue([]);
    
    // Mock window.electronAPI 方法
    mockElectronAPI.getTagByName.mockResolvedValue({
      id: 'tag-123',
      name: '鸟类摄影',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    });
    
    mockElectronAPI.searchTags.mockResolvedValue([
      {
        id: 'tag-123',
        name: '鸟类摄影',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('持续测试覆盖范围', () => {
    describe('删除操作执行逻辑', () => {
      it('应该成功执行标签删除操作', async () => {
        mockDeleteTag.mockResolvedValue(undefined);

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        // 查找删除按钮
        const deleteButton = screen.getByTestId('delete-tag-button');
        expect(deleteButton).toBeInTheDocument();

        // 点击删除按钮
        fireEvent.click(deleteButton);

        // 验证确认对话框出现
        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        // 确认删除
        const confirmButton = screen.getByTestId('confirm-delete-button');
        fireEvent.click(confirmButton);

        // 验证先获取标签信息，然后使用ID删除
        await waitFor(() => {
          expect(mockElectronAPI.getTagByName).toHaveBeenCalledWith('鸟类摄影');
          expect(mockDeleteTag).toHaveBeenCalledWith('tag-123');
        });

        // 验证导航到标签列表页面
        expect(mockNavigate).toHaveBeenCalledWith('/tags');
      });

      it('应该正确处理删除操作的确认流程', async () => {
        mockDeleteTag.mockResolvedValue(undefined);

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        // 验证确认对话框内容
        const dialog = screen.getByTestId('delete-confirmation-dialog');
        expect(within(dialog).getByText('确认删除标签')).toBeInTheDocument();

        // 验证包含标签名称的文本存在 - 使用部分匹配
        expect(within(dialog).getByText((content, element) => {
          return content.includes('确定要删除标签');
        })).toBeInTheDocument();
        expect(within(dialog).getByText((content, element) => {
          return content.includes('鸟类摄影');
        })).toBeInTheDocument();
        expect(within(dialog).getByText((content, element) => {
          return content.includes('吗？');
        })).toBeInTheDocument();

        expect(within(dialog).getByText('此操作将删除标签以及所有相关的图片关联，且无法撤销。')).toBeInTheDocument();

        // 验证按钮存在
        expect(screen.getByTestId('confirm-delete-button')).toBeInTheDocument();
        expect(screen.getByTestId('cancel-delete-button')).toBeInTheDocument();
      });

      it('应该支持取消删除操作', async () => {
        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        // 点击取消按钮
        const cancelButton = screen.getByTestId('cancel-delete-button');
        fireEvent.click(cancelButton);

        // 验证对话框消失
        await waitFor(() => {
          expect(screen.queryByTestId('delete-confirmation-dialog')).not.toBeInTheDocument();
        });

        // 验证没有调用删除API
        expect(mockDeleteTag).not.toHaveBeenCalled();
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });

    describe('删除后数据状态更新', () => {
      it('应该在删除成功后正确更新界面状态', async () => {
        mockDeleteTag.mockResolvedValue(undefined);

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        const confirmButton = screen.getByTestId('confirm-delete-button');
        fireEvent.click(confirmButton);

        // 验证删除过程中的加载状态
        await waitFor(() => {
          expect(screen.getByTestId('deleting-spinner')).toBeInTheDocument();
        });

        // 验证删除完成后的导航
        await waitFor(() => {
          expect(mockNavigate).toHaveBeenCalledWith('/tags');
        });
      });

      it('应该在删除过程中禁用删除按钮', async () => {
        // 模拟慢速删除操作
        mockDeleteTag.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        const confirmButton = screen.getByTestId('confirm-delete-button');
        fireEvent.click(confirmButton);

        // 验证按钮在删除过程中被禁用
        await waitFor(() => {
          expect(confirmButton).toBeDisabled();
        });
      });
    });

    describe('删除失败错误处理', () => {
      it('应该正确处理删除失败的情况', async () => {
        const errorMessage = 'Failed to delete tag';
        mockDeleteTag.mockRejectedValue(new Error(errorMessage));

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        const confirmButton = screen.getByTestId('confirm-delete-button');
        fireEvent.click(confirmButton);

        // 验证错误消息显示
        await waitFor(() => {
          expect(screen.getByTestId('delete-error-message')).toBeInTheDocument();
        });

        expect(screen.getByText(errorMessage)).toBeInTheDocument();

        // 验证没有导航
        expect(mockNavigate).not.toHaveBeenCalled();
      });

      it('应该在删除失败后允许重试', async () => {
        mockDeleteTag.mockRejectedValueOnce(new Error('Network error'));

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        const confirmButton = screen.getByTestId('confirm-delete-button');
        fireEvent.click(confirmButton);

        // 等待错误显示
        await waitFor(() => {
          expect(screen.getByTestId('delete-error-message')).toBeInTheDocument();
        });

        // 模拟重试成功
        mockDeleteTag.mockResolvedValue(undefined);

        // 再次点击确认按钮重试
        fireEvent.click(confirmButton);

        await waitFor(() => {
          expect(mockNavigate).toHaveBeenCalledWith('/tags');
        });

        expect(mockDeleteTag).toHaveBeenCalledTimes(2);
      });
    });

    describe('权限验证逻辑', () => {
      it('应该验证标签名称的有效性', () => {
        // 测试空标签名称的情况
        vi.mocked(mockParams).tagName = '';

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        // 验证删除按钮不存在或被禁用
        const deleteButton = screen.queryByTestId('delete-tag-button');
        if (deleteButton) {
          expect(deleteButton).toBeDisabled();
        } else {
          expect(deleteButton).not.toBeInTheDocument();
        }
      });

      it('应该正确解码URL编码的标签名称', async () => {
        // 测试URL编码的标签名称
        vi.mocked(mockParams).tagName = '%E9%B8%9F%E7%B1%BB%E6%91%84%E5%BD%B1'; // 鸟类摄影

        mockDeleteTag.mockResolvedValue(undefined);

        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-confirmation-dialog')).toBeInTheDocument();
        });

        const confirmButton = screen.getByTestId('confirm-delete-button');
        fireEvent.click(confirmButton);

        // 验证先获取标签信息，然后使用ID删除
        await waitFor(() => {
          expect(mockElectronAPI.getTagByName).toHaveBeenCalledWith('鸟类摄影');
          expect(mockDeleteTag).toHaveBeenCalledWith('tag-123');
        });
      });
    });
  });

  describe('一次性验证项目（不需要持续测试）', () => {
    beforeEach(() => {
      vi.mocked(mockParams).tagName = '鸟类摄影';
    });

    it('应该正确渲染删除按钮UI', () => {
      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      const deleteButton = screen.getByTestId('delete-tag-button');
      expect(deleteButton).toBeInTheDocument();
      expect(deleteButton).toHaveClass('bg-red-600'); // 验证danger样式
    });

    it('应该正确渲染删除确认对话框', async () => {
      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      const deleteButton = screen.getByTestId('delete-tag-button');
      fireEvent.click(deleteButton);

      await waitFor(() => {
        const dialog = screen.getByTestId('delete-confirmation-dialog');
        expect(dialog).toBeInTheDocument();
        expect(dialog).toHaveClass('fixed'); // 验证modal样式
      });
    });
  });

  describe('主题适配验证', () => {
    const themes = ['modern', 'nature', 'neonGalaxy', 'arcadeFlash', 'retroTechDark'];

    themes.forEach(theme => {
      it(`应该在${theme}主题下正确渲染删除功能`, () => {
        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );

        const deleteButton = screen.getByTestId('delete-tag-button');
        expect(deleteButton).toBeInTheDocument();
        
        // 验证主题相关的样式类存在
        expect(deleteButton).toHaveClass('transition-colors');
      });
    });
  });
});
