import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import LoginPage from '../../components/LoginPage';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock the API functions
vi.mock('../../services/api', () => ({
  sendVerificationCode: vi.fn(),
  verifyCodeAndGetToken: vi.fn(),
}));

// Mock constants to ensure we're in web mode
vi.mock('../../constants', () => ({
  IS_ELECTRON: false,
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        <AuthProvider>
          {component}
        </AuthProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('LoginPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('中文文本显示', () => {
    it('显示正确的中文页面标题', () => {
      renderWithProviders(<LoginPage />);
      
      // 检查页面标题
      expect(screen.getByText('登录 / 注册')).toBeInTheDocument();
    });

    it('显示正确的中文表单标签', () => {
      renderWithProviders(<LoginPage />);
      
      // 检查表单标签
      expect(screen.getByText('邮箱地址')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
    });

    it('显示正确的中文按钮文本', () => {
      renderWithProviders(<LoginPage />);
      
      // 检查获取验证码按钮
      expect(screen.getByRole('button', { name: '获取验证码' })).toBeInTheDocument();
    });

    it('在验证码发送后显示正确的中文界面', async () => {
      const user = userEvent.setup();
      const { sendVerificationCode } = await import('../../services/api');
      vi.mocked(sendVerificationCode).mockResolvedValue(undefined);
      
      renderWithProviders(<LoginPage />);
      
      // 输入邮箱
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      await user.type(emailInput, '<EMAIL>');
      
      // 点击获取验证码
      const getCodeButton = screen.getByRole('button', { name: '获取验证码' });
      await user.click(getCodeButton);
      
      // 等待验证码界面出现
      await screen.findByText('验证码');
      expect(screen.getByText('验证码')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('请输入6位验证码')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument();
      expect(screen.getByText('邮箱输入错误？更换邮箱')).toBeInTheDocument();
    });
  });

  describe('错误信息中文化', () => {
    it('显示正确的邮箱验证错误信息', async () => {
      const user = userEvent.setup();
      renderWithProviders(<LoginPage />);
      
      // 不输入邮箱直接点击获取验证码
      const getCodeButton = screen.getByRole('button', { name: '获取验证码' });
      await user.click(getCodeButton);
      
      // 检查错误信息
      expect(screen.getByText('请输入您的邮箱地址。')).toBeInTheDocument();
    });

    it('显示正确的邮箱格式错误信息', async () => {
      const user = userEvent.setup();
      renderWithProviders(<LoginPage />);
      
      // 输入无效邮箱
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      await user.type(emailInput, 'invalid-email');
      
      const getCodeButton = screen.getByRole('button', { name: '获取验证码' });
      await user.click(getCodeButton);
      
      // 检查错误信息
      expect(screen.getByText('请输入有效的邮箱地址。')).toBeInTheDocument();
    });

    it('显示正确的登录验证错误信息', async () => {
      const user = userEvent.setup();
      const { sendVerificationCode } = await import('../../services/api');
      vi.mocked(sendVerificationCode).mockResolvedValue(undefined);
      
      renderWithProviders(<LoginPage />);
      
      // 先获取验证码
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      await user.type(emailInput, '<EMAIL>');
      
      const getCodeButton = screen.getByRole('button', { name: '获取验证码' });
      await user.click(getCodeButton);
      
      // 等待验证码界面出现
      await screen.findByText('验证码');
      
      // 不输入验证码直接点击登录
      const loginButton = screen.getByRole('button', { name: '登录' });
      await user.click(loginButton);
      
      // 检查错误信息
      expect(screen.getByText('请输入邮箱和验证码。')).toBeInTheDocument();
    });
  });

  describe('成功信息中文化', () => {
    it('显示正确的验证码发送成功信息', async () => {
      const user = userEvent.setup();
      const { sendVerificationCode } = await import('../../services/api');
      vi.mocked(sendVerificationCode).mockResolvedValue(undefined);
      
      renderWithProviders(<LoginPage />);
      
      // 输入邮箱并获取验证码
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      await user.type(emailInput, '<EMAIL>');
      
      const getCodeButton = screen.getByRole('button', { name: '获取验证码' });
      await user.click(getCodeButton);
      
      // 检查成功信息
      await screen.findByText(/验证码已发送至 <EMAIL>/);
      expect(screen.getByText(/请检查您的收件箱（包括垃圾邮件文件夹）/)).toBeInTheDocument();
    });
  });

  describe('交互功能', () => {
    it('更换邮箱功能正常工作', async () => {
      const user = userEvent.setup();
      const { sendVerificationCode } = await import('../../services/api');
      vi.mocked(sendVerificationCode).mockResolvedValue(undefined);
      
      renderWithProviders(<LoginPage />);
      
      // 先获取验证码
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      await user.type(emailInput, '<EMAIL>');
      
      const getCodeButton = screen.getByRole('button', { name: '获取验证码' });
      await user.click(getCodeButton);
      
      // 等待验证码界面出现
      await screen.findByText('验证码');
      
      // 点击更换邮箱
      const changeEmailButton = screen.getByText('邮箱输入错误？更换邮箱');
      await user.click(changeEmailButton);
      
      // 验证回到初始状态
      expect(screen.getByRole('button', { name: '获取验证码' })).toBeInTheDocument();
      expect(screen.queryByText('验证码')).not.toBeInTheDocument();
    });
  });
});
