import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import Layout from '../../components/Layout';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock constants
vi.mock('../../constants', () => ({
  IS_ELECTRON: false,
  isPWAEnvironment: () => false,
}));

// Mock the electron API
const mockElectronAPI = {
  isElectron: false,
  onMenuAction: vi.fn(),
  removeAllListeners: vi.fn(),
};

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        <AuthProvider>
          {component}
        </AuthProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('Layout 中文文本显示', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('导航栏中文文本', () => {
    it('显示正确的中文导航链接', () => {
      renderWithProviders(<Layout>Test Content</Layout>);

      // 检查导航链接
      expect(screen.getByText('分类')).toBeInTheDocument();
      expect(screen.getByText('统计')).toBeInTheDocument();
    });

    it('显示正确的主题选择器aria-label', () => {
      renderWithProviders(<Layout>Test Content</Layout>);

      // 查找主题选择器按钮
      const themeSwitcher = screen.getByLabelText('选择主题或安装应用');
      expect(themeSwitcher).toBeInTheDocument();
    });
  });

  describe('内容显示', () => {
    it('正确显示子组件内容', () => {
      renderWithProviders(<Layout><div>测试内容</div></Layout>);
      
      // 检查子组件内容
      expect(screen.getByText('测试内容')).toBeInTheDocument();
    });

    it('显示品牌名称', () => {
      renderWithProviders(<Layout>Test Content</Layout>);
      
      // 检查品牌名称
      expect(screen.getByText('Pokedex')).toBeInTheDocument();
    });

    it('显示版权信息', () => {
      renderWithProviders(<Layout>Test Content</Layout>);
      
      // 检查版权信息
      expect(screen.getByText(/Made With/)).toBeInTheDocument();
      expect(screen.getByText(/By 黄不盈/)).toBeInTheDocument();
    });
  });
});
