import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import BirdDataUploadButton from '../../components/BirdDataUploadButton';

// Mock dependencies
vi.mock('../../services/bird-sighting-api', () => ({
  selectAndUploadBirdSightingFile: vi.fn(),
  getBirdSightingDataStatus: vi.fn(),
}));

vi.mock('../../constants', () => ({
  IS_ELECTRON: true,
}));

// Mock theme context
vi.mock('../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      button: {
        primary: 'bg-blue-600 hover:bg-blue-700',
        primaryText: 'text-white',
        secondary: 'bg-gray-200 hover:bg-gray-300',
        secondaryText: 'text-gray-700',
        transition: 'transition-all duration-200',
      },
      card: {
        rounded: 'rounded-lg',
      },
      text: 'text-gray-900',
    },
  }),
}));

import * as birdSightingApi from '../../services/bird-sighting-api';

describe('BirdDataUploadButton', () => {
  const user = userEvent.setup();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementation
    (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValue({
      success: true,
      data: { importedCount: 5, totalCount: 5 }
    });
    
    (birdSightingApi.getBirdSightingDataStatus as any).mockResolvedValue({
      hasData: false,
      suggestions: ['请上传观鸟数据']
    });
    
    // Mock console methods
    console.log = vi.fn();
    console.error = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('基础渲染', () => {
    it('应该正确渲染上传按钮', () => {
      render(<BirdDataUploadButton onUploadComplete={vi.fn()} />);
      
      const uploadButton = screen.getByRole('button', { name: /上传观鸟数据/i });
      expect(uploadButton).toBeInTheDocument();
    });

    it('应该显示上传图标', () => {
      render(<BirdDataUploadButton onUploadComplete={vi.fn()} />);
      
      // 检查是否有上传相关的图标或文本
      expect(screen.getByRole('button')).toHaveTextContent(/上传/);
    });

    it('应该应用正确的主题样式', () => {
      render(<BirdDataUploadButton onUploadComplete={vi.fn()} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-blue-600');
      expect(button).toHaveClass('text-white');
      expect(button).toHaveClass('transition-all');
    });
  });

  describe('文件上传交互', () => {
    it('应该在点击时触发文件选择', async () => {
      const mockOnUploadComplete = vi.fn();
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      expect(birdSightingApi.selectAndUploadBirdSightingFile).toHaveBeenCalledTimes(1);
    });

    it('应该在上传过程中显示加载状态', async () => {
      // Mock a delayed response
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: { importedCount: 3, totalCount: 3 }
        }), 100))
      );

      const mockOnUploadComplete = vi.fn();
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      // 检查加载状态
      expect(uploadButton).toBeDisabled();
      expect(screen.getByText(/上传中/i)).toBeInTheDocument();
      
      // 等待上传完成
      await waitFor(() => {
        expect(uploadButton).not.toBeDisabled();
      }, { timeout: 200 });
    });

    it('应该在上传成功时调用回调函数', async () => {
      const mockOnUploadComplete = vi.fn();
      const mockResult = {
        success: true,
        data: { importedCount: 5, totalCount: 5 }
      };
      
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValue(mockResult);
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      await waitFor(() => {
        expect(mockOnUploadComplete).toHaveBeenCalledWith(mockResult);
      });
    });

    it('应该在用户取消时不调用回调函数', async () => {
      const mockOnUploadComplete = vi.fn();
      const mockResult = {
        success: false,
        error: '用户取消文件选择'
      };
      
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValue(mockResult);
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      await waitFor(() => {
        expect(mockOnUploadComplete).not.toHaveBeenCalled();
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理上传失败的情况', async () => {
      const mockOnUploadComplete = vi.fn();
      const mockError = {
        success: false,
        error: '文件格式不正确'
      };
      
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValue(mockError);
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /上传失败/i })).toBeInTheDocument();
        expect(screen.getByText(/文件格式不正确/i)).toBeInTheDocument();
      });
    });

    it('应该处理API调用异常', async () => {
      const mockOnUploadComplete = vi.fn();
      const mockError = new Error('网络连接失败');
      
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockRejectedValue(mockError);
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /上传失败/i })).toBeInTheDocument();
        expect(screen.getByText(/网络连接失败/i)).toBeInTheDocument();
      });
    });

    it('应该在错误发生后允许重新上传', async () => {
      const mockOnUploadComplete = vi.fn();
      
      // 第一次调用失败
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValueOnce({
        success: false,
        error: '文件格式错误'
      });
      
      // 第二次调用成功
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValueOnce({
        success: true,
        data: { importedCount: 3, totalCount: 3 }
      });
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      
      // 第一次上传失败
      await user.click(uploadButton);
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /上传失败/i })).toBeInTheDocument();
      });
      
      // 第二次上传成功
      await user.click(uploadButton);
      await waitFor(() => {
        expect(mockOnUploadComplete).toHaveBeenCalledWith({
          success: true,
          data: { importedCount: 3, totalCount: 3 }
        });
      });
    });
  });

  describe('成功状态反馈', () => {
    it('应该显示上传成功消息', async () => {
      const mockOnUploadComplete = vi.fn();
      const mockResult = {
        success: true,
        data: { importedCount: 8, totalCount: 8 }
      };
      
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValue(mockResult);
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /上传成功/i })).toBeInTheDocument();
        expect(screen.getByText(/8.*条记录/i)).toBeInTheDocument();
      });
    });

    it('应该在成功后短暂显示成功状态然后重置', async () => {
      vi.useFakeTimers();
      
      const mockOnUploadComplete = vi.fn();
      const mockResult = {
        success: true,
        data: { importedCount: 5, totalCount: 5 }
      };
      
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockResolvedValue(mockResult);
      
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      // 等待成功状态显示
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /上传成功/i })).toBeInTheDocument();
      });
      
      // 快进时间，检查状态重置
      vi.advanceTimersByTime(3000);
      
      await waitFor(() => {
        expect(screen.queryByRole('button', { name: /上传成功/i })).not.toBeInTheDocument();
        expect(screen.getByRole('button', { name: /上传观鸟数据/i })).toBeInTheDocument();
      }, { timeout: 1000 });
      
      vi.useRealTimers();
    }, 15000);
  });

  describe('可访问性和用户体验', () => {
    it('应该有正确的ARIA属性', () => {
      render(<BirdDataUploadButton onUploadComplete={vi.fn()} />);
      
      const uploadButton = screen.getByRole('button');
      expect(uploadButton).toHaveAttribute('type', 'button');
      expect(uploadButton).not.toHaveAttribute('aria-disabled', 'true');
    });

    it('应该在加载时设置正确的ARIA属性', async () => {
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: { importedCount: 3, totalCount: 3 }
        }), 100))
      );

      render(<BirdDataUploadButton onUploadComplete={vi.fn()} />);
      
      const uploadButton = screen.getByRole('button');
      await user.click(uploadButton);
      
      expect(uploadButton).toBeDisabled();
      expect(uploadButton).toHaveAttribute('aria-disabled', 'true');
    });

    it('应该支持键盘导航', async () => {
      const mockOnUploadComplete = vi.fn();
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      
      // 使用Tab键聚焦
      await user.tab();
      expect(uploadButton).toHaveFocus();
      
      // 使用Enter键激活
      await user.keyboard('{Enter}');
      expect(birdSightingApi.selectAndUploadBirdSightingFile).toHaveBeenCalledTimes(1);
    });

    it('应该支持空格键激活', async () => {
      const mockOnUploadComplete = vi.fn();
      render(<BirdDataUploadButton onUploadComplete={mockOnUploadComplete} />);
      
      const uploadButton = screen.getByRole('button');
      
      // 聚焦按钮
      uploadButton.focus();
      
      // 使用空格键激活
      await user.keyboard('{ }');
      expect(birdSightingApi.selectAndUploadBirdSightingFile).toHaveBeenCalledTimes(1);
    });
  });

  describe('Web环境兼容性', () => {
    beforeEach(() => {
      // Mock Web environment
      vi.doMock('../../constants', () => ({
        IS_ELECTRON: false,
      }));
    });

    it('应该在Web环境中显示不支持提示', () => {
      // Re-mock for this test
      (birdSightingApi.selectAndUploadBirdSightingFile as any).mockRejectedValue(
        new Error('Web模式暂不支持观鸟数据上传功能')
      );

      render(<BirdDataUploadButton onUploadComplete={vi.fn()} />);
      
      const uploadButton = screen.getByRole('button');
      expect(uploadButton).toBeInTheDocument();
      
      // 应该有提示文本或禁用状态
      expect(uploadButton).toBeEnabled(); // 按钮仍然可点击，但会显示错误
    });
  });

  describe('组件属性', () => {
    it('应该接受自定义类名', () => {
      render(
        <BirdDataUploadButton 
          onUploadComplete={vi.fn()}
          className="custom-class"
        />
      );
      
      const uploadButton = screen.getByRole('button');
      expect(uploadButton).toHaveClass('custom-class');
    });

    it('应该接受disabled属性', () => {
      render(
        <BirdDataUploadButton 
          onUploadComplete={vi.fn()}
          disabled={true}
        />
      );
      
      const uploadButton = screen.getByRole('button');
      expect(uploadButton).toBeDisabled();
    });

    it('应该接受size属性', () => {
      render(
        <BirdDataUploadButton 
          onUploadComplete={vi.fn()}
          size="small"
        />
      );
      
      const uploadButton = screen.getByRole('button');
      // 检查是否应用了对应的尺寸样式
      expect(uploadButton).toHaveClass('px-3', 'py-2');
    });
  });
});