/**
 * @vitest-environment jsdom
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import CategorySuggestions from '../../components/CategorySuggestions';

// Mock dependencies
vi.mock('../../services/species', () => ({
  searchSpecies: vi.fn(),
}));

vi.mock('../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      card: {
        text: 'text-gray-900',
        bg: 'bg-white',
        border: 'border-gray-300',
        rounded: 'rounded-lg',
      },
      input: {
        bg: 'bg-white',
        border: 'border-gray-300',
        focusRing: 'focus:ring-blue-500',
        text: 'text-gray-900',
        placeholderText: 'placeholder-gray-500',
      },
    },
  }),
}));

describe('CategorySuggestions', () => {
  const mockOnChange = vi.fn();
  const mockOnSelect = vi.fn();
  
  const defaultProps = {
    value: '',
    onChange: mockOnChange,
    onSelect: mockOnSelect,
    placeholder: '请输入分类名称',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders input field with placeholder', () => {
    render(<CategorySuggestions {...defaultProps} />);

    const input = screen.getByPlaceholderText('请输入分类名称');
    expect(input).toBeInTheDocument();
  });

  it('renders with initial value', () => {
    render(<CategorySuggestions {...defaultProps} value="喜鹊" />);
    
    const input = screen.getByDisplayValue('喜鹊');
    expect(input).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(<CategorySuggestions {...defaultProps} disabled />);

    const input = screen.getByPlaceholderText('请输入分类名称');
    expect(input).toBeDisabled();
  });
});