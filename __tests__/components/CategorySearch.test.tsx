import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import CategorySearch from '../../components/CategorySearch';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { CategoryProvider } from '../../contexts/CategoryContext';

// Mock the electron API
const mockElectronAPI = {
  getCategories: vi.fn(),
};

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock API functions
vi.mock('../../services/api', () => ({
  getAllTags: vi.fn(),
  getCategoryByName: vi.fn(),
  searchImagesByTag: vi.fn(),
}));

const defaultProps = {
  isExpanded: true,
  onFocus: vi.fn(),
  onBlur: vi.fn(),
};

const mockCategories = [
  { id: '1', name: '测试分类', description: '测试描述', created_at: '2024-01-01', updated_at: '2024-01-01' }
];

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      <ThemeProvider>
        <CategoryProvider>
          {component}
        </CategoryProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
};

describe('CategorySearch', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockElectronAPI.getCategories.mockResolvedValue(mockCategories);
  });

  describe('中文文本显示', () => {
    it('显示正确的中文搜索占位符', () => {
      renderWithProviders(<CategorySearch {...defaultProps} />);
      
      // 检查搜索框占位符
      const searchInput = screen.getByPlaceholderText('搜索分类或图片标签...');
      expect(searchInput).toBeInTheDocument();
    });

    it('显示正确的中文aria-label', () => {
      renderWithProviders(<CategorySearch {...defaultProps} />);
      
      // 检查搜索框的aria-label
      const searchInput = screen.getByLabelText('搜索分类或按图片标签搜索');
      expect(searchInput).toBeInTheDocument();
    });

    it('显示正确的清除按钮aria-label', async () => {
      const user = userEvent.setup();
      renderWithProviders(<CategorySearch {...defaultProps} />);
      
      // 输入一些文本以显示清除按钮
      const searchInput = screen.getByPlaceholderText('搜索分类或图片标签...');
      await user.type(searchInput, '测试');
      
      // 检查清除按钮的aria-label
      const clearButton = screen.getByLabelText('清除搜索');
      expect(clearButton).toBeInTheDocument();
    });

    it('清除按钮功能正常', async () => {
      const user = userEvent.setup();
      renderWithProviders(<CategorySearch {...defaultProps} />);
      
      // 输入一些文本
      const searchInput = screen.getByPlaceholderText('搜索分类或图片标签...');
      await user.type(searchInput, '测试');
      expect(searchInput).toHaveValue('测试');
      
      // 点击清除按钮
      const clearButton = screen.getByLabelText('清除搜索');
      await user.click(clearButton);
      
      // 验证输入框被清空
      expect(searchInput).toHaveValue('');
    });
  });

  describe('搜索功能', () => {
    it('可以输入中文搜索内容', async () => {
      const user = userEvent.setup();
      renderWithProviders(<CategorySearch {...defaultProps} />);
      
      const searchInput = screen.getByPlaceholderText('搜索分类或图片标签...');
      await user.type(searchInput, '鸟类');
      
      expect(searchInput).toHaveValue('鸟类');
    });

    it('支持中文字符的搜索', async () => {
      const user = userEvent.setup();
      renderWithProviders(<CategorySearch {...defaultProps} />);
      
      const searchInput = screen.getByPlaceholderText('搜索分类或图片标签...');
      
      // 测试各种中文字符
      await user.type(searchInput, '喜鹊');
      expect(searchInput).toHaveValue('喜鹊');
      
      await user.clear(searchInput);
      await user.type(searchInput, '动物标签');
      expect(searchInput).toHaveValue('动物标签');
    });
  });
});
