import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import CategoryForm from '../../components/CategoryForm';
import { ThemeProvider } from '../../contexts/ThemeContext';

// Mock dependencies
vi.mock('../../services/species', () => ({
  searchSpecies: vi.fn(),
}));

vi.mock('../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      card: {
        text: 'text-gray-900',
        bg: 'bg-white',
        border: 'border-gray-300',
        rounded: 'rounded-lg',
      },
      input: {
        bg: 'bg-white',
        border: 'border-gray-300',
        focusRing: 'focus:ring-blue-500',
        text: 'text-gray-900',
        placeholderText: 'placeholder-gray-500',
      },
      button: {
        primary: 'bg-blue-600',
        primaryText: 'text-white',
        secondary: 'bg-gray-200',
        secondaryText: 'text-gray-700',
      },
    },
  }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('CategoryForm Enhanced', () => {
  const mockOnSubmit = vi.fn();
  const mockOnCancel = vi.fn();
  
  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    isLoading: false,
    error: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Form Functionality', () => {
    it('renders form with category name input', () => {
      render(<CategoryForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/分类名称/i);
      expect(nameInput).toBeInTheDocument();
      expect(nameInput).toBeRequired();
    });

    it('renders form with description input', () => {
      render(<CategoryForm {...defaultProps} />);

      const descInput = screen.getByLabelText(/描述/i);
      expect(descInput).toBeInTheDocument();
      expect(descInput).not.toBeRequired();
    });

    it('renders submit and cancel buttons', () => {
      render(<CategoryForm {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: /创建分类/i });
      const cancelButton = screen.getByRole('button', { name: /取消/i });

      expect(submitButton).toBeInTheDocument();
      expect(cancelButton).toBeInTheDocument();
    });

    it('calls onSubmit when form is submitted', async () => {
      const user = userEvent.setup();
      render(<CategoryForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/分类名称/i);
      const submitButton = screen.getByRole('button', { name: /创建分类/i });

      await user.type(nameInput, '喜鹊');
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: '喜鹊',
        description: null,
      });
    });

    it('calls onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<CategoryForm {...defaultProps} />);

      const cancelButton = screen.getByRole('button', { name: /取消/i });
      await user.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('shows loading state when isLoading is true', () => {
      render(<CategoryForm {...defaultProps} isLoading={true} />);

      const submitButton = screen.getByRole('button', { name: /创建中.../i });
      expect(submitButton).toBeDisabled();
    });

    it('displays error when error prop is provided', () => {
      const error = 'Test error message';
      render(<CategoryForm {...defaultProps} error={error} />);
      
      expect(screen.getByText(error)).toBeInTheDocument();
    });
  });

  describe('Edit Mode', () => {
    const initialData = {
      id: '1',
      name: '喜鹊',
      description: '黑白相间的鸟类',
      thumbnail_path: null,
      thumbnail_url: null,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    };

    it('populates form with initial data', () => {
      render(<CategoryForm {...defaultProps} initialData={initialData} />);
      
      const nameInput = screen.getByDisplayValue('喜鹊');
      const descInput = screen.getByDisplayValue('黑白相间的鸟类');
      
      expect(nameInput).toBeInTheDocument();
      expect(descInput).toBeInTheDocument();
    });

    it('shows update button in edit mode', () => {
      render(<CategoryForm {...defaultProps} initialData={initialData} />);

      const updateButton = screen.getByRole('button', { name: /更新分类/i });
      expect(updateButton).toBeInTheDocument();
    });

    it('shows updating state when loading in edit mode', () => {
      render(<CategoryForm {...defaultProps} initialData={initialData} isLoading={true} />);

      const updateButton = screen.getByRole('button', { name: /更新中.../i });
      expect(updateButton).toBeDisabled();
    });
  });

  describe('Form Validation', () => {
    it('prevents submission when name is empty', async () => {
      const user = userEvent.setup();
      render(<CategoryForm {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: /创建分类/i });
      await user.click(submitButton);

      // HTML5 validation should prevent submission
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('trims whitespace from name input', async () => {
      const user = userEvent.setup();
      render(<CategoryForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/分类名称/i);
      const submitButton = screen.getByRole('button', { name: /创建分类/i });

      await user.type(nameInput, '  喜鹊  ');
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: '  喜鹊  ', // Component doesn't trim - handled by backend
        description: null,
      });
    });

    it('handles empty description as null', async () => {
      const user = userEvent.setup();
      render(<CategoryForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/分类名称/i);
      const descInput = screen.getByLabelText(/描述/i);
      const submitButton = screen.getByRole('button', { name: /创建分类/i });

      await user.type(nameInput, '喜鹊');
      await user.type(descInput, '   '); // Only whitespace
      await user.clear(descInput);
      await user.click(submitButton);

      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: '喜鹊',
        description: null,
      });
    });
  });

  describe('Input Constraints', () => {
    it('respects maxLength constraint for name input', () => {
      render(<CategoryForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/分类名称/i) as HTMLInputElement;
      expect(nameInput.maxLength).toBe(50);
    });

    it('respects maxLength constraint for description input', () => {
      render(<CategoryForm {...defaultProps} />);

      const descInput = screen.getByLabelText(/描述/i) as HTMLTextAreaElement;
      expect(descInput.maxLength).toBe(300);
    });
  });

  describe('Form Reset', () => {
    it('resets form when initialData changes from existing to null', () => {
      const initialData = {
        id: '1',
        name: '喜鹊',
        description: '黑白相间的鸟类',
        thumbnail_path: null,
        thumbnail_url: null,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const { rerender } = render(<CategoryForm {...defaultProps} initialData={initialData} />);
      
      // Initially populated
      expect(screen.getByDisplayValue('喜鹊')).toBeInTheDocument();
      
      // Change to no initial data
      rerender(<CategoryForm {...defaultProps} initialData={undefined} />);
      
      // Should be empty
      const nameInput = screen.getByLabelText(/分类名称/i) as HTMLInputElement;
      const descInput = screen.getByLabelText(/描述/i) as HTMLTextAreaElement;
      
      expect(nameInput.value).toBe('');
      expect(descInput.value).toBe('');
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels', () => {
      render(<CategoryForm {...defaultProps} />);

      const nameLabel = screen.getByText(/分类名称/i);
      const descLabel = screen.getByText(/描述/i);

      expect(nameLabel).toBeInTheDocument();
      expect(descLabel).toBeInTheDocument();
    });

    it('marks required fields with asterisk', () => {
      render(<CategoryForm {...defaultProps} />);
      
      const requiredAsterisk = screen.getByText('*');
      expect(requiredAsterisk).toBeInTheDocument();
    });

    it('has proper form structure', () => {
      render(<CategoryForm {...defaultProps} />);
      
      const form = document.querySelector('form');
      expect(form).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive classes', () => {
      render(<CategoryForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/分类名称/i);
      expect(nameInput).toHaveClass('text-sm', 'sm:text-base');
    });

    it('applies responsive button layout', () => {
      render(<CategoryForm {...defaultProps} />);

      const buttonContainer = screen.getByRole('button', { name: /创建分类/i }).parentElement;
      expect(buttonContainer).toHaveClass('flex', 'flex-col-reverse', 'sm:flex-row');
    });
  });
});