import { beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';
import { setupElectronMocks, cleanupElectronMocks } from '../electron/helpers/electron-mocks';
import { TestDatabaseManager } from '../electron/helpers/test-database';
import '@testing-library/jest-dom';

// 全局测试设置
beforeAll(() => {
  // 设置Electron mocks
  setupElectronMocks();
  
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  
  // Mock window.matchMedia (only in browser-like environments)
  if (typeof window !== 'undefined') {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });
    
    // Mock URL.createObjectURL for file preview functionality
    Object.defineProperty(window.URL, 'createObjectURL', {
      writable: true,
      value: vi.fn().mockImplementation(() => 'blob:mock-url'),
    });
    
    Object.defineProperty(window.URL, 'revokeObjectURL', {
      writable: true,
      value: vi.fn(),
    });
  }
  
  // 静默console.log输出以减少测试噪音（在组件测试中会被spy覆盖）
  // console.log = () => {};
});

afterAll(() => {
  // 清理所有测试数据库
  TestDatabaseManager.getInstance().cleanupAll();
  
  // 清理Electron mocks
  cleanupElectronMocks();
});

// 每个测试前的清理
beforeEach(() => {
  // 清理所有mock的调用历史
  vi.clearAllMocks();
});

afterEach(() => {
  // 清理DOM (only in browser-like environments)
  if (typeof document !== 'undefined') {
    document.body.innerHTML = '';
  }
});

// 全局测试工具函数
declare global {
  var testUtils: {
    delay: (ms: number) => Promise<void>;
    expectAsync: <T>(fn: () => Promise<T>) => Promise<T>;
  };
}

globalThis.testUtils = {
  // 延迟工具函数
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 异步期望工具函数
  expectAsync: async <T>(fn: () => Promise<T>): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      throw error;
    }
  }
};