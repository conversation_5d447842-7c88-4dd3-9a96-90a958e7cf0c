import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as birdSightingApi from '../../services/bird-sighting-api';

// Mock constants
vi.mock('../../constants', () => ({
  IS_ELECTRON: true,
  API_BASE_URL: 'http://localhost:3000'
}));

// Mock api-validation module
vi.mock('../../services/api-validation', () => ({
  safeParseApiResponse: vi.fn((data, schema, errorMsg) => data),
  formatApiErrorWithZod: vi.fn((error, url) => {
    throw error;
  })
}));

// Mock window.electronAPI
const mockElectronAPI = {
  uploadBirdSightingData: vi.fn(),
  getBirdSightingData: vi.fn(),
  getBirdSightingStats: vi.fn(),
  hasBirdSightingData: vi.fn(),
  clearBirdSightingData: vi.fn(),
  showOpenDialog: vi.fn()
};

// Setup global window mock
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true
});

Object.defineProperty(window, 'confirm', {
  value: vi.fn(),
  writable: true
});

describe('Bird Sighting API', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
    
    // Setup console mocks
    console.log = vi.fn();
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('uploadBirdSightingData', () => {
    it('应该成功上传Excel文件', async () => {
      const mockResult = {
        success: true,
        data: {
          importedCount: 5,
          totalCount: 5
        }
      };

      mockElectronAPI.uploadBirdSightingData.mockResolvedValue(mockResult);

      const result = await birdSightingApi.uploadBirdSightingData('/test/file.xlsx');

      expect(result).toEqual(mockResult);
      expect(mockElectronAPI.uploadBirdSightingData).toHaveBeenCalledWith('/test/file.xlsx');
      expect(console.log).toHaveBeenCalledWith('✅ API: 使用本地Electron API上传观鸟数据');
    });

    it('应该处理上传失败的情况', async () => {
      const mockError = {
        success: false,
        error: '文件格式错误'
      };

      mockElectronAPI.uploadBirdSightingData.mockResolvedValue(mockError);

      const result = await birdSightingApi.uploadBirdSightingData('/test/invalid.xlsx');

      expect(result.success).toBe(false);
      expect(result.error).toBe('文件格式错误');
      expect(console.error).toHaveBeenCalledWith('❌ API: 观鸟数据上传失败:', '文件格式错误');
    });

    it('应该处理API调用异常', async () => {
      const mockError = new Error('网络连接失败');
      mockElectronAPI.uploadBirdSightingData.mockRejectedValue(mockError);

      await expect(birdSightingApi.uploadBirdSightingData('/test/file.xlsx'))
        .rejects.toThrow('网络连接失败');
    });
  });

  describe('getBirdSightingData', () => {
    it('应该成功获取观鸟数据', async () => {
      const mockData = [
        {
          id: '4004',
          chineseName: '白眉山鹧鸪',
          latinName: 'Arborophila gingica',
          englishName: "Rickett's Hill Partridge",
          order: '鸡形目',
          family: '雉科',
          count: 5,
          recordDate: '2025/3/22'
        }
      ];

      const mockResponse = {
        success: true,
        data: mockData
      };

      mockElectronAPI.getBirdSightingData.mockResolvedValue(mockResponse);

      const result = await birdSightingApi.getBirdSightingData();

      expect(result).toEqual(mockData);
      expect(mockElectronAPI.getBirdSightingData).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('✅ API: 使用本地Electron API获取观鸟数据');
    });

    it('应该处理获取数据失败的情况', async () => {
      const mockResponse = {
        success: false,
        error: '数据库连接失败'
      };

      mockElectronAPI.getBirdSightingData.mockResolvedValue(mockResponse);

      await expect(birdSightingApi.getBirdSightingData())
        .rejects.toThrow('数据库连接失败');
    });

    it('应该处理空数据的情况', async () => {
      const mockResponse = {
        success: true,
        data: []
      };

      mockElectronAPI.getBirdSightingData.mockResolvedValue(mockResponse);

      const result = await birdSightingApi.getBirdSightingData();

      expect(result).toEqual([]);
      expect(console.log).toHaveBeenCalledWith('📊 API: 获取观鸟数据成功，共 0 条记录');
    });
  });

  describe('getBirdSightingStats', () => {
    it('应该成功获取统计信息', async () => {
      const mockStats = {
        totalRecords: 10,
        totalSpecies: 8,
        totalObservations: 25,
        latestRecord: '2025/3/22'
      };

      const mockResponse = {
        success: true,
        data: mockStats
      };

      mockElectronAPI.getBirdSightingStats.mockResolvedValue(mockResponse);

      const result = await birdSightingApi.getBirdSightingStats();

      expect(result).toEqual(mockStats);
      expect(mockElectronAPI.getBirdSightingStats).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('✅ API: 使用本地Electron API获取观鸟统计');
    });

    it('应该处理获取统计失败的情况', async () => {
      const mockResponse = {
        success: false,
        error: '统计查询失败'
      };

      mockElectronAPI.getBirdSightingStats.mockResolvedValue(mockResponse);

      await expect(birdSightingApi.getBirdSightingStats())
        .rejects.toThrow('统计查询失败');
    });
  });

  describe('hasBirdSightingData', () => {
    it('应该正确检查数据存在状态', async () => {
      mockElectronAPI.hasBirdSightingData.mockResolvedValue(true);

      const result = await birdSightingApi.hasBirdSightingData();

      expect(result).toBe(true);
      expect(mockElectronAPI.hasBirdSightingData).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('✅ API: 使用本地Electron API检查观鸟数据状态');
    });

    it('应该正确检查数据不存在状态', async () => {
      mockElectronAPI.hasBirdSightingData.mockResolvedValue(false);

      const result = await birdSightingApi.hasBirdSightingData();

      expect(result).toBe(false);
      expect(console.log).toHaveBeenCalledWith('📊 API: 观鸟数据状态检查结果: 无数据');
    });

    it('应该处理检查失败的情况', async () => {
      mockElectronAPI.hasBirdSightingData.mockRejectedValue(new Error('检查失败'));

      const result = await birdSightingApi.hasBirdSightingData();

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith('❌ API:', '检查观鸟数据状态失败: 检查失败');
    });
  });

  describe('clearBirdSightingData', () => {
    it('应该在用户确认后成功清空数据', async () => {
      const mockResult = {
        success: true,
        data: { message: '数据已清空' }
      };

      (window.confirm as any).mockReturnValue(true);
      mockElectronAPI.clearBirdSightingData.mockResolvedValue(mockResult);

      const result = await birdSightingApi.clearBirdSightingData();

      expect(result).toEqual(mockResult);
      expect(window.confirm).toHaveBeenCalledWith('确定要清空所有观鸟数据吗？此操作不可撤销。');
      expect(mockElectronAPI.clearBirdSightingData).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('✅ API: 使用本地Electron API清空观鸟数据');
    });

    it('应该在用户取消时返回取消状态', async () => {
      (window.confirm as any).mockReturnValue(false);

      const result = await birdSightingApi.clearBirdSightingData();

      expect(result.success).toBe(false);
      expect(result.error).toBe('用户取消操作');
      expect(mockElectronAPI.clearBirdSightingData).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('🚫 API: 用户取消清空观鸟数据操作');
    });

    it('应该处理清空失败的情况', async () => {
      const mockResult = {
        success: false,
        error: '清空数据失败'
      };

      (window.confirm as any).mockReturnValue(true);
      mockElectronAPI.clearBirdSightingData.mockResolvedValue(mockResult);

      const result = await birdSightingApi.clearBirdSightingData();

      expect(result.success).toBe(false);
      expect(result.error).toBe('清空数据失败');
      expect(console.error).toHaveBeenCalledWith('❌ API: 观鸟数据清空失败:', '清空数据失败');
    });
  });

  describe('selectAndUploadBirdSightingFile', () => {
    it('应该成功选择文件并上传', async () => {
      const mockDialogResult = {
        canceled: false,
        filePaths: ['/test/selected.xlsx']
      };

      const mockUploadResult = {
        success: true,
        data: {
          importedCount: 3,
          totalCount: 3
        }
      };

      mockElectronAPI.showOpenDialog.mockResolvedValue(mockDialogResult);
      mockElectronAPI.uploadBirdSightingData.mockResolvedValue(mockUploadResult);

      const result = await birdSightingApi.selectAndUploadBirdSightingFile();

      expect(result).toEqual(mockUploadResult);
      expect(mockElectronAPI.showOpenDialog).toHaveBeenCalledWith({
        title: '选择观鸟数据Excel文件',
        filters: [
          { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      });
      expect(mockElectronAPI.uploadBirdSightingData).toHaveBeenCalledWith('/test/selected.xlsx');
    });

    it('应该处理用户取消文件选择', async () => {
      const mockDialogResult = {
        canceled: true,
        filePaths: []
      };

      mockElectronAPI.showOpenDialog.mockResolvedValue(mockDialogResult);

      const result = await birdSightingApi.selectAndUploadBirdSightingFile();

      expect(result.success).toBe(false);
      expect(result.error).toBe('用户取消文件选择');
      expect(mockElectronAPI.uploadBirdSightingData).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('🚫 API: 用户取消文件选择');
    });

    it('应该处理文件对话框异常', async () => {
      mockElectronAPI.showOpenDialog.mockRejectedValue(new Error('对话框打开失败'));

      await expect(birdSightingApi.selectAndUploadBirdSightingFile())
        .rejects.toThrow('对话框打开失败');
    });
  });

  describe('getBirdSightingDataStatus', () => {
    it('应该返回有数据时的完整状态', async () => {
      const mockStats = {
        totalRecords: 10,
        totalSpecies: 8,
        totalObservations: 25,
        latestRecord: '2025/3/22'
      };

      mockElectronAPI.hasBirdSightingData.mockResolvedValue(true);
      mockElectronAPI.getBirdSightingStats.mockResolvedValue({
        success: true,
        data: mockStats
      });

      const result = await birdSightingApi.getBirdSightingDataStatus();

      expect(result.hasData).toBe(true);
      expect(result.stats).toEqual(mockStats);
      expect(result.suggestions).toContain('数据已加载，可以查看统计信息和时间线图表');
      expect(result.suggestions).toContain('当前共有 10 条观测记录，涵盖 8 个物种');
      expect(result.suggestions).toContain('可以上传新的Excel文件来更新数据（将覆盖现有数据）');
    });

    it('应该返回无数据时的状态', async () => {
      mockElectronAPI.hasBirdSightingData.mockResolvedValue(false);

      const result = await birdSightingApi.getBirdSightingDataStatus();

      expect(result.hasData).toBe(false);
      expect(result.stats).toBeUndefined();
      expect(result.suggestions).toContain('当前没有观鸟数据，点击"上传数据"按钮导入Excel文件');
      expect(result.suggestions).toContain('Excel文件应包含鸟种编号、中文名、拉丁名、英文名、目、科、记录次数、记录时间等字段');
    });

    it('应该处理获取统计信息失败的情况', async () => {
      mockElectronAPI.hasBirdSightingData.mockResolvedValue(true);
      mockElectronAPI.getBirdSightingStats.mockRejectedValue(new Error('统计获取失败'));

      const result = await birdSightingApi.getBirdSightingDataStatus();

      expect(result.hasData).toBe(true);
      expect(result.stats).toBeUndefined();
      expect(result.suggestions).toContain('数据存在但获取统计信息失败，建议检查数据完整性');
      expect(console.warn).toHaveBeenCalledWith('⚠️ API: 获取统计信息失败:', expect.any(Error));
    });

    it('应该处理hasBirdSightingData失败的情况', async () => {
      mockElectronAPI.hasBirdSightingData.mockRejectedValue(new Error('检查状态失败'));

      const result = await birdSightingApi.getBirdSightingDataStatus();

      // hasBirdSightingData失败时会返回false，然后进入无数据的处理逻辑
      expect(result.hasData).toBe(false);
      expect(result.suggestions).toContain('当前没有观鸟数据，点击"上传数据"按钮导入Excel文件');
      expect(result.suggestions).toContain('Excel文件应包含鸟种编号、中文名、拉丁名、英文名、目、科、记录次数、记录时间等字段');
      
      // 同时检查hasBirdSightingData的错误日志
      expect(console.error).toHaveBeenCalledWith('❌ API:', '检查观鸟数据状态失败: 检查状态失败');
    });
  });

  describe('Web模式测试', () => {
    beforeEach(() => {
      // Mock window.electronAPI to be undefined to simulate web mode
      Object.defineProperty(window, 'electronAPI', {
        value: undefined,
        writable: true
      });
    });

    afterEach(() => {
      // Restore electronAPI mock for other tests
      Object.defineProperty(window, 'electronAPI', {
        value: mockElectronAPI,
        writable: true
      });
    });

    it('Web模式应该抛出不支持错误 - uploadBirdSightingData', async () => {
      await expect(birdSightingApi.uploadBirdSightingData('/test/file.xlsx'))
        .rejects.toThrow('Web模式暂不支持观鸟数据上传功能，请使用Electron桌面版本');
    });

    it('Web模式应该返回空数据 - getBirdSightingData', async () => {
      const result = await birdSightingApi.getBirdSightingData();
      
      expect(result).toEqual([]);
      expect(console.warn).toHaveBeenCalledWith('⚠️ API: Web模式暂不支持观鸟数据功能');
    });

    it('Web模式应该返回空统计 - getBirdSightingStats', async () => {
      const result = await birdSightingApi.getBirdSightingStats();
      
      expect(result).toEqual({
        totalRecords: 0,
        totalSpecies: 0,
        totalObservations: 0,
        latestRecord: undefined
      });
      expect(console.warn).toHaveBeenCalledWith('⚠️ API: Web模式暂不支持观鸟统计功能');
    });

    it('Web模式应该返回false - hasBirdSightingData', async () => {
      const result = await birdSightingApi.hasBirdSightingData();
      
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalledWith('⚠️ API: Web模式暂不支持观鸟数据功能');
    });
  });
});