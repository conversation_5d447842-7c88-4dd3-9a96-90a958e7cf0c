import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getDatabaseStats } from '../../services/database-stats';

// Mock constants module
vi.mock('../../constants', () => ({
  IS_ELECTRON: true,
  API_BASE_URL: 'http://localhost:3000'
}));

// Mock window.electronAPI
const mockElectronAPI = {
  getDatabaseStats: vi.fn(),
  isElectron: true
};

// Mock for non-Electron environment
const mockNonElectronWindow = {};

describe('Database Stats Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up window mock
    delete (global as any).window;
  });

  describe('Electron Environment', () => {
    beforeEach(() => {
      // Mock Electron environment
      (global as any).window = {
        electronAPI: mockElectronAPI
      };
    });

    it('应该成功获取数据库统计信息', async () => {
      const mockStats = {
        categories: { count: 5 },
        images: { count: 150 },
        tags: { count: 25 }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue({
        success: true,
        data: mockStats
      });

      const result = await getDatabaseStats();

      expect(result).toEqual({
        categoriesCount: 5,
        imagesCount: 150,
        tagsCount: 25
      });
      expect(mockElectronAPI.getDatabaseStats).toHaveBeenCalledTimes(1);
    });

    it('应该处理数据库统计获取失败的情况', async () => {
      const mockError = {
        success: false,
        error: 'Database connection failed'
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockError);

      await expect(getDatabaseStats()).rejects.toThrow('Database connection failed');
      expect(mockElectronAPI.getDatabaseStats).toHaveBeenCalledTimes(1);
    });

    it('应该处理IPC调用异常', async () => {
      const mockError = new Error('IPC communication error');
      mockElectronAPI.getDatabaseStats.mockRejectedValue(mockError);

      await expect(getDatabaseStats()).rejects.toThrow('IPC communication error');
      expect(mockElectronAPI.getDatabaseStats).toHaveBeenCalledTimes(1);
    });

    it('应该处理数据格式不正确的情况', async () => {
      const mockInvalidStats = {
        success: true,
        data: {
          categories: { count: 'invalid' }, // 应该是数字
          images: { count: 150 },
          tags: { count: 25 }
        }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockInvalidStats);

      await expect(getDatabaseStats()).rejects.toThrow('Invalid database stats format');
    });

    it('应该处理缺少数据字段的情况', async () => {
      const mockIncompleteStats = {
        success: true,
        data: {
          categories: { count: 5 },
          // 缺少 images 和 tags
        }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockIncompleteStats);

      await expect(getDatabaseStats()).rejects.toThrow('Missing required database stats fields');
    });

    it('应该处理空数据的情况', async () => {
      const mockEmptyStats = {
        success: true,
        data: null
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockEmptyStats);

      await expect(getDatabaseStats()).rejects.toThrow('No database stats data received');
    });
  });

  describe('Non-Electron Environment', () => {
    beforeEach(() => {
      // Mock non-Electron environment by mocking constants
      vi.doMock('../../constants', () => ({
        IS_ELECTRON: false,
        API_BASE_URL: 'http://localhost:3000'
      }));

      // Mock non-Electron window
      (global as any).window = mockNonElectronWindow;
    });

    it('应该在非Electron环境中抛出错误', async () => {
      // Re-import to get the mocked constants
      const { getDatabaseStats: getDatabaseStatsNonElectron } = await import('../../services/database-stats');

      await expect(getDatabaseStatsNonElectron()).rejects.toThrow(
        'Database stats are only available in Electron environment'
      );
    });
  });

  describe('No Window Environment (SSR)', () => {
    it('应该在没有window对象的环境中抛出错误', async () => {
      // No window object
      await expect(getDatabaseStats()).rejects.toThrow(
        'Database stats are only available in Electron environment'
      );
    });
  });

  describe('边界情况测试', () => {
    beforeEach(() => {
      (global as any).window = {
        electronAPI: mockElectronAPI
      };
    });

    it('应该处理零值统计数据', async () => {
      const mockZeroStats = {
        success: true,
        data: {
          categories: { count: 0 },
          images: { count: 0 },
          tags: { count: 0 }
        }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockZeroStats);

      const result = await getDatabaseStats();

      expect(result).toEqual({
        categoriesCount: 0,
        imagesCount: 0,
        tagsCount: 0
      });
    });

    it('应该处理大数值统计数据', async () => {
      const mockLargeStats = {
        success: true,
        data: {
          categories: { count: 999999 },
          images: { count: 1000000 },
          tags: { count: 500000 }
        }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockLargeStats);

      const result = await getDatabaseStats();

      expect(result).toEqual({
        categoriesCount: 999999,
        imagesCount: 1000000,
        tagsCount: 500000
      });
    });

    it('应该处理负数统计数据（虽然不应该发生）', async () => {
      const mockNegativeStats = {
        success: true,
        data: {
          categories: { count: -1 },
          images: { count: 150 },
          tags: { count: 25 }
        }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockNegativeStats);

      await expect(getDatabaseStats()).rejects.toThrow('Invalid database stats format');
    });
  });

  describe('类型安全测试', () => {
    beforeEach(() => {
      (global as any).window = {
        electronAPI: mockElectronAPI
      };
    });

    it('应该验证返回数据的类型安全性', async () => {
      const mockStats = {
        success: true,
        data: {
          categories: { count: 5 },
          images: { count: 150 },
          tags: { count: 25 }
        }
      };

      mockElectronAPI.getDatabaseStats.mockResolvedValue(mockStats);

      const result = await getDatabaseStats();

      expect(typeof result.categoriesCount).toBe('number');
      expect(typeof result.imagesCount).toBe('number');
      expect(typeof result.tagsCount).toBe('number');
      expect(Number.isInteger(result.categoriesCount)).toBe(true);
      expect(Number.isInteger(result.imagesCount)).toBe(true);
      expect(Number.isInteger(result.tagsCount)).toBe(true);
    });
  });
});
