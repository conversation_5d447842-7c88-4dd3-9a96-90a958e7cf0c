/**
 * @vitest-environment jsdom
 */
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { searchSpecies, getSpeciesInfo, getSpeciesByFamily, getSpeciesByGenus } from '../../services/species';
import type { 
  SpeciesDictionarySearchOptions, 
  SpeciesDictionarySearchResult,
  SpeciesDictionaryEntry 
} from '../../schemas/speciesDictionary';

// Mock window.electronAPI
const mockElectronAPI = {
  species: {
    searchSpecies: vi.fn(),
    getSpeciesInfo: vi.fn(),
    getSpeciesByFamily: vi.fn(),
    getSpeciesByGenus: vi.fn(),
  }
};

// Set up global mock
beforeEach(() => {
  vi.clearAllMocks();
  
  // Mock window.electronAPI
  Object.defineProperty(window, 'electronAPI', {
    value: mockElectronAPI,
    writable: true,
  });
});

describe('Species Service', () => {
  describe('searchSpecies', () => {
    it('calls electronAPI.species.search with correct parameters', async () => {
      const mockResult: SpeciesDictionarySearchResult = {
        species: [
          { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '喜鹊' },
          { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '蓝喜鹊' },
        ],
        total: 2,
        query: '喜鹊',
        searchTime: 5,
      };

      mockElectronAPI.species.searchSpecies.mockResolvedValue(mockResult);

      const options: SpeciesDictionarySearchOptions = {
        query: '喜鹊',
        limit: 20,
      };

      const result = await searchSpecies(options);

      expect(mockElectronAPI.species.searchSpecies).toHaveBeenCalledWith(options);
      expect(result).toEqual(mockResult);
    });

    it('throws error when electronAPI is not available', async () => {
      // Remove electronAPI
      Object.defineProperty(window, 'electronAPI', {
        value: undefined,
        writable: true,
      });

      const options: SpeciesDictionarySearchOptions = {
        query: '喜鹊',
        limit: 20,
      };

      await expect(searchSpecies(options)).rejects.toThrow('Species API not available');
    });

    it('handles search errors gracefully', async () => {
      const error = new Error('Search failed');
      mockElectronAPI.species.searchSpecies.mockRejectedValue(error);

      const options: SpeciesDictionarySearchOptions = {
        query: '喜鹊',
        limit: 20,
      };

      await expect(searchSpecies(options)).rejects.toThrow('搜索物种失败: Search failed');
    });

    it('handles non-Error objects in catch block', async () => {
      const errorMessage = 'String error';
      mockElectronAPI.species.searchSpecies.mockRejectedValue(errorMessage);

      const options: SpeciesDictionarySearchOptions = {
        query: '喜鹊',
        limit: 20,
      };

      await expect(searchSpecies(options)).rejects.toThrow('搜索物种失败: String error');
    });
  });

  describe('getSpeciesInfo', () => {
    it('calls electronAPI.species.getInfo with correct parameters', async () => {
      const mockResult: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '喜鹊属',
        种: '喜鹊',
      };

      mockElectronAPI.species.getSpeciesInfo.mockResolvedValue(mockResult);

      const result = await getSpeciesInfo('喜鹊');

      expect(mockElectronAPI.species.getSpeciesInfo).toHaveBeenCalledWith('喜鹊');
      expect(result).toEqual(mockResult);
    });

    it('returns null when species not found', async () => {
      mockElectronAPI.species.getSpeciesInfo.mockResolvedValue(null);

      const result = await getSpeciesInfo('不存在的物种');

      expect(mockElectronAPI.species.getSpeciesInfo).toHaveBeenCalledWith('不存在的物种');
      expect(result).toBeNull();
    });

    it('throws error when electronAPI is not available', async () => {
      Object.defineProperty(window, 'electronAPI', {
        value: undefined,
        writable: true,
      });

      await expect(getSpeciesInfo('喜鹊')).rejects.toThrow('Species API not available');
    });

    it('handles getInfo errors gracefully', async () => {
      const error = new Error('GetInfo failed');
      mockElectronAPI.species.getSpeciesInfo.mockRejectedValue(error);

      await expect(getSpeciesInfo('喜鹊')).rejects.toThrow('获取物种信息失败: GetInfo failed');
    });
  });

  describe('getSpeciesByFamily', () => {
    it('calls electronAPI.species.getByFamily with correct parameters', async () => {
      const mockResult: SpeciesDictionaryEntry[] = [
        { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '喜鹊' },
        { 目: '雀形目', 科: '鸦科', 属: '乌鸦属', 种: '乌鸦' },
      ];

      mockElectronAPI.species.getSpeciesByFamily.mockResolvedValue(mockResult);

      const result = await getSpeciesByFamily('鸦科');

      expect(mockElectronAPI.species.getSpeciesByFamily).toHaveBeenCalledWith('鸦科', undefined);
      expect(result).toEqual(mockResult);
    });

    it('calls electronAPI.species.getByFamily with options', async () => {
      const mockResult: SpeciesDictionaryEntry[] = [
        { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '喜鹊' },
      ];

      mockElectronAPI.species.getSpeciesByFamily.mockResolvedValue(mockResult);

      const options = { limit: 10 };
      const result = await getSpeciesByFamily('鸦科', options);

      expect(mockElectronAPI.species.getSpeciesByFamily).toHaveBeenCalledWith('鸦科', options);
      expect(result).toEqual(mockResult);
    });

    it('throws error when electronAPI is not available', async () => {
      Object.defineProperty(window, 'electronAPI', {
        value: undefined,
        writable: true,
      });

      await expect(getSpeciesByFamily('鸦科')).rejects.toThrow('Species API not available');
    });

    it('handles getByFamily errors gracefully', async () => {
      const error = new Error('GetByFamily failed');
      mockElectronAPI.species.getSpeciesByFamily.mockRejectedValue(error);

      await expect(getSpeciesByFamily('鸦科')).rejects.toThrow('按科查询物种失败: GetByFamily failed');
    });
  });

  describe('getSpeciesByGenus', () => {
    it('calls electronAPI.species.getByGenus with correct parameters', async () => {
      const mockResult: SpeciesDictionaryEntry[] = [
        { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '喜鹊' },
        { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '蓝喜鹊' },
      ];

      mockElectronAPI.species.getSpeciesByGenus.mockResolvedValue(mockResult);

      const result = await getSpeciesByGenus('喜鹊属');

      expect(mockElectronAPI.species.getSpeciesByGenus).toHaveBeenCalledWith('喜鹊属', undefined);
      expect(result).toEqual(mockResult);
    });

    it('calls electronAPI.species.getByGenus with options', async () => {
      const mockResult: SpeciesDictionaryEntry[] = [
        { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '喜鹊' },
      ];

      mockElectronAPI.species.getSpeciesByGenus.mockResolvedValue(mockResult);

      const options = { limit: 5 };
      const result = await getSpeciesByGenus('喜鹊属', options);

      expect(mockElectronAPI.species.getSpeciesByGenus).toHaveBeenCalledWith('喜鹊属', options);
      expect(result).toEqual(mockResult);
    });

    it('throws error when electronAPI is not available', async () => {
      Object.defineProperty(window, 'electronAPI', {
        value: undefined,
        writable: true,
      });

      await expect(getSpeciesByGenus('喜鹊属')).rejects.toThrow('Species API not available');
    });

    it('handles getByGenus errors gracefully', async () => {
      const error = new Error('GetByGenus failed');
      mockElectronAPI.species.getSpeciesByGenus.mockRejectedValue(error);

      await expect(getSpeciesByGenus('喜鹊属')).rejects.toThrow('按属查询物种失败: GetByGenus failed');
    });
  });

  describe('Edge Cases', () => {
    it('handles empty string parameters', async () => {
      const mockResult: SpeciesDictionarySearchResult = {
        species: [],
        total: 0,
        query: '',
        searchTime: 1,
      };

      mockElectronAPI.species.searchSpecies.mockResolvedValue(mockResult);

      const options: SpeciesDictionarySearchOptions = {
        query: '',
        limit: 20,
      };

      const result = await searchSpecies(options);

      expect(mockElectronAPI.species.searchSpecies).toHaveBeenCalledWith(options);
      expect(result).toEqual(mockResult);
    });

    it('handles unicode characters in search query', async () => {
      const mockResult: SpeciesDictionarySearchResult = {
        species: [
          { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '蓝喜鹊' },
        ],
        total: 1,
        query: '蓝喜鹊🐦',
        searchTime: 3,
      };

      mockElectronAPI.species.searchSpecies.mockResolvedValue(mockResult);

      const options: SpeciesDictionarySearchOptions = {
        query: '蓝喜鹊🐦',
        limit: 20,
      };

      const result = await searchSpecies(options);

      expect(mockElectronAPI.species.searchSpecies).toHaveBeenCalledWith(options);
      expect(result).toEqual(mockResult);
    });

    it('handles very large limit values', async () => {
      const mockResult: SpeciesDictionarySearchResult = {
        species: [],
        total: 0,
        query: 'test',
        searchTime: 1,
      };

      mockElectronAPI.species.searchSpecies.mockResolvedValue(mockResult);

      const options: SpeciesDictionarySearchOptions = {
        query: 'test',
        limit: 999999,
      };

      const result = await searchSpecies(options);

      expect(mockElectronAPI.species.searchSpecies).toHaveBeenCalledWith(options);
      expect(result).toEqual(mockResult);
    });
  });

  describe('API Response Validation', () => {
    it('returns search results as received from API', async () => {
      const mockResult: SpeciesDictionarySearchResult = {
        species: [
          { 目: '雀形目', 科: '鸦科', 属: '喜鹊属', 种: '喜鹊' },
        ],
        total: 1,
        query: '喜鹊',
        searchTime: 5,
      };

      mockElectronAPI.species.searchSpecies.mockResolvedValue(mockResult);

      const options: SpeciesDictionarySearchOptions = {
        query: '喜鹊',
        limit: 20,
      };

      const result = await searchSpecies(options);

      expect(result).toEqual(mockResult);
      expect(result.species).toHaveLength(1);
      expect(result.species[0].种).toBe('喜鹊');
      expect(result.total).toBe(1);
      expect(result.query).toBe('喜鹊');
      expect(result.searchTime).toBe(5);
    });

    it('preserves all species properties', async () => {
      const mockSpecies: SpeciesDictionaryEntry = {
        目: '雀形目',
        科: '鸦科',
        属: '喜鹊属',
        种: '喜鹊',
      };

      mockElectronAPI.species.getSpeciesInfo.mockResolvedValue(mockSpecies);

      const result = await getSpeciesInfo('喜鹊');

      expect(result).toEqual(mockSpecies);
      expect(result?.目).toBe('雀形目');
      expect(result?.科).toBe('鸦科');
      expect(result?.属).toBe('喜鹊属');
      expect(result?.种).toBe('喜鹊');
    });
  });
});