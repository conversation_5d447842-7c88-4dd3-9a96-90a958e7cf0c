declare module 'exif-parser' {
  interface ExifResult {
    startMarker: any;
    tags: Record<string, any>;
    imageSize?: {
      width: number;
      height: number;
    };
    thumbnailOffset?: number;
    thumbnailLength?: number;
    thumbnailType?: number;
    app1Offset?: number;
    
    hasThumbnail(mime?: string): boolean;
    getThumbnailOffset(): number;
    getThumbnailLength(): number;
    getThumbnailBuffer(): Buffer;
    getImageSize(): { width: number; height: number } | undefined;
    getThumbnailSize(): { width: number; height: number } | undefined;
  }

  interface ExifParser {
    enableBinaryFields(enable?: boolean): ExifParser;
    enablePointers(enable?: boolean): ExifParser;
    enableTagNames(enable?: boolean): ExifParser;
    enableImageSize(enable?: boolean): ExifParser;
    enableReturnTags(enable?: boolean): ExifParser;
    enableSimpleValues(enable?: boolean): ExifParser;
    parse(): ExifResult;
  }

  function create(buffer: Buffer | ArrayBuffer, global?: any): ExifParser;

  export = {
    create: typeof create;
  };
}
