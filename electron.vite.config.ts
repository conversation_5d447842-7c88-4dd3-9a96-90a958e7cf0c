import { defineConfig } from 'electron-vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { copyFileSync, existsSync, mkdirSync, readdirSync, cpSync } from 'fs';

// 创建一个插件来复制HTML文件和数据文件
const copyAssetsPlugin = () => {
  return {
    name: 'copy-assets',
    writeBundle() {
      // 复制HTML文件
      const sourceHtml = resolve(__dirname, 'electron/oss-config-window.html');
      const targetDir = resolve(__dirname, 'dist-electron/main');
      const targetHtml = resolve(targetDir, 'oss-config-window.html');
      
      if (!existsSync(targetDir)) {
        mkdirSync(targetDir, { recursive: true });
      }
      
      if (existsSync(sourceHtml)) {
        copyFileSync(sourceHtml, targetHtml);
        console.log('✅ 复制HTML文件:', targetHtml);
      } else {
        console.warn('⚠️  HTML文件不存在:', sourceHtml);
      }

      // 确保数据文件正确复制到renderer目录（为物种查询服务使用）
      const sourceDataDir = resolve(__dirname, 'public/data');
      const targetDataDir = resolve(__dirname, 'dist-electron/renderer/data');
      
      if (existsSync(sourceDataDir)) {
        try {
          if (!existsSync(targetDataDir)) {
            mkdirSync(targetDataDir, { recursive: true });
          }
          
          // 复制所有数据文件
          cpSync(sourceDataDir, targetDataDir, { recursive: true });
          console.log('✅ 复制数据文件目录:', targetDataDir);
          
          // 验证关键数据文件（特别是物种列表文件）
          const criticalFiles = [
            'bird_sightings.json',
            'top_birds.json',
            '物种列表.json'  // 物种标签匹配功能必需
          ];
          
          criticalFiles.forEach(file => {
            const filePath = resolve(targetDataDir, file);
            if (existsSync(filePath)) {
              console.log(`✅ 数据文件已复制: ${file}`);
            } else {
              console.warn(`⚠️  数据文件缺失: ${file}`);
            }
          });
          
        } catch (error) {
          console.error('❌ 复制数据文件失败:', error);
        }
      } else {
        console.warn('⚠️  源数据目录不存在:', sourceDataDir);
      }
    }
  };
};

export default defineConfig({
  main: {
    plugins: [copyAssetsPlugin()],
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'electron/main.ts'),
        external: [
          'better-sqlite3',
          'uuid',
          'jimp',
          '@aws-sdk/client-s3',
          '@aws-sdk/lib-storage',
          '@aws-sdk/s3-request-presigner',
          'exif-parser'
        ]
      },
      outDir: 'dist-electron/main'
    }
  },
  preload: {
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'electron/preload.ts'),
        output: {
          format: 'cjs',
          entryFileNames: 'preload.js'
        }
      },
      outDir: 'dist-electron/preload'
    }
  },
  renderer: {
    plugins: [react()],
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'index.html')
      },
      outDir: 'dist-electron/renderer'
    },
    root: '.',
    publicDir: 'public',
    server: {
      port: 5173
    }
  }
});