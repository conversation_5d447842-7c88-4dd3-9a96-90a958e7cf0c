# 图片上传物种标签匹配功能实现计划

## 🎯 任务概述

本计划旨在在图片上传弹窗的Tags (Optional, comma-separated)字段下方添加物种标签匹配功能。当检测到当前分类名称为物种名称时，显示文字提示和按钮，用户可点击将对应的目、科、属信息添加到tags中，以逗号分隔。

### 🏗️ 项目背景信息

#### 应用架构
- **应用类型**: Electron桌面应用（Linux/Windows/macOS）
- **前端框架**: React 19 + TypeScript + Tailwind CSS
- **后端**: Electron主进程 + SQLite数据库
- **构建工具**: Vite + electron-vite
- **测试框架**: Vitest + @testing-library

#### 现有相关功能
- **物种查询服务**: 已实现`SpeciesService`，支持多维度搜索（精确、前缀、拼音、N-gram）
- **图片上传组件**: 已实现`ImageUploadForm`组件
- **数据源**: `public/data/物种列表.json`包含11,194条鸟类物种记录
- **种类匹配**: 已实现`CategorySuggestions`组件

#### 技术栈
- **框架**: Electron + React + TypeScript
- **测试**: Vitest + @testing-library
- **数据库**: SQLite
- **通信**: IPC (ipcMain.handle/ipcRenderer.invoke)
- **UI**: Tailwind CSS + 主题系统

## 🔧 技术方案核心设计

### 架构概览
- **数据源**: 复用现有的`SpeciesService`和物种列表数据
- **UI组件**: 增强`ImageUploadForm`组件，添加物种标签匹配区域
- **匹配逻辑**: 基于当前分类名称进行物种目科属信息匹配
- **交互体验**: 异步匹配 + 可选添加 + 主题适配

### 核心设计
- **异步匹配**: 基于当前分类名称进行物种目科属信息检测，不影响弹窗加载速度
- **智能提示**: 显示匹配到的物种名称和对应的目科属信息
- **可选添加**: 提供按钮让用户选择是否添加到tags中
- **主题适配**: 按钮和文字样式匹配当前主题风格

### 功能流程
1. 用户在特定分类（如"喜鹊"）下打开图片上传弹窗
2. 系统根据当前分类名称异步匹配对应的物种目科属信息
3. 如果匹配到物种，在Tags字段下方显示提示文字和添加按钮
4. 用户点击按钮，将目科属信息添加到tags字段
5. tags字段以逗号分隔显示多个标签

## 📝 详细任务清单

### 📋 **阶段一：物种标签组件开发**

**注意**: 物种查询服务已在前期计划中完成，直接复用 `services/species.ts` 中的 `getSpeciesInfo()` API，获取目、科、属信息

#### [ ] 1. 创建物种标签组件测试
**文件**: `/__tests__/components/SpeciesTagSuggestion.test.tsx`
**目标**: 为物种标签建议组件编写测试
**预估时间**: 60分钟

**测试覆盖范围**:
- 组件基本渲染
- 物种目科属信息显示
- 添加按钮交互
- 主题样式适配
- 属性传递和回调
- 边界情况处理

#### [ ] 2. 实现物种标签组件
**文件**: `/components/SpeciesTagSuggestion.tsx`
**目标**: 创建物种标签建议组件
**预估时间**: 90分钟

**核心功能**:
- 显示匹配到的物种名称
- 展示目科属信息
- 提供添加按钮
- 主题样式适配
- 可访问性支持
- TypeScript类型安全

#### [ ] 3. 运行物种标签组件测试
**目标**: 确保组件测试全部通过
**预估时间**: 30分钟

**验证标准**:
- 所有测试用例通过 ✅
- 测试覆盖率 > 90% ✅
- 主题样式正确 ✅

---

### 📋 **阶段二：在ImageUploadForm中添加物种标签功能**

#### [ ] 4. 在ImageUploadForm中添加物种标签区域
**文件**: `/components/ImageUploadForm.tsx`
**目标**: 在Tags字段下方添加物种标签建议区域
**预估时间**: 60分钟

**添加内容**:
- 在Tags输入框下方添加`SpeciesTagSuggestion`组件
- 传入当前categoryId对应的分类名称
- 处理点击按钮后添加标签到tags字段的逻辑
- 保持现有表单功能完全不变

#### [ ] 5. 测试添加的物种标签功能
**目标**: 验证新添加的功能正常工作
**预估时间**: 30分钟

**验证内容**:
- 物种标签组件正确显示
- 点击按钮能正确添加标签
- 不影响原有表单功能

---

### 📋 **阶段三：集成测试和用户体验优化**

#### [ ] 6. 创建端到端集成测试
**文件**: `/__tests__/integration/species-tag-matching.test.ts`
**目标**: 测试完整的物种标签匹配流程
**预估时间**: 90分钟

**测试覆盖范围**:
- 完整的用户使用流程
- 组件间协作正确性
- 异步匹配性能测试
- 多物种名称处理
- 边界情况处理

#### [ ] 7. 运行完整测试套件
**目标**: 确保所有功能正常且性能达标
**预估时间**: 45分钟

**验证标准**:
- 所有单元测试通过 (100%) ✅
- 所有集成测试通过 (100%) ✅
- 性能测试达标 (匹配响应 < 100ms) ✅
- 测试覆盖率 ≥ 95% ✅
- 无TypeScript错误 ✅
- 无Lint错误 ✅

---

### 📋 **阶段四：文档和代码质量**

#### [ ] 8. 更新项目文档
**文件**: `/CLAUDE.md`
**目标**: 更新项目文档以反映新功能
**预估时间**: 30分钟

**更新内容**:
- 新增组件说明
- 功能特性更新
- 使用指南更新

#### [ ] 9. 代码质量检查
**目标**: 确保代码质量符合项目标准
**预估时间**: 30分钟

**检查项目**: 
- TypeScript类型检查
- ESLint代码检查
- Prettier格式检查
- 依赖项安全检查
- 构建测试

#### [ ] 10. 最终验收测试
**目标**: 验证完整功能
**预估时间**: 45分钟

**验收标准**:
- 功能验收: 物种目科属识别、标签添加 ✅
- 质量验收: 测试覆盖率、代码质量、类型安全 ✅
- 测试验收: `npm test` 通过率 100% ✅

**最终测试**: 在实际Electron应用中手动验证功能

---

## 🎯 **交付物清单**

### 交付文件
- **核心组件**: SpeciesTagSuggestion.tsx、增强的ImageUploadForm.tsx
- **测试文件**: 组件测试、集成测试
- **文档**: 项目文档更新
- **依赖**: 无新增依赖（复用现有 `services/species.ts` API）

---

## 📊 **验收标准总结**

| 项目 | 目标 | 验收标准 |
|------|------|----------|
| 功能完整性 | 100% | 物种目科属匹配、标签添加、异步处理正常工作 |
| 性能指标 | 达标 | 匹配响应<100ms |
| 测试覆盖率 | ≥95% | 组件+集成测试 |
| 测试通过率 | 100% | `npm test` 全部通过 |
| 代码质量 | 无错误 | TypeScript+ESLint+Prettier |
| 用户体验 | 简单 | 基本交互功能正常 |

---

**开发负责人**: Claude Code Assistant
**计划创建时间**: 2025-07-18
**预估完成时间**: 1天
**优先级**: 高

---

## 🔍 **技术实现细节**

### 技术实现说明
**复用现有API**: 直接使用已实现的 `services/species.ts` 中的 `getSpeciesInfo(categoryName)` 函数

### 物种标签组件接口
```typescript
interface SpeciesTagSuggestionProps {
  species: SpeciesDictionaryEntry;
  onAddTags: (tags: string) => void;
  className?: string;
}
```

### 用户体验设计

#### 交互流程
1. **加载阶段**: 用户在特定分类下打开图片上传弹窗
2. **匹配阶段**: 系统根据分类名称异步匹配物种科属种信息
3. **显示阶段**: 在Tags字段下方显示匹配结果和添加按钮
4. **添加阶段**: 用户点击按钮，将科属种信息添加到tags字段
5. **确认阶段**: tags字段显示更新后的标签内容

#### 视觉设计
- 匹配成功时显示绿色图标
- 按钮样式匹配当前主题
- 加载时显示合适的指示器
- 错误时显示友好提示

### 功能示例

#### 匹配示例
- 当前分类: "喜鹊"
- 匹配结果: 雀形目·鸦科·鹊属
- 显示提示: "添加喜鹊对应目科属（雀形目·鸦科·鹊属）到tag中"
- 添加内容: "雀形目, 鸦科, 鹊属"

#### 标签处理
- 现有tags: "鸟类摄影, 野生动物"
- 添加后: "鸟类摄影, 野生动物, 雀形目, 鸦科, 鹊属"