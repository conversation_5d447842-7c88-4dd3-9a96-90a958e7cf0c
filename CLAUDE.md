# CLAUDE.md

本文档为Claude Code (claude.ai/code) 提供在此代码库中工作的指导。

## 开发命令

### 包管理器
- `npm install` - 安装依赖
- `npm run rebuild:all` - 为测试和Electron环境重建原生模块
- `npm run rebuild:test` - 为Node.js测试环境重建原生模块
- `npm run rebuild:electron` - 为Electron运行时重建原生模块

### 开发与构建
- `npm run dev` - 启动Web版本的Vite开发服务器
- `npm run build` - 构建生产环境的Web版本
- `npm run preview` - 预览生产环境Web构建
- `npm run electron:dev` - 启动Electron开发模式
- `npm run electron:build` - 构建Electron应用
- `npm run electron:start` - 启动打包后的Electron应用
- `npm run electron:pack` - 打包Electron应用（不包含分发）
- `npm run electron:dist` - 为当前平台构建并分发
- `npm run electron:dist:linux` - 构建Linux AppImage
- `npm run electron:dist:win` - 构建Windows安装包
- `npm run electron:dist:mac` - 构建macOS DMG

### 代码质量
- `npm run lint` - 运行ESLint代码分析
- `npm run lint:fix` - 自动修复ESLint问题
- `npm run format` - 使用Prettier格式化代码
- `npm run format:check` - 检查代码格式
- `npm run type-check` - 运行TypeScript类型检查
- `npm run check-all` - 运行所有质量检查（类型检查 + lint + 格式检查）

### 测试
- `npm test` 或 `npm run test` - 在监视模式下运行测试
- `npm run test:run` - 运行一次测试
- `npm run test:coverage` - 运行测试并生成覆盖率报告

## 架构概述

### 技术栈
- **前端**：React 19 + TypeScript + Tailwind CSS + Framer Motion
- **桌面端**：Electron 28.3.3 + better-sqlite3（本地数据库）
- **构建工具**：Vite + electron-vite（快速开发与构建）
- **测试**：Vitest（包含全面的Electron测试设置）
- **存储**：支持本地文件和S3兼容对象存储（OSS）的双存储系统
- **中文本地化**：使用Noto Serif SC字体优化中文显示
- **动画系统**：基于Framer Motion实现开屏动画和交互动效
  - 开屏动画系统采用CSS动画与Framer Motion组合技术
  - 实现几何背景动态生成算法（GeometricBackground组件）
  - 进度同步技术确保动画元素时序一致性（ProgressBar组件）
  - 使用SplashAnimations.css定义关键帧动画序列

### 项目结构
- `electron/` - Electron主进程、服务和数据库管理
- `electron/services/` - 核心业务逻辑服务（分类、图片、标签、设置、OSS）
- `electron/database/` - SQLite数据库管理和模式
- `components/` - React UI组件
- `contexts/` - React Context状态管理
- `schemas/` - Zod验证模式（类型安全）
- `services/` - 前端API服务
- `__tests__/` - 覆盖所有Electron功能的全面测试套件
- `plan/` - 存放功能开发和重构的详细实施计划文档
- `data/` - 包含示例数据文件如鸟类观测数据、统计信息等
- `public/data/` - 公共数据文件用于地图可视化和图表展示
- `utils/` - 实用工具函数
- `hooks/` - 自定义React Hooks

### 数据库架构
使用SQLite和better-sqlite3实现高性能本地存储：
- `categories`表 - 图片分类管理
- `images`表 - 图片元数据和文件引用
- `tags`表 - 标签定义
- `image_tags`表 - 图片-标签多对多关系
- `bird_sightings`表 - 观鸟数据记录
  - 存储从Excel导入的观鸟观测数据
  - 支持中文名、拉丁名、英文名、分类信息
  - 记录观测次数和时间信息

### 存储系统
支持双存储模式的无缝切换：
- **本地存储**：文件存储在用户本地文件系统中（按分类文件夹结构组织）
- **OSS存储**：S3兼容对象存储（阿里云OSS、AWS S3）
- 自定义协议处理器(`electron://`)统一访问不同存储类型的文件

### 功能领域
- **观鸟功能**：作为中国鸟类观察记录管理的专业工具，提供以下核心能力：
  - 物种智能识别：基于深度学习的鸟类物种自动识别系统
  - 地理分布分析：热力图展示中国特有鸟类地理分布特征
  - 观测数据可视化：时间轴与统计图表展示观测记录
  - **物种标签匹配**：图片上传时自动识别物种目科属信息并提供标签建议
- **分析功能**：使用ECharts/PieChart实现数据可视化分析

## 开发指南

### 原生模块管理
项目使用better-sqlite3需要原生编译，以下情况后需运行重建命令：
- 初始npm install后
- Node.js版本变更后
- 切换开发环境和Electron环境之间
- 运行测试前

### 数据库操作
- 数据库操作通过`electron/services/`中的服务类处理
- 多表操作使用事务
- 所有数据库访问通过DatabaseManager类
- SQLite运行在WAL模式提升并发性能，通过write-ahead logging实现多进程安全访问

### 测试策略
全面测试覆盖包括：
- 所有服务类的单元测试
- 数据库操作的集成测试
- 关键操作的性能基准测试
- 使用模拟服务的OSS集成测试
- 边界情况和错误处理验证

### 存储配置
- 设置由`electron/services/SettingsService.ts`中的SettingsService管理
- OSS配置安全存储在用户数据目录
- 存储类型可通过应用菜单在运行时切换
- 首次设置引导用户完成存储配置

### 代码质量标准
- 启用TypeScript严格模式（全面类型检查）
- Zod模式提供运行时验证和编译时类型
- ESLint + Prettier确保一致的代码格式
- 所有新功能应包含相应测试

### IPC通信
- 主进程通过`electron/main.ts`中的IPC处理程序暴露API
- 预加载脚本提供类型安全的API桥接
- 所有异步操作返回一致的响应结构
- 错误处理包含详细的错误消息和日志记录

#### 删除功能IPC接口
- `delete-image` - 单个图片删除，返回详细的删除结果状态
- `delete-images` - 批量图片删除，支持进度反馈和错误处理
- `delete-category` - 分类删除，自动处理关联图片的批量删除
- `validate-delete-conditions` - 删除前条件验证
- `show-delete-confirmation` - 显示删除确认对话框
- `show-delete-progress` - 删除进度通知

#### 观鸟数据IPC接口
- `upload-bird-sighting-data` - 上传Excel文件并导入观鸟数据
- `get-bird-sighting-data` - 获取所有观鸟数据记录
- `get-bird-sighting-stats` - 获取观鸟数据统计信息
- `has-bird-sighting-data` - 检查是否存在观鸟数据
- `clear-bird-sighting-data` - 清空所有观鸟数据
- `show-open-dialog` - 显示文件选择对话框

## 关键文件

### 核心系统文件
- `electron/main.ts` - Electron主进程（包含IPC处理程序和菜单系统）
- `electron/database/index.ts` - SQLite数据库管理器
- `electron/preload.ts` - 主进程和渲染进程之间的IPC桥接
- **认证系统**：采用JWT令牌验证机制
  - 结合Context API实现全局状态管理（AuthContext）
  - 支持GitHub/Gitee第三方OAuth登录扩展
  - 实现自动令牌刷新及验证机制

### 服务层
- `electron/services/CategoryService.ts` - 分类CRUD操作
  - 增强的分类删除功能，支持级联删除关联图片文件
  - 集成图片批量删除逻辑，确保数据一致性
- `electron/services/ImageService.ts` - 图片流式上传分块处理技术
  - 实时缩略图生成（基于sharp库）
  - 支持大文件分块上传和断点续传
  - **完善的删除功能**：
    - 单个图片删除，支持本地和OSS存储
    - 批量图片删除，支持进度反馈和错误处理
    - 删除前验证机制，确保操作安全性
    - 详细的删除结果反馈和审计日志
    - 删除操作的回滚机制和事务处理
- `electron/services/TagService.ts` - 标签管理和图片-标签关联
- `electron/services/SettingsService.ts` - 基于electron-store实现配置管理
  - 原子操作保证配置一致性
  - 自动持久化机制
- `electron/services/OSSService.ts` - S3兼容对象存储集成
- `electron/services/BirdSightingService.ts` - 观鸟数据管理服务
  - Excel文件解析和数据转换
  - 数据验证和格式转换
  - 数据库CRUD操作
  - 覆盖模式的批量数据更新

### 配置文件
- `electron.vite.config.ts` - Electron构建配置
- `vitest.config.ts` - 测试框架配置
- `package.json` - 依赖项和npm脚本

## 组件架构

### 核心UI组件
- `App.tsx` - 应用根组件，包含路由配置和全局状态管理
- `Layout.tsx` - 应用布局框架
- `SplashScreen.tsx` - 开屏动画组件
- `WelcomeGuide.tsx` - 首次使用引导组件

### 分类管理组件
- `CategoryList.tsx` - 分类列表展示
- `CategoryCard.tsx` - 分类卡片组件
- `CategoryDetail.tsx` - 分类详情页面
- `CategoryForm.tsx` - 分类创建/编辑表单
- `CategorySearch.tsx` - 分类搜索功能

### 图片管理组件
- `ImageCard.tsx` - 图片卡片组件
- `ImageDetailModal.tsx` - 图片详情模态框
- `ImageUploadForm.tsx` - 图片上传表单

### 数据分析组件
- `AnalyticsPage.tsx` - 数据分析页面
- `BirdSightingTimeline.tsx` - 鸟类观测时间线组件
- `ChinaBirdMap.tsx` - 中国鸟类分布热力地图
- `PieChart.tsx` - 饼图数据可视化组件

### 观鸟数据组件
- `BirdDataUploadButton.tsx` - 观鸟数据上传按钮组件
  - 支持Excel文件选择和上传
  - 实时状态反馈（上传中、成功、失败）
  - 防抖处理和用户友好的错误信息
  - 平滑的动画过渡效果
- `BirdDataUploadStatus.tsx` - 观鸟数据状态显示组件
  - 智能状态检测（有数据/无数据）
  - 数据统计信息展示
  - 缓存机制减少重复请求
  - 动画效果和交互反馈

### 实用组件
- `AlertDialog.tsx` - 警告对话框
- `EnhancedAnimations.tsx` - 增强动画组件
- `ErrorDisplay.tsx` - 错误显示组件
- `LoadingSpinner.tsx` - 加载指示器
- `Modal.tsx` - 模态框基础组件
- `ShortcutsModal.tsx` - 快捷键指南模态框
- `SpeciesDetailCard.tsx` - 物种详情卡片
- `SpeciesSearch.tsx` - 物种搜索组件
- `SpeciesTagSuggestion.tsx` - 物种标签建议组件，实现图片上传时的智能标签推荐

## 状态管理

### Context API实现
- `AuthContext.tsx` - 处理用户认证状态和令牌管理
- `CategoryContext.tsx` - 管理分类数据和相关操作
- `ThemeContext.tsx` - 主题切换和颜色模式管理

### 自定义Hooks
- `useSplashAnimation.ts` - 开屏动画控制逻辑
  - 支持多任务并行加载进度跟踪
  - 提供动画阶段控制API
  - 实现最小显示时间保证

## 数据文件

### 示例数据
- `bird_sightings.json` - 鸟类观测记录数据
- `region_bird_stats.json` - 地区鸟类统计数据
- `top_birds.json` - 热门鸟类数据
- `social_stats.json` - 社交分享统计数据
- `analytics_summary.json` - 分析摘要数据

### 地理数据
- `public/china.json` - 中国地理边界数据（用于地图可视化）

## 开发计划

### 实施计划文档
位于`plan/`目录下的详细开发计划文档：
- `1.CDN本地化显示一致性修复计划.md`
- `2.Framer Motion动画增强计划.md`
- `3.TailwindCSS迁移计划.md`
- `4.WebGL图像查看器实施计划.md`
- `5.Zod数据验证迁移计划.md`
- `6.本地Electron应用改造完整实施方案.md`
- `7.图片存储配置功能开发计划.md`
- `8.开屏动画系统实施计划.md`
- `9.Electron单元测试覆盖计划.md`
- `10.OSS存储配置功能开发计划.md`
- `11.一键重置数据库功能开发计划.md`
- `21.图片上传物种标签匹配功能实现计划.md` - 已完成：实现了图片上传时的物种标签智能匹配功能

## 字体管理系统

### 字体实现
- 使用`@font-face`在`src/assets/fonts/fonts.css`中定义
- 支持三种字体族：Inter（界面字体）、IBM Plex Mono（代码字体）、Noto Serif SC（中文字体）
- 字体文件位于`src/assets/fonts/`各子目录中

## 实用工具

### 工具函数
- `utils/animations.ts` - 动画辅助函数
  - 提供常用动画工具
  - 简化复杂动画实现

### ECharts集成
- `services/echarts.ts` - ECharts图表初始化和配置工具
  - 封装图表生命周期管理
  - 提供响应式支持
  - 处理主题切换

### 观鸟数据API
- `services/bird-sighting-api.ts` - 观鸟数据前端API服务
  - 文件选择和上传功能
  - 数据状态检查和统计
  - 支持Electron和Web双模式
  - Zod数据验证集成
  - 统一错误处理和用户友好提示

## 测试策略详解

### 测试目录结构
- `__tests__/electron/` - Electron相关测试
  - `database/` - 数据库测试
  - `edge-cases/` - 边界情况测试
  - `integration/` - 集成测试
  - `performance/` - 性能测试
  - `services/` - 服务层单元测试
  - `helpers/` - 测试辅助工具

### 测试类型
- **单元测试**：验证独立函数和类的行为
- **集成测试**：测试多个模块协同工作
- **性能测试**：确保关键操作满足性能要求
- **边界测试**：验证错误处理和非标准输入场景

## 认证系统实现

### JWT认证流程
1. 用户通过登录表单提交凭证
2. 验证成功后服务器返回access token和refresh token
3. 客户端存储token于localStorage或electron-store
4. 后续请求携带access token于Authorization头
5. token过期时使用refresh token获取新access token

### 第三方OAuth
- 支持GitHub/Gitee等第三方登录
- 通过OAuth 2.0授权码流程实现
- 安全存储用户token

## Service Worker实现

### PWA功能
- `public/service-worker.js` - Service Worker实现文件
- 支持离线访问关键资源
- 实现资源预缓存策略
- 处理后台同步和推送通知

### 生命周期管理
- `install`事件：预缓存核心资源
- `activate`事件：清理旧缓存
- `fetch`事件：拦截网络请求提供缓存优先策略

## 删除功能使用指南

### 单个图片删除
```typescript
// 前端调用示例
import { deleteImage, validateDeleteConditions, showDeleteConfirmation } from '../services/api';

// 1. 验证删除条件（可选）
const validation = await validateDeleteConditions(imageId);
if (!validation.valid) {
  console.error('删除验证失败:', validation.errors);
  return;
}

// 2. 显示删除确认对话框
const confirmation = await showDeleteConfirmation({
  type: 'single',
  itemName: '图片'
});

if (!confirmation.confirmed) {
  return;
}

// 3. 执行删除
try {
  const result = await deleteImage(imageId);
  if (result.success) {
    console.log('图片删除成功');
  } else {
    console.error('图片删除失败:', result.error);
  }
} catch (error) {
  console.error('删除操作异常:', error);
}
```

### 批量图片删除
```typescript
// 批量删除示例
import { deleteImages, showDeleteConfirmation } from '../services/api';

const imageIds = ['id1', 'id2', 'id3'];

// 1. 显示批量删除确认
const confirmation = await showDeleteConfirmation({
  type: 'batch',
  count: imageIds.length,
  itemName: '图片'
});

if (!confirmation.confirmed) {
  return;
}

// 2. 执行批量删除
try {
  const result = await deleteImages(imageIds);
  console.log(`批量删除完成: 成功 ${result.successCount}/${result.totalCount}`);

  if (result.failedCount > 0) {
    console.warn('部分删除失败:', result.errors);
  }
} catch (error) {
  console.error('批量删除异常:', error);
}
```

### 分类删除（自动处理图片）
```typescript
// 分类删除示例
import { deleteCategory } from '../services/api';

try {
  const result = await deleteCategory(categoryId);

  if (result.success) {
    console.log('分类删除成功:', result.message);
    console.log(`删除了 ${result.details?.imagesDeleted.successCount} 张图片`);
  } else {
    console.error('分类删除失败:', result.message);
  }
} catch (error) {
  console.error('分类删除异常:', error);
}
```

### 删除结果处理
```typescript
// 删除结果接口
interface DeleteResult {
  success: boolean;
  imageId: string;
  error?: string;
  storageDeleted: boolean;
  databaseDeleted: boolean;
  details?: {
    imagePath?: string;
    thumbnailPath?: string;
    storageType: string;
  };
}

// 批量删除结果接口
interface BatchDeleteResult {
  totalCount: number;
  successCount: number;
  failedCount: number;
  results: DeleteResult[];
  errors: string[];
}
```

## 开发注意事项

- 所有数据库操作应使用事务确保一致性
- 图片上传需要适当的错误处理和进度反馈
- 新功能应包含相应的测试用例
- 遵循现有的代码风格和架构模式
- 确保跨平台兼容性（Windows、macOS、Linux）
- **删除操作注意事项**：
  - 删除前应进行条件验证
  - 提供用户确认机制
  - 实现详细的错误处理和日志记录
  - 确保存储文件和数据库记录的一致性
  - 支持批量操作的进度反馈
- **物种标签匹配功能注意事项**：
  - 功能基于现有的`services/species.ts`物种查询API实现
  - 只在有效的物种分类下显示标签建议
  - 自动过滤空的分类学信息（目、科、属）
  - 异步匹配不影响表单加载性能
  - 已完整测试覆盖（36个测试用例通过）