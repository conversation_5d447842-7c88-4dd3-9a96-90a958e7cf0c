const ExifParser = require('exif-parser');
import { validateExifData } from '../../schemas/image';
import type { ExifData } from '../../schemas/image';

/**
 * EXIF信息提取服务
 *
 * 负责从图片文件中提取EXIF元数据，并将其转换为应用程序可用的格式。
 * 支持JPEG和TIFF格式（exif-parser主要支持的格式）。
 */
export class ExifService {
  /**
   * 从图片Buffer中提取EXIF信息
   *
   * @param buffer 图片文件的Buffer数据
   * @returns Promise<ExifData | null> 提取的EXIF数据，如果没有EXIF信息或提取失败则返回null
   */
  async extractExifFromBuffer(buffer: Buffer): Promise<ExifData | null> {
    try {
      // 验证输入参数
      if (!buffer || !Buffer.isBuffer(buffer) || buffer.length === 0) {
        console.warn('⚠️ ExifService: 无效的Buffer输入');
        return null;
      }

      // 使用exif-parser提取EXIF数据
      const parser = ExifParser.create(buffer);
      const result = parser.parse();

      // 检查是否有EXIF数据
      if (!result.tags || Object.keys(result.tags).length === 0) {
        console.debug('📷 ExifService: 图片不包含EXIF数据');
        return null;
      }

      // 将exif-parser输出映射到我们的schema格式
      const mappedData = this.mapExifParserToSchema(result.tags);

      // 检查映射后的数据是否有有效内容
      if (!this.hasValidExifData(mappedData)) {
        console.debug('📷 ExifService: EXIF数据为空或无效');
        return null;
      }

      // 验证数据格式
      const validatedData = this.validateExifData(mappedData);

      console.debug('✅ ExifService: 成功提取EXIF数据', {
        fields: Object.keys(validatedData).filter(
          key => validatedData[key as keyof ExifData] != null
        ).length,
      });

      return validatedData;
    } catch (error) {
      // 优雅处理错误，不影响图片上传流程
      console.warn('⚠️ ExifService: EXIF提取失败', {
        error: error instanceof Error ? error.message : String(error),
        bufferSize: buffer?.length || 0,
      });
      return null;
    }
  }

  /**
   * 将exif-parser的输出映射到ExifDataSchema格式
   *
   * @param tags exif-parser提取的原始标签数据
   * @returns ExifData 映射后的EXIF数据
   */
  private mapExifParserToSchema(tags: Record<string, any>): ExifData {
    return {
      make: this.formatExifValue(tags.Make),
      model: this.formatExifValue(tags.Model),
      lens_make: this.formatExifValue(tags.LensMake),
      bits_per_sample: this.formatExifValue(tags.BitsPerSample),
      date_time_original: this.formatExifValue(tags.DateTimeOriginal),
      exposure_time: this.formatExifValue(tags.ExposureTime),
      f_number: this.formatExifValue(tags.FNumber),
      exposure_program: this.formatExifValue(tags.ExposureProgram),
      iso_speed_rating: this.formatExifValue(tags.ISO || tags.ISOSpeedRatings),
      focal_length: this.formatExifValue(tags.FocalLength),
      lens_specification: this.formatExifValue(tags.LensSpecification),
      lens_model: this.formatExifValue(tags.LensModel),
      exposure_mode: this.formatExifValue(tags.ExposureMode),
      cfa_pattern: this.formatExifValue(tags.CFAPattern),
      color_space: this.formatExifValue(tags.ColorSpace),
      white_balance: this.formatExifValue(tags.WhiteBalance),
    };
  }

  /**
   * 格式化EXIF值
   *
   * @param value exif-parser返回的原始值
   * @returns string | null 格式化后的字符串值，无效值返回null
   */
  private formatExifValue(value: any): string | null {
    try {
      // 处理null和undefined
      if (value === null || value === undefined) {
        return null;
      }

      // exif-parser直接返回值，不像exifreader那样包装在对象中
      if (typeof value === 'string' || typeof value === 'number') {
        const stringValue = String(value);
        return stringValue === '' ? null : stringValue;
      }

      // 处理数组（某些EXIF字段可能是数组）
      if (Array.isArray(value)) {
        const arrayString = value.join(', ');
        return arrayString === '' ? null : arrayString;
      }

      // 处理对象（如果有的话）
      if (typeof value === 'object') {
        const objectString = JSON.stringify(value);
        return objectString === '{}' ? null : objectString;
      }

      // 其他类型视为无效
      return null;
    } catch (error) {
      console.warn('⚠️ ExifService: 格式化EXIF值失败', { value, error });
      return null;
    }
  }

  /**
   * 验证EXIF数据格式
   *
   * @param data 要验证的EXIF数据
   * @returns ExifData 验证后的数据
   * @throws 如果数据格式不符合schema
   */
  private validateExifData(data: any): ExifData {
    try {
      return validateExifData(data);
    } catch (error) {
      console.error('❌ ExifService: EXIF数据验证失败', { data, error });
      throw new Error(
        `EXIF数据格式无效: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 检查EXIF数据是否包含有效内容
   *
   * @param data 要检查的EXIF数据
   * @returns boolean 是否包含有效数据
   */
  private hasValidExifData(data: ExifData): boolean {
    if (!data || typeof data !== 'object') {
      return false;
    }

    // 检查是否至少有一个非空字段
    return Object.values(data).some(value => value !== null && value !== undefined && value !== '');
  }

  /**
   * 获取支持的图片格式列表
   *
   * @returns string[] 支持的图片格式扩展名
   */
  static getSupportedFormats(): string[] {
    // exif-parser主要支持JPEG和TIFF格式
    return ['.jpg', '.jpeg', '.tiff', '.tif'];
  }

  /**
   * 检查文件格式是否支持EXIF提取
   *
   * @param mimeType 文件的MIME类型
   * @returns boolean 是否支持
   */
  static isSupportedFormat(mimeType: string): boolean {
    const supportedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/tiff'
    ];

    return supportedMimeTypes.includes(mimeType.toLowerCase());
  }

  /**
   * 获取EXIF提取的统计信息（用于调试和监控）
   *
   * @param exifData 提取的EXIF数据
   * @returns object 统计信息
   */
  static getExifStats(exifData: ExifData | null): {
    hasExif: boolean;
    fieldsCount: number;
    mainFields: string[];
  } {
    if (!exifData) {
      return {
        hasExif: false,
        fieldsCount: 0,
        mainFields: [],
      };
    }

    const validFields = Object.entries(exifData)
      .filter(([, value]) => value !== null && value !== undefined && value !== '')
      .map(([key]) => key);

    // 主要字段（相机信息）
    const mainFields = ['make', 'model', 'date_time_original', 'iso_speed_rating'].filter(
      field => exifData[field as keyof ExifData] != null
    );

    return {
      hasExif: validFields.length > 0,
      fieldsCount: validFields.length,
      mainFields,
    };
  }

  /**
   * 清理和标准化EXIF日期时间格式
   *
   * @param dateTimeString EXIF中的日期时间字符串
   * @returns string | null 标准化后的ISO日期字符串，失败返回null
   */
  static normalizeDateTime(dateTimeString: string | null): string | null {
    if (!dateTimeString) {
      return null;
    }

    try {
      // EXIF日期格式通常是 "YYYY:MM:DD HH:mm:ss"
      // 转换为ISO格式 "YYYY-MM-DDTHH:mm:ss"
      const normalized = dateTimeString.replace(/^(\d{4}):(\d{2}):(\d{2})/, '$1-$2-$3');

      // 验证日期是否有效
      const date = new Date(normalized);
      if (isNaN(date.getTime())) {
        return null;
      }

      return date.toISOString();
    } catch (error) {
      console.warn('⚠️ ExifService: 日期时间格式化失败', { dateTimeString, error });
      return null;
    }
  }

  /**
   * 提取相机和镜头的基本信息摘要
   *
   * @param exifData EXIF数据
   * @returns object 相机和镜头摘要信息
   */
  static getCameraSummary(exifData: ExifData | null): {
    camera: string | null;
    lens: string | null;
    settings: string | null;
  } {
    if (!exifData) {
      return { camera: null, lens: null, settings: null };
    }

    // 相机信息
    const camera = [exifData.make, exifData.model].filter(Boolean).join(' ') || null;

    // 镜头信息
    const lens =
      exifData.lens_model ||
      [exifData.lens_make, exifData.lens_specification].filter(Boolean).join(' ') ||
      null;

    // 拍摄设置
    const settings =
      [
        exifData.f_number ? `f/${exifData.f_number}` : null,
        exifData.exposure_time ? `${exifData.exposure_time}s` : null,
        exifData.iso_speed_rating ? `ISO ${exifData.iso_speed_rating}` : null,
        exifData.focal_length ? `${exifData.focal_length}` : null,
      ]
        .filter(Boolean)
        .join(' • ') || null;

    return { camera, lens, settings };
  }
}
