import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 缩略图选项接口
 */
export interface ThumbnailOptions {
  width?: number;           // 缩略图宽度（默认300）
  height?: number;          // 缩略图高度（默认300）
  quality?: number;         // 压缩质量0-100（默认80）
  format?: 'jpeg' | 'png' | 'webp';  // 输出格式（默认jpeg）
  fit?: 'cover' | 'contain' | 'fill'; // 缩放模式（默认cover）
  background?: string;      // 背景色（默认白色）
}

/**
 * 缩略图生成结果接口
 */
export interface ThumbnailResult {
  buffer: Buffer;           // 缩略图数据
  originalSize: number;     // 原图大小
  thumbnailSize: number;    // 缩略图大小
  compressionRatio: number; // 压缩率
  processingTime: number;   // 处理时间ms
  format: string;           // 输出格式
  dimensions: { width: number; height: number }; // 尺寸
}

/**
 * 平台能力接口
 */
export interface PlatformCapabilities {
  jimpAvailable: boolean;  // Jimp库是否可用
  platform: 'win32' | 'linux'; // 平台类型
  fallbackReason?: string;  // Jimp不可用的原因
}

/**
 * 跨平台缩略图生成服务
 */
export class ThumbnailService {
  private platformCapabilities: PlatformCapabilities;
  
  constructor() {
    this.platformCapabilities = this.detectPlatformCapabilities();
    this.logPlatformInfo();
  }
  
  /**
   * 检测平台能力
   */
  private detectPlatformCapabilities(): PlatformCapabilities {
    const platform = os.platform() as 'win32' | 'linux';
    let jimpAvailable = false;
    let fallbackReason: string | undefined;

    try {
      // 动态导入Jimp库
      const Jimp = require('jimp');
      // 测试Jimp库基本功能
      if (Jimp && typeof Jimp.read === 'function') {
        jimpAvailable = true;
        console.log('✅ Jimp库可用');
      }
    } catch (error) {
      jimpAvailable = false;
      fallbackReason = `Jimp库不可用: ${error instanceof Error ? error.message : String(error)}`;
      console.warn('⚠️ Jimp库不可用:', fallbackReason);
    }

    return {
      jimpAvailable,
      platform,
      fallbackReason
    };
  }

  
  /**
   * 记录平台信息
   */
  private logPlatformInfo(): void {
    console.log('🔍 缩略图服务平台信息:', {
      platform: this.platformCapabilities.platform,
      jimpAvailable: this.platformCapabilities.jimpAvailable,
      fallbackReason: this.platformCapabilities.fallbackReason
    });
  }
  
  /**
   * 从Buffer生成缩略图（跨平台兼容）
   */
  async generateFromBuffer(buffer: Buffer, options: ThumbnailOptions = {}): Promise<ThumbnailResult> {
    const opts = { ...ThumbnailService.getDefaultOptions(), ...options };

    // 只使用Jimp库，确保压缩质量
    if (!this.platformCapabilities.jimpAvailable) {
      throw new Error(`Jimp库不可用，无法生成缩略图。原因: ${this.platformCapabilities.fallbackReason}`);
    }

    return await this.generateWithJimp(buffer, opts);
  }
  
  /**
   * 从文件路径生成缩略图（跨平台兼容）
   */
  async generateFromFile(inputPath: string, outputPath: string, options: ThumbnailOptions = {}): Promise<ThumbnailResult> {
    const startTime = Date.now();
    
    try {
      // 读取输入文件
      const inputBuffer = fs.readFileSync(inputPath);
      
      // 生成缩略图
      const result = await this.generateFromBuffer(inputBuffer, options);
      
      // 写入输出文件
      fs.writeFileSync(outputPath, result.buffer);
      
      // 更新处理时间
      result.processingTime = Date.now() - startTime;
      
      return result;
    } catch (error) {
      console.error('❌ 文件缩略图生成失败:', error);
      throw new Error(`文件缩略图生成失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Jimp库实现（主要方案）
   */
  private async generateWithJimp(buffer: Buffer, options: ThumbnailOptions): Promise<ThumbnailResult> {
    const startTime = Date.now();

    try {
      const Jimp = require('jimp');

      // 获取原图信息
      const originalSize = buffer.length;

      // 读取图片
      const image = await Jimp.read(buffer);

      // 获取原图尺寸
      const originalWidth = image.getWidth();
      const originalHeight = image.getHeight();

      // 计算缩放尺寸（保持宽高比）
      const targetWidth = options.width || 300;
      const targetHeight = options.height || 300;

      let newWidth, newHeight;
      if (options.fit === 'contain') {
        // 保持完整图片，可能有空白
        const ratio = Math.min(targetWidth / originalWidth, targetHeight / originalHeight);
        newWidth = Math.round(originalWidth * ratio);
        newHeight = Math.round(originalHeight * ratio);
      } else {
        // 默认cover模式：填满目标尺寸，可能裁剪
        const ratio = Math.max(targetWidth / originalWidth, targetHeight / originalHeight);
        newWidth = Math.round(originalWidth * ratio);
        newHeight = Math.round(originalHeight * ratio);
      }

      // 缩放图片
      image.resize(newWidth, newHeight);

      // 如果是cover模式且尺寸不匹配，进行裁剪
      if (options.fit !== 'contain' && (newWidth !== targetWidth || newHeight !== targetHeight)) {
        const cropX = Math.max(0, Math.round((newWidth - targetWidth) / 2));
        const cropY = Math.max(0, Math.round((newHeight - targetHeight) / 2));
        image.crop(cropX, cropY, targetWidth, targetHeight);
      }

      // 设置质量
      const quality = options.quality || 80;
      image.quality(quality);

      // 转换为Buffer
      const thumbnailBuffer = await image.getBufferAsync(Jimp.MIME_JPEG);

      const processingTime = Date.now() - startTime;
      const compressionRatio = (originalSize - thumbnailBuffer.length) / originalSize;

      return {
        buffer: thumbnailBuffer,
        originalSize,
        thumbnailSize: thumbnailBuffer.length,
        compressionRatio: Math.max(0, compressionRatio), // 确保不为负数
        processingTime,
        format: 'jpeg', // Jimp输出JPEG格式
        dimensions: { width: targetWidth, height: targetHeight }
      };
    } catch (error) {
      console.error('❌ Jimp缩略图生成失败:', error);
      throw new Error(`Jimp缩略图生成失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  

  
  /**
   * 批量生成缩略图
   */
  async generateBatch(inputs: Array<{buffer: Buffer, options?: ThumbnailOptions}>): Promise<ThumbnailResult[]> {
    const results: ThumbnailResult[] = [];
    
    for (const input of inputs) {
      try {
        const result = await this.generateFromBuffer(input.buffer, input.options);
        results.push(result);
      } catch (error) {
        console.error('❌ 批量处理中的单个项目失败:', error);
        throw error; // 确保压缩质量，直接抛出错误
      }
    }
    
    return results;
  }
  
  /**
   * 获取图片信息
   */
  async getImageInfo(buffer: Buffer): Promise<{width: number, height: number, format: string, size: number}> {
    try {
      if (!this.platformCapabilities.jimpAvailable) {
        throw new Error(`Jimp库不可用，无法获取图片信息。原因: ${this.platformCapabilities.fallbackReason}`);
      }

      const Jimp = require('jimp');
      const image = await Jimp.read(buffer);

      // 从MIME类型推断格式
      let format = 'unknown';
      if (image._originalMime) {
        if (image._originalMime.includes('jpeg')) format = 'jpeg';
        else if (image._originalMime.includes('png')) format = 'png';
        else if (image._originalMime.includes('webp')) format = 'webp';
        else if (image._originalMime.includes('gif')) format = 'gif';
      }

      return {
        width: image.getWidth(),
        height: image.getHeight(),
        format: format,
        size: buffer.length
      };
    } catch (error) {
      console.error('❌ 获取图片信息失败:', error);
      throw new Error(`获取图片信息失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * 获取平台能力
   */
  getPlatformCapabilities(): PlatformCapabilities {
    return { ...this.platformCapabilities };
  }
  
  /**
   * 验证图片格式
   */
  static isSupportedFormat(mimeType: string): boolean {
    const supportedMimeTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/webp'
    ];
    return supportedMimeTypes.includes(mimeType.toLowerCase());
  }
  
  /**
   * 获取默认配置
   */
  static getDefaultOptions(): ThumbnailOptions {
    return {
      width: 300,
      height: 300,
      quality: 80,
      format: 'jpeg',
      fit: 'cover',
      background: '#ffffff'
    };
  }
}
