import fs from 'fs/promises';
import path from 'path';
import { pinyin } from 'pinyin-pro';
import type { 
  SpeciesDictionaryEntry, 
  SpeciesDictionarySearchOptions, 
  SpeciesDictionarySearchResult, 
  SpeciesByGroupOptions 
} from '../../schemas/speciesDictionary';
import {
  validateSpeciesDictionaryEntry,
  validateSpeciesDictionarySearchOptions
} from '../../schemas/speciesDictionary';

/**
 * 物种查询服务
 * 
 * 功能特性:
 * - 内存缓存 + 多维索引
 * - 支持精确匹配、前缀匹配、拼音匹配、N-gram匹配
 * - 高性能查询（目标: 首次加载 < 1000ms，查询响应 < 50ms）
 * - 支持按科属筛选
 */
export class SpeciesService {
  private species: SpeciesDictionaryEntry[] = [];
  private loaded: boolean = false;
  private dataPath: string;

  // 多维索引
  private exactNameIndex: Map<string, SpeciesDictionaryEntry> = new Map();
  private prefixIndex: Map<string, SpeciesDictionaryEntry[]> = new Map();
  private pinyinIndex: Map<string, SpeciesDictionaryEntry[]> = new Map();
  private ngramIndex: Map<string, SpeciesDictionaryEntry[]> = new Map();
  private familyIndex: Map<string, SpeciesDictionaryEntry[]> = new Map();
  private genusIndex: Map<string, SpeciesDictionaryEntry[]> = new Map();

  // 性能监控
  private loadTime: number = 0;
  private indexBuildTime: number = 0;

  constructor(customDataPath?: string) {
    if (customDataPath) {
      this.dataPath = customDataPath;
    } else {
      // 根据环境确定正确的数据文件路径
      this.dataPath = this.resolveDataPath();
    }
    
    console.log('📊 SpeciesService初始化');
    console.log('🔍 数据文件路径:', this.dataPath);
    console.log('📂 process.cwd():', process.cwd());
    console.log('📁 __dirname:', __dirname);
    console.log('🏠 process.resourcesPath:', process.resourcesPath || 'undefined');
    console.log('🔧 app.isPackaged:', process.env.NODE_ENV === 'production');
  }

  /**
   * 解析数据文件路径，确保在不同环境下都能正确找到文件
   */
  private resolveDataPath(): string {
    const fileName = '物种列表.json';
    
    // 可能的路径列表，按优先级排序
    const possiblePaths = [
      // 1. 开发环境：项目根目录下的public/data
      path.join(process.cwd(), 'public/data', fileName),
      
      // 2. 打包环境：相对于main.js的路径（通常在resources/app.asar/dist-electron/main/）
      path.join(__dirname, '../../../public/data', fileName),
      path.join(__dirname, '../../public/data', fileName),
      path.join(__dirname, '../public/data', fileName),
      
      // 3. Electron资源路径
      ...(process.resourcesPath ? [
        path.join(process.resourcesPath, 'app.asar/public/data', fileName),
        path.join(process.resourcesPath, 'public/data', fileName),
      ] : []),
      
      // 4. 相对于进程工作目录的备选路径
      path.join(process.cwd(), 'resources/app.asar/public/data', fileName),
    ];

    // console.log('🔍 尝试查找数据文件，候选路径:');
    // possiblePaths.forEach((p, i) => console.log(`  ${i + 1}. ${p}`));

    // 返回第一个路径作为默认值，实际验证在ensureLoaded中进行
    return possiblePaths[0];
  }

  /**
   * 尝试从多个可能的路径读取数据文件
   */
  private async readDataFileWithFallback(): Promise<string> {
    const fileName = '物种列表.json';
    
    // 所有可能的路径，根据实际的构建配置调整
    const possiblePaths = [
      // 当前设置的路径
      this.dataPath,
      
      // 开发环境路径
      path.join(process.cwd(), 'public/data', fileName),
      
      // 构建后的数据文件路径（根据electron.vite.config.ts配置）
      path.join(__dirname, '../renderer/data', fileName),
      path.join(__dirname, '../../dist-electron/renderer/data', fileName),
      
      // 打包环境的各种可能路径
      path.join(__dirname, '../../../public/data', fileName),
      path.join(__dirname, '../../public/data', fileName), 
      path.join(__dirname, '../public/data', fileName),
      
      // Electron资源路径（在app.asar内）
      ...(process.resourcesPath ? [
        path.join(process.resourcesPath, 'app.asar/dist-electron/renderer/data', fileName),
        path.join(process.resourcesPath, 'app.asar/public/data', fileName),
        path.join(process.resourcesPath, 'public/data', fileName),
      ] : []),
      
      // 其他备选路径
      path.join(process.cwd(), 'resources/app.asar/public/data', fileName),
      path.join(process.cwd(), 'resources/app.asar/dist-electron/renderer/data', fileName),
    ];

    // 去重
    const uniquePaths = [...new Set(possiblePaths)];
    
    for (let i = 0; i < uniquePaths.length; i++) {
      const currentPath = uniquePaths[i];
      
      try {
        const content = await fs.readFile(currentPath, 'utf-8');
        console.log(`✅ 成功读取数据文件: ${currentPath}`);
        
        // 更新dataPath为成功的路径
        if (this.dataPath !== currentPath) {
          console.log(`🔄 更新数据文件路径: ${this.dataPath} -> ${currentPath}`);
          this.dataPath = currentPath;
        }
        
        return content;
      } catch (error) {
        // 只在最后一次尝试失败时输出详细错误
        if (i === uniquePaths.length - 1) {
          console.error(`❌ 所有路径尝试失败，最后错误: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }
    
    // 所有路径都失败了
    throw new Error(`无法在以下任何路径找到数据文件 ${fileName}:\n${uniquePaths.map((p, i) => `  ${i + 1}. ${p}`).join('\n')}`);
  }

  /**
   * 确保数据已加载（懒加载）
   */
  async ensureLoaded(): Promise<boolean> {
    if (this.loaded) {
      return true;
    }

    console.log('📥 开始加载物种数据...');
    const startTime = Date.now();

    try {
      // 1. 验证并尝试读取JSON文件
      const fileContent = await this.readDataFileWithFallback();
      const rawData = JSON.parse(fileContent);
      
      // 2. 验证数据格式
      if (!Array.isArray(rawData)) {
        throw new Error('物种数据必须是数组格式');
      }

      // 3. 验证每条记录
      this.species = rawData.map((item, index) => {
        try {
          return validateSpeciesDictionaryEntry(item);
        } catch (error) {
          console.error(`第${index + 1}条记录验证失败:`, item, error);
          throw new Error(`数据验证失败，第${index + 1}条记录格式错误`);
        }
      });

      this.loadTime = Date.now() - startTime;
      console.log(`✅ 物种数据加载完成: ${this.species.length}条记录，耗时: ${this.loadTime}ms`);

      // 4. 构建索引
      await this.buildIndexes();
      
      this.loaded = true;
      return true;

    } catch (error) {
      console.error('❌ 物种数据加载失败:', error);
      throw new Error(`加载物种数据失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 构建多维索引
   */
  private async buildIndexes(): Promise<void> {
    console.log('🔧 开始构建物种索引...');
    const startTime = Date.now();

    // 清空现有索引
    this.clearIndexes();

    for (const species of this.species) {
      const speciesName = species.种;

      // 1. 精确匹配索引
      this.exactNameIndex.set(speciesName, species);

      // 2. 前缀索引
      this.buildPrefixIndex(speciesName, species);

      // 3. 拼音索引
      this.buildPinyinIndex(speciesName, species);

      // 4. N-gram索引
      this.buildNgramIndex(speciesName, species);

      // 5. 科属索引
      this.addToMapArray(this.familyIndex, species.科, species);
      this.addToMapArray(this.genusIndex, species.属, species);
    }

    this.indexBuildTime = Date.now() - startTime;
    console.log(`✅ 索引构建完成，耗时: ${this.indexBuildTime}ms`);
    console.log(`📈 索引统计: 精确=${this.exactNameIndex.size}, 前缀=${this.prefixIndex.size}, 拼音=${this.pinyinIndex.size}, N-gram=${this.ngramIndex.size}`);
  }

  /**
   * 构建前缀索引
   */
  private buildPrefixIndex(text: string, species: SpeciesDictionaryEntry): void {
    // 为每个字符前缀建立索引
    for (let i = 1; i <= text.length; i++) {
      const prefix = text.substring(0, i);
      this.addToMapArray(this.prefixIndex, prefix, species);
    }
  }

  /**
   * 构建拼音索引
   */
  private buildPinyinIndex(text: string, species: SpeciesDictionaryEntry): void {
    try {
      // 全拼音索引
      const fullPinyin = pinyin(text, { toneType: 'none', type: 'array' }).join('');
      if (fullPinyin) {
        // 全拼音的所有前缀
        for (let i = 1; i <= fullPinyin.length; i++) {
          const prefix = fullPinyin.substring(0, i).toLowerCase();
          this.addToMapArray(this.pinyinIndex, prefix, species);
        }
      }

      // 首字母拼音索引
      const firstLetters = pinyin(text, { pattern: 'first', toneType: 'none' });
      if (firstLetters) {
        // 清理空格并转换为小写
        const cleanFirstLetters = firstLetters.replace(/\s+/g, '').toLowerCase();
        // 首字母的所有前缀
        for (let i = 1; i <= cleanFirstLetters.length; i++) {
          const prefix = cleanFirstLetters.substring(0, i);
          this.addToMapArray(this.pinyinIndex, prefix, species);
        }
      }
    } catch (error) {
      console.warn('拼音索引构建失败:', text, error);
      // 拼音失败不影响其他索引
    }
  }

  /**
   * 构建N-gram索引
   */
  private buildNgramIndex(text: string, species: SpeciesDictionaryEntry, n: number = 2): void {
    // 单字符索引
    for (const char of text) {
      this.addToMapArray(this.ngramIndex, char, species);
    }

    // N-gram子串索引
    for (let i = 0; i <= text.length - n; i++) {
      const ngram = text.substring(i, i + n);
      this.addToMapArray(this.ngramIndex, ngram, species);
    }
  }

  /**
   * 通用的Map数组添加方法
   */
  private addToMapArray<T>(map: Map<string, T[]>, key: string, value: T): void {
    if (!map.has(key)) {
      map.set(key, []);
    }
    map.get(key)!.push(value);
  }

  /**
   * 搜索物种
   */
  async searchSpecies(options: SpeciesDictionarySearchOptions): Promise<SpeciesDictionarySearchResult> {
    await this.ensureLoaded();

    const startTime = Date.now();

    // 预处理参数，确保limit在合理范围内
    const preprocessedOptions = {
      ...options,
      limit: Math.min(Math.max(options.limit || 20, 1), 100) // 限制在1-100之间
    };

    const validatedOptions = validateSpeciesDictionarySearchOptions(preprocessedOptions);
    const { query, limit } = validatedOptions;

    console.log(`🔍 搜索物种: "${query}", limit: ${limit}`);

    const resultSet = new Set<SpeciesDictionaryEntry>();
    const queryLower = query.toLowerCase();

    // 1. 精确匹配（最高优先级）
    const exactMatch = this.exactNameIndex.get(query);
    if (exactMatch) {
      resultSet.add(exactMatch);
    }

    // 2. 前缀匹配（高优先级）
    const prefixMatches = this.prefixIndex.get(query) || [];
    prefixMatches.forEach(species => resultSet.add(species));

    // 3. 拼音匹配（中优先级）
    const pinyinMatches = this.pinyinIndex.get(queryLower) || [];
    pinyinMatches.forEach(species => resultSet.add(species));

    // 4. N-gram匹配（低优先级）
    const ngramMatches = this.ngramIndex.get(query) || [];
    ngramMatches.forEach(species => resultSet.add(species));

    // 转换为数组并限制结果数量
    const results = Array.from(resultSet).slice(0, Math.min(limit, 100)); // 最大100条

    const searchTime = Date.now() - startTime;
    console.log(`✅ 搜索完成: 找到${results.length}条结果，耗时: ${searchTime}ms`);

    return {
      species: results,
      total: results.length,
      query,
      searchTime,
    };
  }

  /**
   * 获取物种详细信息
   */
  async getSpeciesInfo(speciesName: string): Promise<SpeciesDictionaryEntry | null> {
    await this.ensureLoaded();

    const result = this.exactNameIndex.get(speciesName);
    return result || null;
  }

  /**
   * 按科查询物种
   */
  async getSpeciesByFamily(familyName: string, options?: SpeciesByGroupOptions): Promise<SpeciesDictionaryEntry[]> {
    await this.ensureLoaded();

    const limit = options?.limit || 50;
    const species = this.familyIndex.get(familyName) || [];

    return species.slice(0, Math.min(limit, 500)); // 最大500条
  }

  /**
   * 按属查询物种
   */
  async getSpeciesByGenus(genusName: string, options?: SpeciesByGroupOptions): Promise<SpeciesDictionaryEntry[]> {
    await this.ensureLoaded();

    const limit = options?.limit || 50;
    const species = this.genusIndex.get(genusName) || [];

    return species.slice(0, Math.min(limit, 500)); // 最大500条
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    return {
      loaded: this.loaded,
      totalSpecies: this.species.length,
      totalFamilies: this.familyIndex.size,
      totalGenera: this.genusIndex.size,
      loadTime: this.loadTime,
      indexBuildTime: this.indexBuildTime,
      memoryUsage: {
        exactIndex: this.exactNameIndex.size,
        prefixIndex: this.prefixIndex.size,
        pinyinIndex: this.pinyinIndex.size,
        ngramIndex: this.ngramIndex.size,
      },
    };
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    console.log('🧹 清理物种服务缓存...');

    this.species = [];
    this.loaded = false;
    this.loadTime = 0;
    this.indexBuildTime = 0;

    this.clearIndexes();

    console.log('✅ 缓存清理完成');
  }

  /**
   * 清空所有索引
   */
  private clearIndexes(): void {
    this.exactNameIndex.clear();
    this.prefixIndex.clear();
    this.pinyinIndex.clear();
    this.ngramIndex.clear();
    this.familyIndex.clear();
    this.genusIndex.clear();
  }
}
