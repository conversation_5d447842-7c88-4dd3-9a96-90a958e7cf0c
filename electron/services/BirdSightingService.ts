import { DatabaseManager } from '../database';
import Database from 'better-sqlite3';
import fs from 'fs';
import * as XLSX from 'xlsx';

// 类型定义
export interface ExcelBirdData {
  鸟种编号: string;
  中文名: string;
  拉丁名: string;
  英文名: string;
  目: string;
  科: string;
  定点记报告: string;
  随手记报告: string;
  记录次数: string;
  记录时间: string;
}

export interface BirdSightingRecord {
  id: string;
  chineseName: string;
  latinName: string;
  englishName: string;
  order: string;
  family: string;
  count: number;
  recordDate: string;
}

export interface ParseResult {
  success: boolean;
  data?: BirdSightingRecord[];
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface DatabaseResult {
  success: boolean;
  data?: any;
  error?: string;
}

export interface BirdSightingStats {
  totalRecords: number;
  totalSpecies: number;
  totalObservations: number;
  latestRecord?: string;
}

export interface UploadResult {
  success: boolean;
  data?: {
    importedCount: number;
    totalCount: number;
  };
  error?: string;
}

export class BirdSightingService {
  constructor(private dbManager: DatabaseManager) {}

  /**
   * 解析Excel文件并返回鸟类观测记录
   */
  async parseExcelFile(filePath: string): Promise<ParseResult> {
    try {
      console.log('📋 开始解析Excel文件:', filePath);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: '文件不存在: ' + filePath
        };
      }

      // 读取Excel文件
      let workbook: XLSX.WorkBook;
      try {
        // 先读取文件内容为Buffer，然后解析
        const fileBuffer = fs.readFileSync(filePath);
        workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      } catch (error) {
        console.error('❌ Excel文件读取失败:', error);
        return {
          success: false,
          error: 'Excel文件解析失败: ' + (error instanceof Error ? error.message : String(error))
        };
      }

      // 检查是否有鸟种数据导出工作表
      const sheetName = '鸟种数据导出';
      if (!workbook.SheetNames.includes(sheetName)) {
        return {
          success: false,
          error: '未找到鸟种数据导出工作表，请确认文件格式正确'
        };
      }

      // 获取工作表数据
      const worksheet = workbook.Sheets[sheetName];
      const excelData: ExcelBirdData[] = XLSX.utils.sheet_to_json(worksheet);

      if (!excelData || excelData.length === 0) {
        return {
          success: false,
          error: 'Excel文件中没有数据'
        };
      }

      // 转换数据格式
      const timelineData: BirdSightingRecord[] = [];
      const errors: string[] = [];

      for (let i = 0; i < excelData.length; i++) {
        try {
          const record = this.convertExcelDataToTimelineFormat(excelData[i]);
          
          // 验证转换后的数据
          const validation = this.validateBirdSightingRecord(record);
          if (validation.isValid) {
            timelineData.push(record);
          } else {
            errors.push(`第${i + 1}行数据验证失败: ${validation.errors.join(', ')}`);
          }
        } catch (error) {
          errors.push(`第${i + 1}行数据转换失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      if (timelineData.length === 0) {
        return {
          success: false,
          error: '没有有效的数据可以导入。错误信息: ' + errors.join('; ')
        };
      }

      console.log(`✅ Excel文件解析成功，共解析 ${timelineData.length} 条有效记录`);
      
      if (errors.length > 0) {
        console.warn('⚠️ 部分数据存在问题:', errors);
      }

      return {
        success: true,
        data: timelineData
      };

    } catch (error) {
      console.error('❌ 解析Excel文件时发生错误:', error);
      return {
        success: false,
        error: '解析Excel文件失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * 将Excel数据转换为时间线格式
   */
  convertExcelDataToTimelineFormat(excelData: ExcelBirdData): BirdSightingRecord {
    // 安全地转换数字字段
    const parseNumber = (value: string | number): number => {
      if (typeof value === 'number') return value;
      const parsed = parseInt(String(value), 10);
      return isNaN(parsed) ? 0 : parsed;
    };

    return {
      id: String(excelData.鸟种编号 || ''),
      chineseName: String(excelData.中文名 || ''),
      latinName: String(excelData.拉丁名 || ''),
      englishName: String(excelData.英文名 || ''),
      order: String(excelData.目 || ''),
      family: String(excelData.科 || ''),
      count: parseNumber(excelData.记录次数),
      recordDate: this.formatRecordDate(String(excelData.记录时间 || ''))
    };
  }

  /**
   * 格式化记录日期为YYYY/M/D格式
   */
  formatRecordDate(dateString: string): string {
    try {
      // 处理各种可能的日期格式
      let cleanDateString = dateString.trim();
      
      // 移除时间部分 (如果存在)
      if (cleanDateString.includes(' ')) {
        cleanDateString = cleanDateString.split(' ')[0];
      }

      // 解析日期
      let date: Date;
      
      if (cleanDateString.includes('-')) {
        // 格式: YYYY-MM-DD
        date = new Date(cleanDateString);
      } else if (cleanDateString.includes('/')) {
        // 格式: YYYY/MM/DD 或 YYYY/M/D
        date = new Date(cleanDateString);
      } else {
        // 其他格式，尝试直接解析
        date = new Date(cleanDateString);
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('⚠️ 无法解析日期格式:', dateString);
        return new Date().toLocaleDateString('zh-CN').replace(/\//g, '/');
      }

      // 返回YYYY/M/D格式
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // getMonth()返回0-11
      const day = date.getDate();

      return `${year}/${month}/${day}`;
    } catch (error) {
      console.warn('⚠️ 日期格式化失败:', dateString, error);
      return new Date().toLocaleDateString('zh-CN').replace(/\//g, '/');
    }
  }

  /**
   * 验证鸟类观测记录数据
   */
  validateBirdSightingRecord(record: BirdSightingRecord): ValidationResult {
    const errors: string[] = [];

    // 验证必要字段
    if (!record.id || record.id.trim() === '') {
      errors.push('鸟种编号不能为空');
    }

    if (!record.chineseName || record.chineseName.trim() === '') {
      errors.push('中文名不能为空');
    }

    if (!record.latinName || record.latinName.trim() === '') {
      errors.push('拉丁名不能为空');
    }

    if (!record.englishName || record.englishName.trim() === '') {
      errors.push('英文名不能为空');
    }

    if (!record.order || record.order.trim() === '') {
      errors.push('目不能为空');
    }

    if (!record.family || record.family.trim() === '') {
      errors.push('科不能为空');
    }

    if (!record.recordDate || record.recordDate.trim() === '') {
      errors.push('记录时间不能为空');
    }

    // 验证数据类型和范围
    if (typeof record.count !== 'number' || record.count < 0) {
      errors.push('记录次数必须是非负整数');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 插入单条鸟类观测记录到数据库
   */
  async insertBirdSightingRecord(record: BirdSightingRecord): Promise<DatabaseResult> {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;
      const now = new Date().toISOString();

      const insert = db.prepare(`
        INSERT INTO bird_sightings (id, chinese_name, latin_name, english_name, bird_order, family, count, record_date, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insert.run(
        record.id,
        record.chineseName,
        record.latinName,
        record.englishName,
        record.order,
        record.family,
        record.count,
        record.recordDate,
        now,
        now
      );

      console.log('✅ 观鸟记录插入成功:', record.id, record.chineseName);

      return {
        success: true,
        data: record
      };
    } catch (error) {
      console.error('❌ 插入观鸟记录失败:', error);
      return {
        success: false,
        error: '插入记录失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * 获取所有鸟类观测记录
   */
  async getAllBirdSightingRecords(): Promise<DatabaseResult> {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;

      const query = `
        SELECT id, chinese_name as chineseName, latin_name as latinName, english_name as englishName,
               bird_order as 'order', family, count, record_date as recordDate
        FROM bird_sightings
        ORDER BY record_date DESC, created_at DESC
      `;

      const records = db.prepare(query).all() as BirdSightingRecord[];

      console.log(`📊 获取观鸟记录成功，共 ${records.length} 条记录`);

      return {
        success: true,
        data: records
      };
    } catch (error) {
      console.error('❌ 获取观鸟记录失败:', error);
      return {
        success: false,
        error: '获取记录失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * 覆盖所有鸟类观测记录（清空后重新插入）
   */
  async replaceAllBirdSightingRecords(records: BirdSightingRecord[]): Promise<DatabaseResult> {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;

      // 使用事务确保数据一致性
      const replaceTransaction = db.transaction(() => {
        // 清空现有数据
        db.prepare('DELETE FROM bird_sightings').run();

        // 插入新数据
        const now = new Date().toISOString();
        const insert = db.prepare(`
          INSERT INTO bird_sightings (id, chinese_name, latin_name, english_name, bird_order, family, count, record_date, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        for (const record of records) {
          insert.run(
            record.id,
            record.chineseName,
            record.latinName,
            record.englishName,
            record.order,
            record.family,
            record.count,
            record.recordDate,
            now,
            now
          );
        }
      });

      replaceTransaction();

      console.log(`✅ 观鸟记录覆盖成功，共插入 ${records.length} 条记录`);

      return {
        success: true,
        data: {
          importedCount: records.length,
          totalCount: records.length
        }
      };
    } catch (error) {
      console.error('❌ 覆盖观鸟记录失败:', error);
      return {
        success: false,
        error: '数据插入失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * 获取鸟类观测统计信息
   */
  async getBirdSightingStats(): Promise<DatabaseResult> {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;

      // 获取基本统计信息
      const totalRecords = db.prepare('SELECT COUNT(*) as count FROM bird_sightings').get() as { count: number };
      const totalSpecies = db.prepare('SELECT COUNT(DISTINCT id) as count FROM bird_sightings').get() as { count: number };
      const totalObservations = db.prepare('SELECT SUM(count) as total FROM bird_sightings').get() as { total: number };
      const latestRecord = db.prepare('SELECT MAX(record_date) as latest FROM bird_sightings').get() as { latest: string };

      const stats: BirdSightingStats = {
        totalRecords: totalRecords.count || 0,
        totalSpecies: totalSpecies.count || 0,
        totalObservations: totalObservations.total || 0,
        latestRecord: latestRecord.latest || undefined
      };

      console.log('📊 观鸟统计信息:', stats);

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('❌ 获取观鸟统计失败:', error);
      return {
        success: false,
        error: '获取统计信息失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * 完整的Excel文件上传流程
   */
  async uploadExcelFile(filePath: string): Promise<UploadResult> {
    try {
      console.log('🚀 开始Excel文件上传流程:', filePath);

      // 1. 解析Excel文件
      const parseResult = await this.parseExcelFile(filePath);
      if (!parseResult.success || !parseResult.data) {
        return {
          success: false,
          error: parseResult.error
        };
      }

      // 2. 覆盖数据库中的数据
      const replaceResult = await this.replaceAllBirdSightingRecords(parseResult.data);
      if (!replaceResult.success) {
        return {
          success: false,
          error: replaceResult.error
        };
      }

      console.log('✅ Excel文件上传完成');

      return {
        success: true,
        data: {
          importedCount: parseResult.data.length,
          totalCount: parseResult.data.length
        }
      };
    } catch (error) {
      console.error('❌ Excel文件上传失败:', error);
      return {
        success: false,
        error: '上传失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * 检查是否有观鸟数据
   */
  async hasBirdSightingData(): Promise<boolean> {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;
      const result = db.prepare('SELECT COUNT(*) as count FROM bird_sightings').get() as { count: number };
      return result.count > 0;
    } catch (error) {
      console.error('❌ 检查观鸟数据失败:', error);
      return false;
    }
  }

  /**
   * 清空所有观鸟数据
   */
  async clearAllBirdSightingRecords(): Promise<DatabaseResult> {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;
      db.prepare('DELETE FROM bird_sightings').run();

      console.log('✅ 观鸟数据已清空');

      return {
        success: true,
        data: { message: '数据已清空' }
      };
    } catch (error) {
      console.error('❌ 清空观鸟数据失败:', error);
      return {
        success: false,
        error: '清空数据失败: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }
}