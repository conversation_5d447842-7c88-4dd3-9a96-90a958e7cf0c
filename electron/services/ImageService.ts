import { DatabaseManager } from '../database';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import type { ImageRead, ImageUpdate } from '../../schemas/image';
import { SettingsService } from './SettingsService';
import { OSSService } from './OSSService';
import { ExifService } from './ExifService';
import { ThumbnailService } from './ThumbnailService';

/**
 * 删除结果接口
 */
export interface DeleteResult {
  success: boolean;
  imageId: string;
  error?: string;
  storageDeleted: boolean;
  databaseDeleted: boolean;
  details?: {
    imagePath?: string;
    thumbnailPath?: string;
    storageType: string;
  };
}

/**
 * 批量删除结果接口
 */
export interface BatchDeleteResult {
  totalCount: number;
  successCount: number;
  failedCount: number;
  results: DeleteResult[];
  errors: string[];
}

export class ImageService {
  private imagesDir: string;
  private thumbnailsDir: string;
  private ossService: OSSService;
  private exifService: ExifService;
  private thumbnailService: ThumbnailService;

  constructor(private dbManager: DatabaseManager, private settingsService?: SettingsService) {
    // 设置图片存储目录 - 只使用分类文件夹结构
    if (this.settingsService) {
      this.imagesDir = this.settingsService.getStoragePath();
      this.thumbnailsDir = this.settingsService.getStoragePath();
    } else {
      // 默认路径（测试或未初始化时）
      const userDataPath = app.getPath('userData');
      this.imagesDir = userDataPath;
      this.thumbnailsDir = userDataPath;
    }
    
    // 初始化OSS服务
    this.ossService = new OSSService();
    this.initializeOSSService();
    
    // 初始化EXIF服务
    this.exifService = new ExifService();

    // 初始化缩略图服务
    this.thumbnailService = new ThumbnailService();

    // 目录创建在需要时动态进行
  }

  /**
   * 初始化OSS服务
   */
  private initializeOSSService(): void {
    if (this.settingsService) {
      const ossConfig = this.settingsService.getOSSConfig();
      if (ossConfig) {
        this.ossService.updateConfig(ossConfig);
      }
    }
  }

  /**
   * 获取当前存储类型
   */
  private getStorageType(): 'local' | 'oss' {
    return this.settingsService?.getStorageType() || 'local';
  }

  /**
   * 获取缩略图配置选项
   */
  private getThumbnailOptions(customOptions?: {
    compressionQuality?: number;
    width?: number;
    height?: number;
  }) {
    // 可以从SettingsService获取用户配置，这里使用默认值
    return {
      width: customOptions?.width || 300,
      height: customOptions?.height || 300,
      quality: customOptions?.compressionQuality || 80,
      format: 'jpeg' as const,
      fit: 'cover' as const
    };
  }

  /**
   * 生成OSS文件路径
   */
  private generateOSSPath(categoryId: string, filename: string, isThumb: boolean = false): string {
    const categoryName = this.getCategoryName(categoryId);
    if (isThumb) {
      return `${categoryName}/thumbnails/${filename}`;
    } else {
      return `${categoryName}/${filename}`;
    }
  }


  /**
   * 获取分类名称（用于创建文件夹）
   */
  private getCategoryName(categoryId: string): string {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;
      const category = db.prepare('SELECT name FROM categories WHERE id = ?').get(categoryId) as any;
      
      if (!category) {
        return 'unknown-category';
      }
      
      // 清理分类名称，确保可以作为文件夹名称
      return this.sanitizeFolderName(category.name);
    } catch (error) {
      console.error('❌ 获取分类名称失败:', error);
      return 'unknown-category';
    }
  }

  /**
   * 清理文件夹名称，移除或替换不安全的字符
   */
  private sanitizeFolderName(name: string): string {
    return name
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的字符
      .replace(/\s+/g, '_')           // 替换空格为下划线
      .replace(/_{2,}/g, '_')         // 合并多个下划线
      .replace(/^_+|_+$/g, '')       // 移除开头和结尾的下划线
      .substring(0, 100);             // 限制长度
  }

  /**
   * 验证删除前的条件
   */
  async validateDeleteConditions(imageId: string): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    image?: any;
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];


    // 1. 验证图片是否存在于数据库
    let image: any;
    try {
      image = await this.getImageById(imageId);
      if (!image) {
        errors.push(`数据库中没有找到要删除的图片记录`);
        return { valid: false, errors, warnings };
      }
    } catch (error) {
      errors.push(`数据库查询失败: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, errors, warnings };
    }

    // 2. 检查图片文件是否存在于存储中
    if (image.stored_filename) {
      const storageType = this.getStorageType();

      if (storageType === 'oss') {
        // OSS存储验证
        if (!this.ossService.isConfigured()) {
          warnings.push('OSS未配置，无法验证文件存在性');
        } else {
          try {
            const imageOSSPath = this.generateOSSPath(image.category_id, image.stored_filename, false);
            // 注意：这里我们不实际检查OSS文件存在性，因为这可能很耗时
            // 在实际删除时会处理文件不存在的情况
          } catch (error) {
            warnings.push(`OSS路径生成失败: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
      } else {
        // 本地存储验证
        try {
          const imagePath = this.getImagePath(image.stored_filename);
          const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
          const thumbnailPath = this.getThumbnailPath(thumbnailFilename);

          if (!fs.existsSync(imagePath)) {
            warnings.push(`原图文件不存在: ${imagePath}`);
          } else {
          }

          if (!fs.existsSync(thumbnailPath)) {
            warnings.push(`缩略图文件不存在: ${thumbnailPath}`);
          } else {
          }
        } catch (error) {
          warnings.push(`本地文件检查失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    } else {
      warnings.push('图片没有存储文件名');
    }

    // 3. 验证用户权限（目前跳过，因为没有权限系统）
    // 在未来如果有权限系统，可以在这里添加权限检查

    // 4. 记录删除前的审计日志
    this.logDeleteAudit(imageId, 'VALIDATION', {
      imageTitle: image.title,
      categoryId: image.category_id,
      storageType: this.getStorageType(),
      hasStoredFile: !!image.stored_filename,
      validationErrors: errors,
      validationWarnings: warnings
    });

    const valid = errors.length === 0;

    return {
      valid,
      errors,
      warnings,
      image
    };
  }

  /**
   * 记录删除操作的审计日志
   */
  private logDeleteAudit(imageId: string, operation: 'VALIDATION' | 'DELETE_START' | 'DELETE_SUCCESS' | 'DELETE_FAILED', details: any): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      imageId,
      operation,
      details
    };

    // 这里可以将日志写入文件或数据库
    // 目前只输出到控制台
  }

  /**
   * 获取分类的图片存储目录（直接在分类文件夹下）
   */
  private getCategoryImagePath(categoryId: string): string {
    const categoryName = this.getCategoryName(categoryId);
    const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.imagesDir;
    return path.join(basePath, categoryName);
  }

  /**
   * 获取分类的缩略图存储目录
   */
  private getCategoryThumbnailPath(categoryId: string): string {
    const categoryName = this.getCategoryName(categoryId);
    const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.thumbnailsDir;
    return path.join(basePath, categoryName, 'thumbnails');
  }

  /**
   * 确保分类目录存在
   */
  private ensureCategoryDirectories(categoryId: string): void {
    const imageDir = this.getCategoryImagePath(categoryId);
    const thumbnailDir = this.getCategoryThumbnailPath(categoryId);
    
    if (!fs.existsSync(imageDir)) {
      fs.mkdirSync(imageDir, { recursive: true });
    }
    
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
    }
  }

  async getImagesByCategoryId(categoryId: string): Promise<ImageRead[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const query = `
      SELECT 
        i.*,
        GROUP_CONCAT(t.id) as tag_ids,
        GROUP_CONCAT(t.name) as tag_names
      FROM images i
      LEFT JOIN image_tags it ON i.id = it.image_id
      LEFT JOIN tags t ON it.tag_id = t.id
      WHERE i.category_id = ?
      GROUP BY i.id
    `;
    
    const rows = db.prepare(query).all(categoryId) as any[];
    
    const images = rows.map(row => {
      // 如果有标签，需要单独查询完整的标签信息
      let tags: any[] = [];
      if (row.tag_ids) {
        const tagIds = row.tag_ids.split(',');
        const placeholders = tagIds.map(() => '?').join(',');
        const tagQuery = `SELECT * FROM tags WHERE id IN (${placeholders})`;
        tags = db.prepare(tagQuery).all(...tagIds);
      }
      
      return {
        ...row,
        file_metadata: JSON.parse(row.file_metadata || '{}'),
        exif_info: row.exif_info ? JSON.parse(row.exif_info) : null,
        tags
      };
    });
    
    return images;
  }

  async getImageById(imageId: string): Promise<ImageRead | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const query = `
      SELECT 
        i.*,
        GROUP_CONCAT(t.id) as tag_ids,
        GROUP_CONCAT(t.name) as tag_names
      FROM images i
      LEFT JOIN image_tags it ON i.id = it.image_id
      LEFT JOIN tags t ON it.tag_id = t.id
      WHERE i.id = ?
      GROUP BY i.id
    `;
    
    const row = db.prepare(query).get(imageId) as any;
    
    if (!row) {
      return null;
    }
    
    // 如果有标签，需要单独查询完整的标签信息
    let tags: any[] = [];
    if (row.tag_ids) {
      const tagIds = row.tag_ids.split(',');
      const placeholders = tagIds.map(() => '?').join(',');
      const tagQuery = `SELECT * FROM tags WHERE id IN (${placeholders})`;
      tags = db.prepare(tagQuery).all(...tagIds);
    }
    
    return {
      ...row,
      file_metadata: JSON.parse(row.file_metadata || '{}'),
      exif_info: row.exif_info ? JSON.parse(row.exif_info) : null,
      tags
    };
  }

  async uploadImage(
    categoryId: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimeType: string,
    setAsCategoryThumbnail: boolean = false,
    thumbnailOptions?: {
      createThumbnails?: boolean;
      compressionQuality?: number;
      width?: number;
      height?: number;
    }
  ): Promise<ImageRead> {
    const db = this.dbManager.getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();
    const storageType = this.getStorageType();
    
    // 生成文件名
    const fileExtension = path.extname(originalFilename);
    const storedFilename = `${id}${fileExtension}`;
    const thumbnailFilename = `${id}_thumb${fileExtension}`;
    
    try {
      let imagePath: string;
      let thumbnailPath: string;
      let thumbnailBuffer: Buffer;
      
      if (storageType === 'oss') {
        // OSS存储模式
        if (!this.ossService.isConfigured()) {
          throw new Error('OSS未配置');
        }
        
        // 生成缩略图（在内存中）
        if (thumbnailOptions?.createThumbnails !== false) {
          thumbnailBuffer = await this.generateThumbnailBuffer(fileBuffer, thumbnailOptions);
        } else {
          // 如果禁用缩略图生成，使用原图
          thumbnailBuffer = fileBuffer;
        }
        
        // 上传原图到OSS
        const imageOSSPath = this.generateOSSPath(categoryId, storedFilename, false);
        const imageUploadResult = await this.ossService.uploadFile(imageOSSPath, fileBuffer, mimeType);
        if (!imageUploadResult.success) {
          throw new Error(`上传原图失败: ${imageUploadResult.message}`);
        }
        
        // 上传缩略图到OSS
        const thumbnailOSSPath = this.generateOSSPath(categoryId, thumbnailFilename, true);
        const thumbnailUploadResult = await this.ossService.uploadFile(thumbnailOSSPath, thumbnailBuffer, mimeType);
        if (!thumbnailUploadResult.success) {
          // 清理已上传的原图
          await this.ossService.deleteFile(imageOSSPath);
          throw new Error(`上传缩略图失败: ${thumbnailUploadResult.message}`);
        }
        
        // OSS路径用于数据库存储
        imagePath = imageOSSPath;
        thumbnailPath = thumbnailOSSPath;
        
      } else {
        // 本地存储模式（原有逻辑）
        this.ensureCategoryDirectories(categoryId);
        
        const categoryImageDir = this.getCategoryImagePath(categoryId);
        imagePath = path.join(categoryImageDir, storedFilename);
        
        const categoryThumbnailDir = this.getCategoryThumbnailPath(categoryId);
        thumbnailPath = path.join(categoryThumbnailDir, thumbnailFilename);
        
        // 保存原图文件
        fs.writeFileSync(imagePath, fileBuffer);
        
        // 使用Jimp生成高质量缩略图
        if (thumbnailOptions?.createThumbnails !== false) {
          await this.generateThumbnail(imagePath, thumbnailPath, thumbnailOptions);
        } else {
          // 如果禁用缩略图生成，复制原图
          fs.copyFileSync(imagePath, thumbnailPath);
        }
        
      }
      
      // 提取EXIF信息（异步，但不阻塞主流程）
      let exifInfo = null;
      if (ExifService.isSupportedFormat(mimeType)) {
        try {
          console.debug('📷 开始提取EXIF信息...', { mimeType, fileSize: fileBuffer.length });
          exifInfo = await this.exifService.extractExifFromBuffer(fileBuffer);
          
          if (exifInfo) {
            const stats = ExifService.getExifStats(exifInfo);
            console.log('✅ EXIF提取成功', {
              fieldsCount: stats.fieldsCount,
              mainFields: stats.mainFields,
              filename: originalFilename
            });
          } else {
            console.debug('📷 图片不包含EXIF信息或提取为空', { filename: originalFilename });
          }
        } catch (error) {
          // EXIF提取失败不影响图片上传
          console.warn('⚠️ EXIF提取失败，继续图片上传流程', {
            filename: originalFilename,
            error: error instanceof Error ? error.message : String(error)
          });
          exifInfo = null;
        }
      } else {
        console.debug('📷 文件格式不支持EXIF提取', { mimeType, filename: originalFilename });
      }
      
      const image: ImageRead = {
        id,
        category_id: categoryId,
        title: path.basename(originalFilename, fileExtension),
        original_filename: originalFilename,
        stored_filename: storedFilename,
        relative_file_path: storageType === 'oss' ? imagePath : this.getRelativeImagePath(categoryId, storedFilename),
        relative_thumbnail_path: storageType === 'oss' ? thumbnailPath : this.getRelativeThumbnailPath(categoryId, thumbnailFilename),
        mime_type: mimeType,
        size_bytes: fileBuffer.length,
        description: null,
        created_at: now,
        updated_at: now,
        file_metadata: {
          width: null, // 后续可以添加图片尺寸检测
          height: null,
          format: fileExtension.replace('.', '')
        },
        // 添加Schema要求的字段，使用electron://协议
        image_url: `electron://file/${storedFilename}`,
        thumbnail_url: `electron://thumbnail/${thumbnailFilename}`,
        exif_info: exifInfo, // 提取的EXIF信息或null
        tags: [] // 空数组，符合Schema可选要求
      };
      
      // 添加到数据库
      const dbSqlite = db as Database.Database;
      const stmt = dbSqlite.prepare(`
        INSERT INTO images (
          id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url,
          thumbnail_url, exif_info
        ) VALUES (
          @id, @category_id, @title, @original_filename, @stored_filename,
          @relative_file_path, @relative_thumbnail_path, @mime_type, @size_bytes,
          @description, @created_at, @updated_at, @file_metadata, @image_url,
          @thumbnail_url, @exif_info
        )
      `);
      
      stmt.run({
        ...image,
        file_metadata: JSON.stringify(image.file_metadata),
        exif_info: image.exif_info ? JSON.stringify(image.exif_info) : null
      });
      
      
      // 如果需要设置为分类缩略图
      if (setAsCategoryThumbnail) {
        await this.setCategoryThumbnail(categoryId, id);
      }
      
      return image;
    } catch (error) {
      // 清理已创建的文件（本地存储模式）
      if (storageType === 'local') {
        const categoryImageDir = this.getCategoryImagePath(categoryId);
        const imagePath = path.join(categoryImageDir, storedFilename);
        const categoryThumbnailDir = this.getCategoryThumbnailPath(categoryId);
        const thumbnailPath = path.join(categoryThumbnailDir, thumbnailFilename);
        
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath);
        }
      }
      throw error;
    }
  }

  async updateImage(imageId: string, imageData: ImageUpdate): Promise<ImageRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 获取现有图片
    const existingImage = await this.getImageById(imageId);
    if (!existingImage) {
      throw new Error(`图片不存在: ${imageId}`);
    }
    
    const now = new Date().toISOString();
    
    // 处理标签更新
    if ('tags' in imageData && typeof imageData.tags === 'string') {
      await this.updateImageTags(imageId, imageData.tags);
    }
    
    // 处理缩略图设置
    if ('set_as_category_thumbnail' in imageData && imageData.set_as_category_thumbnail) {
      await this.setCategoryThumbnail(existingImage.category_id, imageId);
    }
    
    // 创建更新后的图片数据（只更新基本字段）
    let processedImageData = { ...imageData };
    
    // 移除不属于图片基本信息的字段
    delete processedImageData.tags;
    delete processedImageData.set_as_category_thumbnail;
    
    // 更新图片数据
    const updateFields = [];
    const updateValues: any = { updated_at: now, id: imageId };
    
    if (processedImageData.title !== undefined) {
      updateFields.push('title = @title');
      updateValues.title = processedImageData.title;
    }
    if (processedImageData.description !== undefined) {
      updateFields.push('description = @description');
      updateValues.description = processedImageData.description;
    }
    
    if (updateFields.length > 0) {
      updateFields.push('updated_at = @updated_at');
      const updateQuery = `UPDATE images SET ${updateFields.join(', ')} WHERE id = @id`;
      db.prepare(updateQuery).run(updateValues);
    }
    
    // 返回更新后的完整图片数据
    const updatedImage = await this.getImageById(imageId);
    if (!updatedImage) {
      throw new Error('更新图片后无法获取');
    }
    
    return updatedImage;
  }

  // 更新图片标签
  private async updateImageTags(imageId: string, tagsString: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 移除现有的图片标签关联
    db.prepare('DELETE FROM image_tags WHERE image_id = ?').run(imageId);
    
    // 解析并去重标签字符串
    const tagNames = tagsString
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    
    // 使用Set去重（不区分大小写）
    const uniqueTags = new Set<string>();
    const deduplicatedTags: string[] = [];
    
    for (const tagName of tagNames) {
      const lowerTagName = tagName.toLowerCase();
      if (!uniqueTags.has(lowerTagName)) {
        uniqueTags.add(lowerTagName);
        deduplicatedTags.push(tagName);
      }
    }
    
    console.log(`📋 标签处理: 原始${tagNames.length}个，去重后${deduplicatedTags.length}个`);
    
    // 处理去重后的标签
    for (const tagName of deduplicatedTags) {
      try {
        // 查找或创建标签
        let tag = db.prepare('SELECT * FROM tags WHERE LOWER(name) = LOWER(?)').get(tagName) as any;
        
        if (!tag) {
          // 创建新标签
          const tagId = uuidv4();
          const now = new Date().toISOString();
          db.prepare('INSERT INTO tags (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)')
            .run(tagId, tagName, now, now);
          tag = { id: tagId };
          console.log(`✨ 创建新标签: ${tagName}`);
        }
        
        // 检查是否已存在关联（额外的安全检查）
        const existingAssoc = db.prepare('SELECT 1 FROM image_tags WHERE image_id = ? AND tag_id = ?')
          .get(imageId, tag.id);
        
        if (!existingAssoc) {
          // 添加图片-标签关联
          db.prepare('INSERT INTO image_tags (image_id, tag_id) VALUES (?, ?)')
            .run(imageId, tag.id);
          console.log(`🔗 添加标签关联: ${tagName}`);
        } else {
          console.log(`⚠️ 标签关联已存在，跳过: ${tagName}`);
        }
      } catch (error) {
        console.error(`❌ 处理标签失败: ${tagName}`, error);
        // 继续处理其他标签，不中断整个流程
      }
    }
  }

  // 获取图片的标签对象数组
  private async getImageTagObjects(imageId: string): Promise<any[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const query = `
      SELECT t.*
      FROM tags t
      JOIN image_tags it ON t.id = it.tag_id
      WHERE it.image_id = ?
    `;
    
    const tags = db.prepare(query).all(imageId);
    return tags;
  }

  // 设置分类缩略图
  private async setCategoryThumbnail(categoryId: string, imageId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 查找分类
    const category = db.prepare('SELECT * FROM categories WHERE id = ?').get(categoryId) as any;
    if (!category) {
      throw new Error(`分类不存在: ${categoryId}`);
    }
    
    // 查找图片
    const image = db.prepare('SELECT * FROM images WHERE id = ?').get(imageId) as any;
    if (!image) {
      throw new Error(`图片不存在: ${imageId}`);
    }
    
    // 更新分类的缩略图
    const now = new Date().toISOString();
    db.prepare(`
      UPDATE categories 
      SET thumbnail_path = ?, thumbnail_url = ?, updated_at = ?
      WHERE id = ?
    `).run(image.relative_thumbnail_path, image.thumbnail_url, now, categoryId);
    
  }

  async deleteImage(imageId: string): Promise<DeleteResult> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const storageType = this.getStorageType();


    // 验证删除条件
    const validation = await this.validateDeleteConditions(imageId);
    if (!validation.valid) {
      const error = `删除验证失败: ${validation.errors.join('; ')}`;
      console.error(`❌ ${error}`);
      this.logDeleteAudit(imageId, 'DELETE_FAILED', { reason: 'validation_failed', errors: validation.errors });
      return {
        success: false,
        imageId,
        error,
        storageDeleted: false,
        databaseDeleted: false,
        details: { storageType }
      };
    }

    const image = validation.image!;

    // 记录删除开始
    this.logDeleteAudit(imageId, 'DELETE_START', {
      imageTitle: image.title,
      categoryId: image.category_id,
      storageType,
      hasStoredFile: !!image.stored_filename,
      validationWarnings: validation.warnings
    });

    let storageDeleted = false;
    let storageError: string | undefined;
    let imagePath: string | undefined;
    let thumbnailPath: string | undefined;

    // 删除文件
    if (image.stored_filename) {
      try {
        if (storageType === 'oss') {
          // OSS存储模式
          if (this.ossService.isConfigured()) {
            // 删除原图
            const imageOSSPath = this.generateOSSPath(image.category_id, image.stored_filename, false);
            imagePath = imageOSSPath;
            await this.ossService.deleteFile(imageOSSPath);

            // 删除缩略图
            const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
            const thumbnailOSSPath = this.generateOSSPath(image.category_id, thumbnailFilename, true);
            thumbnailPath = thumbnailOSSPath;
            await this.ossService.deleteFile(thumbnailOSSPath);

            storageDeleted = true;
          } else {
            storageError = 'OSS未配置，无法删除存储文件';
          }
        } else {
          // 本地存储模式
          imagePath = this.getImagePath(image.stored_filename);
          const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
          thumbnailPath = this.getThumbnailPath(thumbnailFilename);

          let localFilesDeleted = 0;
          let totalLocalFiles = 0;
          let firstError: string | null = null;

          if (fs.existsSync(imagePath)) {
            totalLocalFiles++;
            try {
              fs.unlinkSync(imagePath);
              localFilesDeleted++;
            } catch (error) {
              const errorMsg = error instanceof Error ? error.message : String(error);
              console.error('❌ 删除原图文件失败:', error);
              if (!firstError) firstError = errorMsg;
            }
          }

          if (fs.existsSync(thumbnailPath)) {
            totalLocalFiles++;
            try {
              fs.unlinkSync(thumbnailPath);
              localFilesDeleted++;
            } catch (error) {
              const errorMsg = error instanceof Error ? error.message : String(error);
              console.error('❌ 删除缩略图文件失败:', error);
              if (!firstError) firstError = errorMsg;
            }
          }

          storageDeleted = localFilesDeleted === totalLocalFiles && totalLocalFiles > 0;
          if (!storageDeleted && totalLocalFiles > 0) {
            storageError = firstError || `本地文件删除不完整: ${localFilesDeleted}/${totalLocalFiles}`;
          }

        }
      } catch (error) {
        storageError = error instanceof Error ? error.message : String(error);
        console.error('❌ 删除存储文件失败:', error);
      }
    } else {
      storageDeleted = true; // 没有文件需要删除，视为成功
    }

    // 删除数据库记录
    let databaseDeleted = false;
    let databaseError: string | undefined;

    try {
      const deleteTransaction = db.transaction(() => {
        // 删除图片记录（外键约束会自动删除image_tags关联）
        const result = db.prepare('DELETE FROM images WHERE id = ?').run(imageId);
        if (result.changes === 0) {
          throw new Error('数据库中没有找到要删除的图片记录');
        }
      });

      deleteTransaction();
      databaseDeleted = true;
    } catch (error) {
      databaseError = error instanceof Error ? error.message : String(error);
      console.error('❌ 从数据库删除图片失败:', error);
    }

    const success = databaseDeleted && (storageDeleted || !image.stored_filename);
    const combinedError = [storageError, databaseError].filter(Boolean).join('; ');

    const result: DeleteResult = {
      success,
      imageId,
      error: combinedError || undefined,
      storageDeleted,
      databaseDeleted,
      details: {
        imagePath,
        thumbnailPath,
        storageType
      }
    };

    if (success) {
      this.logDeleteAudit(imageId, 'DELETE_SUCCESS', {
        imageTitle: image.title,
        storageDeleted,
        databaseDeleted,
        details: result.details
      });
    } else {
      console.error(`❌ 图片删除失败: ${image.title} (${imageId}), 错误: ${combinedError}`);
      this.logDeleteAudit(imageId, 'DELETE_FAILED', {
        imageTitle: image.title,
        error: combinedError,
        storageDeleted,
        databaseDeleted,
        details: result.details
      });
    }

    return result;
  }

  /**
   * 批量删除图片
   */
  async deleteImages(imageIds: string[]): Promise<BatchDeleteResult> {

    if (imageIds.length === 0) {
      return {
        totalCount: 0,
        successCount: 0,
        failedCount: 0,
        results: [],
        errors: []
      };
    }

    const results: DeleteResult[] = [];
    const errors: string[] = [];
    let successCount = 0;
    let failedCount = 0;

    // 验证所有图片是否存在
    const validImageIds: string[] = [];
    for (const imageId of imageIds) {
      try {
        const image = await this.getImageById(imageId);
        if (image) {
          validImageIds.push(imageId);
        } else {
          const error = `图片不存在: ${imageId}`;
          errors.push(error);
          results.push({
            success: false,
            imageId,
            error,
            storageDeleted: false,
            databaseDeleted: false,
            details: { storageType: this.getStorageType() }
          });
          failedCount++;
        }
      } catch (error) {
        const errorMsg = `验证图片失败 ${imageId}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        results.push({
          success: false,
          imageId,
          error: errorMsg,
          storageDeleted: false,
          databaseDeleted: false,
          details: { storageType: this.getStorageType() }
        });
        failedCount++;
      }
    }


    // 按存储类型分组处理
    const storageType = this.getStorageType();

    if (storageType === 'oss') {
      // OSS批量删除
      await this.batchDeleteFromOSS(validImageIds, results, errors);
    } else {
      // 本地存储批量删除
      await this.batchDeleteFromLocal(validImageIds, results, errors);
    }

    // 批量删除数据库记录
    await this.batchDeleteFromDatabase(validImageIds, results, errors);

    // 统计最终结果
    successCount = 0;
    failedCount = 0;
    for (const result of results) {
      if (result.success) {
        successCount++;
      } else {
        failedCount++;
      }
    }

    const batchResult: BatchDeleteResult = {
      totalCount: imageIds.length,
      successCount,
      failedCount,
      results,
      errors
    };


    return batchResult;
  }

  /**
   * 从OSS批量删除文件
   */
  private async batchDeleteFromOSS(imageIds: string[], results: DeleteResult[], errors: string[]): Promise<void> {
    if (!this.ossService.isConfigured()) {
      const error = 'OSS未配置，无法删除存储文件';

      for (const imageId of imageIds) {
        const existingResult = results.find(r => r.imageId === imageId);
        if (existingResult) {
          existingResult.error = existingResult.error ? `${existingResult.error}; ${error}` : error;
        }
      }
      return;
    }


    for (const imageId of imageIds) {
      try {
        const image = await this.getImageById(imageId);
        if (!image || !image.stored_filename) continue;

        // 删除原图
        const imageOSSPath = this.generateOSSPath(image.category_id, image.stored_filename, false);
        await this.ossService.deleteFile(imageOSSPath);

        // 删除缩略图
        const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
        const thumbnailOSSPath = this.generateOSSPath(image.category_id, thumbnailFilename, true);
        await this.ossService.deleteFile(thumbnailOSSPath);

        // 更新结果
        let result = results.find(r => r.imageId === imageId);
        if (!result) {
          result = {
            success: false,
            imageId,
            storageDeleted: false,
            databaseDeleted: false,
            details: { storageType: 'oss' }
          };
          results.push(result);
        }

        result.storageDeleted = true;
        result.details!.imagePath = imageOSSPath;
        result.details!.thumbnailPath = thumbnailOSSPath;

      } catch (error) {
        const errorMsg = `OSS删除失败 ${imageId}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);

        let result = results.find(r => r.imageId === imageId);
        if (!result) {
          result = {
            success: false,
            imageId,
            storageDeleted: false,
            databaseDeleted: false,
            details: { storageType: 'oss' }
          };
          results.push(result);
        }

        result.error = result.error ? `${result.error}; ${errorMsg}` : errorMsg;
        console.error(`❌ ${errorMsg}`);
      }
    }
  }

  /**
   * 从本地存储批量删除文件
   */
  private async batchDeleteFromLocal(imageIds: string[], results: DeleteResult[], errors: string[]): Promise<void> {

    for (const imageId of imageIds) {
      try {
        const image = await this.getImageById(imageId);
        if (!image || !image.stored_filename) continue;

        const imagePath = this.getImagePath(image.stored_filename);
        const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
        const thumbnailPath = this.getThumbnailPath(thumbnailFilename);

        let localFilesDeleted = 0;
        let totalLocalFiles = 0;
        const deleteErrors: string[] = [];

        // 删除原图
        if (fs.existsSync(imagePath)) {
          totalLocalFiles++;
          try {
            fs.unlinkSync(imagePath);
            localFilesDeleted++;
          } catch (error) {
            deleteErrors.push(`删除原图失败: ${error instanceof Error ? error.message : String(error)}`);
          }
        }

        // 删除缩略图
        if (fs.existsSync(thumbnailPath)) {
          totalLocalFiles++;
          try {
            fs.unlinkSync(thumbnailPath);
            localFilesDeleted++;
          } catch (error) {
            deleteErrors.push(`删除缩略图失败: ${error instanceof Error ? error.message : String(error)}`);
          }
        }

        // 更新结果
        let result = results.find(r => r.imageId === imageId);
        if (!result) {
          result = {
            success: false,
            imageId,
            storageDeleted: false,
            databaseDeleted: false,
            details: { storageType: 'local' }
          };
          results.push(result);
        }

        result.storageDeleted = localFilesDeleted === totalLocalFiles && totalLocalFiles > 0;
        result.details!.imagePath = imagePath;
        result.details!.thumbnailPath = thumbnailPath;

        if (deleteErrors.length > 0) {
          const errorMsg = deleteErrors.join('; ');
          result.error = result.error ? `${result.error}; ${errorMsg}` : errorMsg;
          errors.push(`本地删除部分失败 ${imageId}: ${errorMsg}`);
        }

      } catch (error) {
        const errorMsg = `本地删除失败 ${imageId}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);

        let result = results.find(r => r.imageId === imageId);
        if (!result) {
          result = {
            success: false,
            imageId,
            storageDeleted: false,
            databaseDeleted: false,
            details: { storageType: 'local' }
          };
          results.push(result);
        }

        result.error = result.error ? `${result.error}; ${errorMsg}` : errorMsg;
        console.error(`❌ ${errorMsg}`);
      }
    }
  }

  /**
   * 从数据库批量删除记录
   */
  private async batchDeleteFromDatabase(imageIds: string[], results: DeleteResult[], errors: string[]): Promise<void> {
    if (imageIds.length === 0) return;


    const db = this.dbManager.getDatabase() as Database.Database;

    try {
      const batchDeleteTransaction = db.transaction(() => {
        const deleteStmt = db.prepare('DELETE FROM images WHERE id = ?');

        for (const imageId of imageIds) {
          try {
            const result = deleteStmt.run(imageId);

            let deleteResult = results.find(r => r.imageId === imageId);
            if (!deleteResult) {
              deleteResult = {
                success: false,
                imageId,
                storageDeleted: true, // 假设存储删除成功，如果没有存储文件
                databaseDeleted: false,
                details: { storageType: this.getStorageType() }
              };
              results.push(deleteResult);
            }

            if (result.changes > 0) {
              deleteResult.databaseDeleted = true;
              deleteResult.success = deleteResult.storageDeleted && deleteResult.databaseDeleted;
            } else {
              const errorMsg = `数据库中没有找到图片记录: ${imageId}`;
              deleteResult.error = deleteResult.error ? `${deleteResult.error}; ${errorMsg}` : errorMsg;
              errors.push(errorMsg);
            }
          } catch (error) {
            const errorMsg = `数据库删除失败 ${imageId}: ${error instanceof Error ? error.message : String(error)}`;
            errors.push(errorMsg);

            let deleteResult = results.find(r => r.imageId === imageId);
            if (!deleteResult) {
              deleteResult = {
                success: false,
                imageId,
                storageDeleted: false,
                databaseDeleted: false,
                details: { storageType: this.getStorageType() }
              };
              results.push(deleteResult);
            }

            deleteResult.error = deleteResult.error ? `${deleteResult.error}; ${errorMsg}` : errorMsg;
            console.error(`❌ ${errorMsg}`);
          }
        }
      });

      batchDeleteTransaction();
    } catch (error) {
      const errorMsg = `数据库批量删除事务失败: ${error instanceof Error ? error.message : String(error)}`;
      errors.push(errorMsg);
      console.error(`❌ ${errorMsg}`);

      // 标记所有图片的数据库删除失败
      for (const imageId of imageIds) {
        let result = results.find(r => r.imageId === imageId);
        if (!result) {
          result = {
            success: false,
            imageId,
            storageDeleted: false,
            databaseDeleted: false,
            details: { storageType: this.getStorageType() }
          };
          results.push(result);
        }

        result.databaseDeleted = false;
        result.success = false;
        result.error = result.error ? `${result.error}; ${errorMsg}` : errorMsg;
      }
    }
  }

  /**
   * 获取相对图片路径（用于数据库存储）
   */
  private getRelativeImagePath(categoryId: string, filename: string): string {
    const categoryName = this.getCategoryName(categoryId);
    return path.join(categoryName, filename).replace(/\\/g, '/');
  }

  /**
   * 获取相对缩略图路径（用于数据库存储）
   */
  private getRelativeThumbnailPath(categoryId: string, filename: string): string {
    const categoryName = this.getCategoryName(categoryId);
    return path.join(categoryName, 'thumbnails', filename).replace(/\\/g, '/');
  }

  // 获取图片文件的绝对路径或OSS路径
  getImagePath(filename: string): string {
    const storageType = this.getStorageType();
    
    if (storageType === 'oss') {
      // OSS存储模式，从数据库中获取完整的OSS路径
      try {
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id, relative_file_path FROM images WHERE stored_filename = ?').get(filename) as any;
        if (image && image.relative_file_path) {
          return image.relative_file_path; // 返回OSS路径
        }
      } catch (error) {
        console.error('❌ 查找OSS图片路径失败:', error);
      }
      
      // 如果查找不到，构造默认OSS路径
      return `unknown-category/images/${filename}`;
    } else {
      // 本地存储模式（原有逻辑）
      try {
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id FROM images WHERE stored_filename = ?').get(filename) as any;
        if (image) {
          return path.join(this.getCategoryImagePath(image.category_id), filename);
        }
      } catch (error) {
        console.error('❌ 查找图片路径失败:', error);
      }
      
      // 如果查找不到，返回默认路径
      const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.imagesDir;
      return path.join(basePath, 'unknown-category', 'images', filename);
    }
  }

  // 获取缩略图文件的绝对路径或OSS路径
  getThumbnailPath(filename: string): string {
    const storageType = this.getStorageType();
    
    if (storageType === 'oss') {
      // OSS存储模式，从数据库中获取完整的OSS路径
      try {
        const originalFilename = filename.replace(/_thumb/, '');
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id, relative_thumbnail_path FROM images WHERE stored_filename = ?').get(originalFilename) as any;
        if (image && image.relative_thumbnail_path) {
          return image.relative_thumbnail_path; // 返回OSS路径
        }
      } catch (error) {
        console.error('❌ 查找OSS缩略图路径失败:', error);
      }
      
      // 如果查找不到，构造默认OSS路径
      return `unknown-category/thumbnails/${filename}`;
    } else {
      // 本地存储模式（原有逻辑）
      try {
        const originalFilename = filename.replace(/_thumb/, '');
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id FROM images WHERE stored_filename = ?').get(originalFilename) as any;
        if (image) {
          return path.join(this.getCategoryThumbnailPath(image.category_id), filename);
        }
      } catch (error) {
        console.error('❌ 查找缩略图路径失败:', error);
      }
      
      // 如果查找不到，返回默认路径
      const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.thumbnailsDir;
      return path.join(basePath, 'unknown-category', 'thumbnails', filename);
    }
  }

  // 检查图片文件是否存在
  imageExists(filename: string): boolean {
    return fs.existsSync(this.getImagePath(filename));
  }

  // 检查缩略图是否存在
  thumbnailExists(filename: string): boolean {
    return fs.existsSync(this.getThumbnailPath(filename));
  }

  // 真正的缩略图生成方法（使用ThumbnailService）
  private async generateThumbnail(
    imagePath: string,
    thumbnailPath: string,
    customOptions?: {
      compressionQuality?: number;
      width?: number;
      height?: number;
    }
  ): Promise<void> {
    console.log(`🔄 开始生成缩略图: ${imagePath} -> ${thumbnailPath}`);
    console.log(`📋 缩略图选项:`, customOptions);

    // 确保缩略图目录存在
    const thumbnailDir = path.dirname(thumbnailPath);
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
      console.log(`📁 创建缩略图目录: ${thumbnailDir}`);
    }

    // 使用ThumbnailService生成真正的压缩缩略图
    const options = this.getThumbnailOptions(customOptions);
    console.log(`⚙️ 最终缩略图选项:`, options);

    const result = await this.thumbnailService.generateFromFile(imagePath, thumbnailPath, options);

    console.log(`✅ 缩略图生成成功: ${imagePath}`, {
      originalSize: result.originalSize,
      thumbnailSize: result.thumbnailSize,
      compressionRatio: (result.compressionRatio * 100).toFixed(1) + '%',
      processingTime: result.processingTime + 'ms',
      format: result.format
    });
  }

  // 在内存中生成缩略图Buffer（用于OSS上传）
  private async generateThumbnailBuffer(
    imageBuffer: Buffer,
    customOptions?: {
      compressionQuality?: number;
      width?: number;
      height?: number;
    }
  ): Promise<Buffer> {
    console.log(`🔄 开始生成内存缩略图, Buffer大小: ${imageBuffer.length} bytes`);
    console.log(`📋 缩略图选项:`, customOptions);

    // 使用ThumbnailService生成真正的压缩缩略图
    const options = this.getThumbnailOptions(customOptions);
    console.log(`⚙️ 最终缩略图选项:`, options);

    const result = await this.thumbnailService.generateFromBuffer(imageBuffer, options);

    console.log(`✅ 内存缩略图生成成功`, {
      originalSize: result.originalSize,
      thumbnailSize: result.thumbnailSize,
      compressionRatio: (result.compressionRatio * 100).toFixed(1) + '%',
      processingTime: result.processingTime + 'ms',
      format: result.format
    });

    return result.buffer;
  }

  /**
   * 迁移存储位置 - 将现有图片从旧位置移动到新位置
   */
  async migrateStorageLocation(newStoragePath: string): Promise<{success: boolean, message: string, details?: any}> {
    try {
      
      if (!this.settingsService) {
        throw new Error('设置服务未初始化');
      }

      const currentSettings = this.settingsService.getSettings();
      const oldStoragePath = currentSettings.storagePath;
      
      // 检查是否需要迁移
      const unifiedImagesDir = path.join(oldStoragePath, 'images');
      const hasUnifiedStructure = fs.existsSync(unifiedImagesDir);
      
      // 如果路径相同且不需要结构迁移，跳过
      if (oldStoragePath === newStoragePath && !hasUnifiedStructure) {
        return {
          success: true,
          message: '源路径和目标路径相同，且文件结构已正确'
        };
      }

      // 如果是结构迁移（统一存储 -> 分类文件夹），继续执行迁移逻辑

      // 获取所有图片记录
      const db = this.dbManager.getDatabase() as Database.Database;
      const allImages = db.prepare('SELECT * FROM images').all() as any[];
      const allCategories = db.prepare('SELECT id, name FROM categories').all() as any[];
      

      if (allImages.length === 0) {
        return {
          success: true,
          message: '没有图片需要迁移'
        };
      }

      // 统计信息
      let successCount = 0;
      let failedCount = 0;
      const failedFiles: string[] = [];
      const migratedFiles: {category: string, files: number}[] = [];

      // 按分类分组迁移
      const categoryMap = new Map();
      allCategories.forEach(cat => categoryMap.set(cat.id, cat.name));

      // 为每个分类创建目标目录
      for (const category of allCategories) {
        const categoryName = this.sanitizeFolderName(category.name);
        const targetImageDir = path.join(newStoragePath, categoryName);
        const targetThumbnailDir = path.join(newStoragePath, categoryName, 'thumbnails');
        
        if (!fs.existsSync(targetImageDir)) {
          fs.mkdirSync(targetImageDir, { recursive: true });
        }
        if (!fs.existsSync(targetThumbnailDir)) {
          fs.mkdirSync(targetThumbnailDir, { recursive: true });
        }
        
      }

      // 迁移图片文件
      for (const image of allImages) {
        try {
          const categoryName = categoryMap.get(image.category_id);
          if (!categoryName) {
            failedCount++;
            failedFiles.push(`${image.stored_filename} (分类不存在)`);
            continue;
          }

          const sanitizedCategoryName = this.sanitizeFolderName(categoryName);
          
          // 查找旧文件路径
          const oldImagePath = path.join(oldStoragePath, sanitizedCategoryName, image.stored_filename);
          const thumbFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
          const oldThumbnailPath = path.join(oldStoragePath, sanitizedCategoryName, 'thumbnails', thumbFilename);

          // 新文件路径
          const newImagePath = path.join(newStoragePath, sanitizedCategoryName, image.stored_filename);
          const newThumbnailPath = path.join(newStoragePath, sanitizedCategoryName, 'thumbnails', thumbFilename);

          // 移动原图
          if (fs.existsSync(oldImagePath)) {
            fs.copyFileSync(oldImagePath, newImagePath);
            fs.unlinkSync(oldImagePath);
          } else {
          }

          // 移动缩略图
          if (fs.existsSync(oldThumbnailPath)) {
            fs.copyFileSync(oldThumbnailPath, newThumbnailPath);
            fs.unlinkSync(oldThumbnailPath);
          } else {
          }

          // 更新数据库中的路径信息
          const newRelativeImagePath = path.join(sanitizedCategoryName, image.stored_filename).replace(/\\/g, '/');
          const newRelativeThumbnailPath = path.join(sanitizedCategoryName, 'thumbnails', thumbFilename).replace(/\\/g, '/');
          
          db.prepare(`
            UPDATE images 
            SET relative_file_path = ?, relative_thumbnail_path = ?, updated_at = ?
            WHERE id = ?
          `).run(newRelativeImagePath, newRelativeThumbnailPath, new Date().toISOString(), image.id);

          successCount++;
          
          // 统计分类迁移情况
          let categoryStats = migratedFiles.find(item => item.category === categoryName);
          if (!categoryStats) {
            categoryStats = { category: categoryName, files: 0 };
            migratedFiles.push(categoryStats);
          }
          categoryStats.files++;
          
        } catch (error) {
          console.error(`❌ 迁移图片 ${image.stored_filename} 失败:`, error);
          failedCount++;
          failedFiles.push(`${image.stored_filename} (${error instanceof Error ? error.message : String(error)})`);
        }
      }

      // 清理旧的空目录
      try {
        for (const category of allCategories) {
          const categoryName = this.sanitizeFolderName(category.name);
          const oldCategoryDir = path.join(oldStoragePath, categoryName);
          const oldThumbnailDir = path.join(oldCategoryDir, 'thumbnails');
          
          // 删除空的缩略图目录
          if (fs.existsSync(oldThumbnailDir) && fs.readdirSync(oldThumbnailDir).length === 0) {
            fs.rmdirSync(oldThumbnailDir);
          }
          
          // 如果分类目录为空，删除它
          if (fs.existsSync(oldCategoryDir) && fs.readdirSync(oldCategoryDir).length === 0) {
            fs.rmdirSync(oldCategoryDir);
          }
        }
      } catch (error) {
      }

      const result = {
        success: failedCount === 0,
        message: `迁移完成: 成功 ${successCount} 张，失败 ${failedCount} 张`,
        details: {
          total: allImages.length,
          success: successCount,
          failed: failedCount,
          failedFiles,
          migratedCategories: migratedFiles
        }
      };

      return result;
      
    } catch (error) {
      console.error('❌ 数据迁移失败:', error);
      return {
        success: false,
        message: `迁移失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 获取OSS服务实例（用于外部访问）
   */
  getOSSService(): OSSService {
    return this.ossService;
  }

  /**
   * 重新初始化OSS服务（当配置更改时调用）
   */
  reinitializeOSSService(): void {
    this.initializeOSSService();
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    if (this.ossService) {
      this.ossService.destroy();
    }
  }
}